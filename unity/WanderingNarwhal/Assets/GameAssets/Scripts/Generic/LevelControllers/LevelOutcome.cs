using System;
using System.Collections.Generic;
using GameAssets.Scripts.Match3.Logic;

namespace BBB
{
    public enum LevelOutcome
    {
        Lose = 0,
        Win = 1,
        Exit = 2,
        ExitWithoutPlaying = 3,
        OutOfMoves = 4,

        /// <summary>
        /// Outcome that may rarely happen if shuffle didn't find any possible move.
        /// </summary>
        /// <remarks>
        /// This may happen on some levels with Sand mechanic or Ivy tiles very rarely,
        /// and it is lose condition for the level because there is no possible way to recover from this, only restart.
        /// </remarks>
        ShuffleFailed = 6
    }

    public static class LevelOutcomeExtensions
    {
        private enum LossReason
        {
            None = 0,
            ShuffleFailed = 2
        }

        public static LevelResult GetResult(this LevelOutcome outcome)
        {
            switch (outcome)
            {
                case LevelOutcome.Win:
                    return LevelResult.Win;
                case LevelOutcome.Exit:
                    return LevelResult.Exit;
                case LevelOutcome.ExitWithoutPlaying:
                    return LevelResult.ExitWithoutPlaying;
                default:
                    return LevelResult.Lose;
            }
        }

        public static string GetLossReason(this LevelOutcome outcome, Dictionary<GoalType, float> goalProgress)
        {
            switch (outcome)
            {
                case LevelOutcome.Lose:
                case LevelOutcome.OutOfMoves:
                {
                    GoalType lowestFilledType = GoalType.None;
                    var minValue = float.MaxValue;
                    foreach (var kvp in goalProgress)
                    {
                        if (kvp.Value < minValue)
                        {
                            minValue = kvp.Value;
                            lowestFilledType = kvp.Key;
                        }
                    }
                    return lowestFilledType.ToString();
                }
                case LevelOutcome.ShuffleFailed:
                {
                    return LossReason.ShuffleFailed.ToString();
                }
                default:
                {
                    return LossReason.None.ToString();
                }
            }
        }
    }
}