using System.Collections.Generic;

namespace BBB.UI
{
    public interface IForceWinLosable
    {
        void ForceWin();
        void ForceLose();
        void CollectGoals();
        void GetReplayData();
        void SetOneMove();
        void SetCustomMoves(int moves);
        void ShowOutOfMoves();
        List<string> GetEligibleBoosters();
        void SetCustomBoosterAmount(string boosterUid, int boosterCount);
    }
}