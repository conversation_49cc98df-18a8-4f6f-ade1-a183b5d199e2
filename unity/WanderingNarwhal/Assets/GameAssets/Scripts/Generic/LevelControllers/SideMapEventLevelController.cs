using System;
using BBB.Core.Analytics;
using BBB.Core.Wallet;
using BBB.DI;
using BBB.EndGameEvents;
using BBB.UI.Level;
using BBB.UI.UI.Controllers;
using BBB.Wallet;
using BebopBee.Core.UI;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.Generic;

namespace BBB.UI
{
    public class SideMapEventLevelController : LevelController
    {
        // Currently disabled by UI design
        private const bool ShouldVisualiseWeeklyTournament = false;
        
        private IGameEventResourceManager _gameEventResourceManager;
        private IGameEventManager _gameEventManager;

        public override bool IsEditor => false;
        
        protected override bool CanRetryLevelAfterLose => _gameEventManager.GetCurrentSideMapEvent() is { Status: GameEventStatus.Active };

        protected override ScreenType FallbackScreen
        {
            get
            {
                if (!ConnectivityStatusManager.ConnectivityReachable)
                    return base.FallbackScreen;
                var gameEvent = _gameEventManager.GetCurrentSideMapEvent();
                if (gameEvent is CompetitionGameEvent competitionGameEvent)
                {
                    competitionGameEvent.TryCompleteRelativeEvent();
                    competitionGameEvent.TryProcessSpecialEvent();
                }

                if (gameEvent is not { Status: GameEventStatus.Active })
                    return base.FallbackScreen;

                if (gameEvent is SideMapGameEvent sideMapGameEvent)
                    return sideMapGameEvent.HomeScreen;

                return ScreenType.SideMapScreen;
            }
        }

        protected override void OnContextInitialized(IContext previousContext, IContext context)
        {
            base.OnContextInitialized(previousContext, context);

            _gameEventManager = previousContext.Resolve<IGameEventManager>();
            var gameEvent = _gameEventManager.GetCurrentSideMapEvent();
            EventTitle.text = gameEvent?.NameText;

            _gameEventResourceManager = previousContext.Resolve<IGameEventResourceManager>();
        }

        protected override Type GetBackgroundGetterType(IContext previousContext)
        {
            return typeof(EventBasedBackgroundImageGetter);
        }

        protected override void HandlePostLevelLose()
        {
            if (!CanRetryLevelAfterLose)
            {
                ShowFallbackScreen();
                return;
            }

            var startLevelController = ModalsBuilder.CreateModalView<SideMapStartLevelController>(ModalsType.RoundStart);
            startLevelController.SetupAsRetryLevel(Context.Resolve<GoalViewHelper>(), GoalsSystem, _levelHolder.level,
                OnConfirmedLevelRetry, OnConfirmedLevelExitWithoutRetry);
            startLevelController.ShowModal(ShowMode.Delayed);
        }

        protected override void OnConfirmedLevelExitWithoutRetry()
        {
            ShowFallbackScreen();
        }

        public override void OnShow()
        {
            base.OnShow();

            var gameEventUid = _gameEventManager.GetCurrentSideMapEvent()?.Uid;
            var palette = _gameEventResourceManager.GetSettings(gameEventUid).Palette;
            PaletteApplier.Apply(palette);
        }

        protected override void OnFirstMove()
        {
            GameEventM3ManagerCollection.ForEveryManager(manager => manager.ProcessOnFirstMove());
            ButlerGiftManager.ProcessOnFirstMove();
            SdbManager.ProcessOnFirstMove();
            
            if (WeeklyLeaderboardManager.Status == WeeklyLeaderboardStatus.Active)
            {
                WeeklyLeaderboardManager.IncrementFailedAttempt();
            }
        }

        protected override void OnLevelResultPredicted(LevelResultPredicted ev)
        {
            if (ResultReceived)
                return;

            ResultReceived = true;

            var levelWon = ev.Arg0 == LevelOutcome.Win;

            if (levelWon)
            {
                // enforcing modal preloading to avoid empty frames on board disappearing
                GetLevelSuccessController();
            }
            
            var level = _levelHolder.level;
            
            RoyaleEventMatch3Manager.RestorePenalizableScore();

            if (levelWon)
            {
                TriggerLevelWonEvent(level);

                var transaction = new Transaction()
                    .Earn(level.RewardsDictionary)
                    .SetAnalyticsData(CurrencyFlow.Level.Name, level.LevelUid, level.Stage.ToString())
                    .AddTag(TransactionTag.LevelReward);

                WalletTransactionController.MakeTransaction(transaction);
                
                RaceEventMatch3Manager.ProcessOnLevelWin();
                RoyaleEventMatch3Manager.ProcessOnLevelWin();
                TeamCoopEventMatch3Manager.ProcessOnLevelWin();
                ButlerGiftManager.IncrementStreak();
                CanSendChallenge = ChallengeTriviaManager.ProcessOnLevelWin(level, level.Stage);
                SdbManager.ProcessOnLevelWin(level, level.Stage);

                if (IsStartedLevelPlay)
                {
                    WeeklyLeaderboardManager.DecrementFailedAttempt();
                }
                
                var applyDoubleTrophy = WeeklyLeaderboardManager.IsMultiplierScoreStreakActive();
                var resultWeeklyTrophies = WeeklyLeaderboardManager.TryAddWeeklyTrophy(applyDoubleTrophy ? WeeklyLeaderboardManager.GetScoreMultiplier() : 1);
                if (resultWeeklyTrophies > 0 && WeeklyLeaderboardManager.TrySubmitToWeeklyLeaderboard())
                {
                    if (ShouldVisualiseWeeklyTournament)
                    {
                        var rankedLevelProgressTransaction = new Transaction()
                            .AddTag(TransactionTag.LevelReward)
                            .Earn(InventoryItems.WeeklyTournament, WeeklyLeaderboardManager.LastDeltaTrophies);

                        WalletTransactionController.MakeOnlyVisualTransaction(rankedLevelProgressTransaction);
                    }
                }
                
                ProcessOnLevelWinGameEvents();
                GameEventM3ManagerCollection.ForEveryManager(manager =>
                {
                    if (manager.ActiveGameEvent is CompetitionGameEvent competitionGameEvent)
                        competitionGameEvent.TryCompleteRelativeEvent();
                });

                if (_gameEventManager.GetCurrentSideMapEvent() is SideMapGameEvent
                    {
                        Status: GameEventStatus.Active, GameplayType: GameEventGameplayType.EndOfContent
                    } gameEvent)
                {
                    if (IsStartedLevelPlay)
                    {
                        gameEvent.DecrementFailedAttempt();
                    }

                    int score = gameEvent.IsMultiplierScoreStreakActive() ? gameEvent.GetScoreMultiplier(true) : 1;
                    gameEvent.AddScore(score, true);

                    if (gameEvent.ShouldAutoSubmit())
                    {
                        gameEvent.EventLeaderboard.SubmitCurrentScore(null);
                    }

                    var curUid = InventoryItems.GetGameEventScoreUid(GameEventGameplayType.EndOfContent);

                    var gameEventScoreTransaction = new Transaction()
                        .AddTag(TransactionTag.LevelReward)
                        .SetAnalyticsData(CurrencyFlow.Level.Name, level.LevelUid, level.Stage.ToString())
                        .SetExtraData(gameEvent.Uid)
                        .Earn(curUid, score);
                    WalletTransactionController.MakeOnlyVisualTransaction(gameEventScoreTransaction);
                }

                if (IsStartedLevelPlay)
                {
                    // Restore life, which was silently subtracted after first turn.
                    _livesManager.AddLife(new LivesData
                    {
                        Hidden = true,
                        IsIgnoreCap = IsStartedLevelWithAdditionalLives
                    });
                }

                _playerManager.Player.TryIncrementFirstTryWinsCount();
                _accountManager.Profile.FirstTryWins = _playerManager.Player.FirstTryWinsCount;
                _livesManager.ResetLossStreak();
            }
            else
            {
                if (IsStartedLevelPlay)
                {
                    if (ev.Arg0 == LevelOutcome.ShuffleFailed)
                    {
                        // If level lost due shuffle failed, then restore live anyway (as if player win).
                        // this can only happen if level design is incorrect, so we don't need to take penalty from player. -VK
                        _livesManager.AddLife(new LivesData
                        {
                            Hidden = true,
                            IsIgnoreCap = IsStartedLevelWithAdditionalLives
                        });
                        
                        // Shuffle Failed is not counted as a loss in the streak
                        GameEventM3ManagerCollection.ForEveryManager(manager => manager.ProcessOnExit());
                        RaceEventMatch3Manager.ProcessOnShuffleFailed();
                        ButlerGiftManager.ProcessOnShuffleFailed();
                        SdbManager.ProcessOnShuffleFailed();
                    }
                    else
                    {
                        GameEventM3ManagerCollection.ForEveryManager(manager => manager.ProcessOnLevelLose());
                        RaceEventMatch3Manager.ProcessOnLevelLose();
                        RoyaleEventMatch3Manager.ProcessOnLevelLose();
                        TeamCoopEventMatch3Manager.ProcessOnLevelLose();
                        _playerManager.Player.ResetWinStreak();
                        _livesManager.AddLossToStreak();
                        ButlerGiftManager.ResetStreak();
                        SdbManager.ProcessOnLevelLose(IsStartedLevelPlay);
                        level.LoseLevel(Config);
                    }
                }
                else
                {
                    GameEventM3ManagerCollection.ForEveryManager(manager => manager.ProcessOnExit());
                    RaceEventMatch3Manager.ProcessOnExit(IsStartedLevelPlay);
                    RoyaleEventMatch3Manager.ProcessOnExit(IsStartedLevelPlay);
                    TeamCoopEventMatch3Manager.ProcessOnExit(IsStartedLevelPlay);
                    ButlerGiftManager.ResetStreak();
                    SdbManager.ProcessOnLevelLose(IsStartedLevelPlay);
                    level.ExitWithoutPlaying();
                }
            }
        }

        protected override void RunGame(bool skipLevelStartNarrative = false)
        {
            base.RunGame(skipLevelStartNarrative);

            LevelTitle.gameObject.SetActive(false);
            EventTitle.enabled = true;
        }
    }
}