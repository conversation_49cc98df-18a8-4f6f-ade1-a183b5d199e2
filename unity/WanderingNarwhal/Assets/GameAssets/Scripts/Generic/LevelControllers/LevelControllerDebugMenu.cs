using System.Collections.Generic;
using BBB.Core;
using BBB.Match3.Systems;
using GameAssets.Scripts.Core;
using TMPro;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace BBB.UI
{
    public class LevelControllerDebugMenu : BbbMonoBehaviour
    {
        private const string SuperDiscoBall = "SuperDiscoBall";
        private const string Set = "Set";
        private const string Add = "Add";
        
        [SerializeField] private Button _forceWinButton;
        [SerializeField] private Button _forceLoseButton;
        [SerializeField] private Button _collectGoalsButton;
        [SerializeField] private Button _getReplayButton;
        [SerializeField] private Button _outOfMovesButton;
        [SerializeField] private Button _set1MoveButton;
        [SerializeField] private Button _assistLogToggleButton;
        [SerializeField] private Button _debugMenuButton;
        [SerializeField] private Button _memoryWarningButton;
        [SerializeField] private TMP_InputField _randomSeed;
        [SerializeField] private Toggle _lockRandomSeed;
        [SerializeField] private TMP_InputField _customMoves;
        [SerializeField] private AutoBoosterItems _autoBoosterItemPrefab;

        private bool _isRefreshing;
        private IForceWinLosable _levelController;
        private readonly List<string> _autoBoosterItems = new();
        private AssistValuesPanelController _assistValuesPanel;

        private void Awake()
        {
#if BBB_DEBUG
            _randomSeed.onEndEdit.AddListener((s) =>
            {
                if (_isRefreshing) return;
                long.TryParse(s, out var seed);
                var currentLock = RandomSystem.DebugLockSeed;
                RandomSystem.DebugLockSeed = false;
                RandomSystem.Reset((uint)seed);
                RandomSystem.DebugLockSeed = currentLock;
            });
            _lockRandomSeed.onValueChanged.AddListener((b) =>
            {
                if (_isRefreshing) return;
                RandomSystem.DebugLockSeed = b;
            });
            
            _customMoves.onEndEdit.AddListener(input =>
            {
                if (_isRefreshing) return;
                int.TryParse(input, out var moves);
                SetCustomMoves(moves);
            });
#endif
        }
        
        public void Init(LevelController levelController, AssistValuesPanelController assistValuesPanel)
        {
            _levelController = levelController;
#if BBB_DEBUG
            _forceWinButton.ReplaceOnClick(OnForceWinButton);
            _forceLoseButton.ReplaceOnClick(OnForceLoseButton);
            _collectGoalsButton.ReplaceOnClick(OnCollectGoalsButton);
            _getReplayButton.ReplaceOnClick(OnGetReplayButton);
            _outOfMovesButton.ReplaceOnClick(OnOutOfMovesButton);
            _set1MoveButton.ReplaceOnClick(OnSetOneMoveButton);
            _assistLogToggleButton.ReplaceOnClick(OnAssistLogToggleButton);
            _debugMenuButton.ReplaceOnClick(OnDebugMenuButton);
            _memoryWarningButton.ReplaceOnClick(MemoryAdviceApi.OnLowMemory);
            _assistValuesPanel = assistValuesPanel;

            var eligibleBoosters = _levelController.GetEligibleBoosters();
            if (eligibleBoosters is {Count: > 0})
            {
                foreach (var boostUid in eligibleBoosters)
                {
                    if (_autoBoosterItems.Contains(boostUid)) continue;
                    _autoBoosterItems.Add(boostUid);
                    
                    var boostItem = Instantiate(_autoBoosterItemPrefab, _autoBoosterItemPrefab.transform.parent);
                    boostItem.gameObject.SetActive(true);
                    boostItem.Setup(boostUid, Add);
                    boostItem.ActionButton.onClick.RemoveAllListeners();
                    boostItem.ActionButton.onClick.AddListener(() =>
                    {
                        _levelController.SetCustomBoosterAmount(boostUid, (int) boostItem.Value);
                    });
                }
            }

            // For Super Disco Ball
            if (!_autoBoosterItems.Contains(SuperDiscoBall))
            {
                _autoBoosterItems.Add(SuperDiscoBall);
                var superDiscoBallItem = Instantiate(_autoBoosterItemPrefab, _autoBoosterItemPrefab.transform.parent);
                superDiscoBallItem.gameObject.SetActive(true);
                superDiscoBallItem.Setup(SuperDiscoBall, Set);
                superDiscoBallItem.ActionButton.onClick.RemoveAllListeners();
                superDiscoBallItem.ActionButton.onClick.AddListener(() => { levelController.SetSuperDiscoBallValue(superDiscoBallItem.Value); });
            }
#else
            _debugMenuButton.gameObject.SetActive(false);
            gameObject.SetActive(false);
#endif
        }

        protected override void OnEnable()
        {
            _isRefreshing = true;
            _randomSeed.text = RandomSystem.Seed.ToString();
#if BBB_DEBUG
            _lockRandomSeed.isOn = RandomSystem.DebugLockSeed;
#endif
            _isRefreshing = false;
        }

        private void OnDebugMenuButton()
        {
            gameObject.SetActive(!gameObject.activeSelf);
        }

        private void OnAssistLogToggleButton()
        {
            _assistValuesPanel.LoggerToggleButtonHandler();
        }

        private void OnOutOfMovesButton()
        {
            BDebug.LogWarning(LogCat.Match3, "Out of moves opened from debug!");
            _levelController.ShowOutOfMoves();
        }

        private void OnForceLoseButton()
        {
            _levelController.ForceLose();
        }

        private void OnCollectGoalsButton()
        {
            _levelController.CollectGoals();
        }

        private void OnGetReplayButton()
        {
            _levelController.GetReplayData();
        }

        private void OnSetOneMoveButton()
        {
            _levelController.SetOneMove();
        }
        
        private void SetCustomMoves(int moves)
        {
            _levelController.SetCustomMoves(moves);
        }

        private void OnForceWinButton()
        {
            _levelController.ForceWin();
        }
    }
}
