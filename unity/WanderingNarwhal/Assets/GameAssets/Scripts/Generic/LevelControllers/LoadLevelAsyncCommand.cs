using System;
using BBB.DI;
using BBB.GameAssets.Scripts.Player;
using BBB.Match3.Systems;
using BBB.UI.Level;
using BebopBee;
using Cysharp.Threading.Tasks;
using FBConfig;
using GameAssets.Scripts.Match3.Logic;
using PBGame;
using UnityEngine;

namespace BBB
{
    public class LoadLevelAsyncCommand : CommandBase
    {
        public override int LeafCommandsCount => 0;

        private LevelRemoteData _levelData;

        public LoadLevelAsyncCommand(LevelRemoteData levelData)
        {
            SetLeveRemoteData(levelData);
        }
        
        public LoadLevelAsyncCommand()
        {
        }

        public LoadLevelAsyncCommand SetLeveRemoteData(LevelRemoteData levelData)
        {
            _levelData = levelData;
            return this;
        }
        
        public LoadLevelAsyncCommand ResetCommand()
        {
            base.Reset();
            return this;
        }

        protected override void CommandExecutionStart(IContext context)
        {
            base.CommandExecutionStart(context);
            CommandExecutionStartAsync(context).Forget();
        }

        protected async UniTaskVoid CommandExecutionStartAsync(IContext context)
        {
            var config = context.Resolve<IConfig>();
            var levelConfigs = config.Get<ProgressionLevelConfig>();
            var levelConfig = levelConfigs[_levelData.Config.Uid];

            var levelState = _levelData.State;
            levelState.Stage = (int)LevelHelper.PlayOnlyStage;
            
            var levelFullname = Level.GetLevelFullName(levelConfig, levelState.Stage);
            var levelLoader = context.Resolve<ILevelLoader>();
            var playerManager = context.Resolve<IPlayerManager>();
            var spawnerSettingsManager = context.Resolve<SpawnerSettingsManager>();
            try
            {
                await levelLoader.LoadLevelAsync(levelFullname);

                var level = levelLoader.GetLoadedLevel();
                var toLoad = new LevelRemoteData(levelConfig, levelState, false);
                level.SetupRemoteData(toLoad, spawnerSettingsManager.SpawnerSettings);
                playerManager.SetCurrentLevel(level);
                CurrentStatus = CommandStatus.Success;
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                CurrentStatus = CommandStatus.Failure;
            }
        }
    }

    public class LoadSideMapLevelAsyncCommand : CommandBase
    {
        public override int LeafCommandsCount
        {
            get { return 0; }
        }

        private readonly SideMapLevelRemoteData _levelData;

        public LoadSideMapLevelAsyncCommand(SideMapLevelRemoteData levelData)
        {
            _levelData = levelData;
        }

        public override void Execute(DI.IContext context)
        {
            ExecuteAsync(context).Forget();
        }
        public async UniTaskVoid ExecuteAsync(DI.IContext context)
        {
            if (CurrentStatus == CommandStatus.Pending)
            {
                CurrentStatus = CommandStatus.Running;
                var levelFullname = Level.GetLevelFullName(_levelData.Config, 0);
                var playerManager = context.Resolve<IPlayerManager>();
                var levelLoader = context.Resolve<ILevelLoader>();
                var spawnerSettingsManager = context.Resolve<SpawnerSettingsManager>();
                var config = context.Resolve<IConfig>();
                var locationManager = context.Resolve<ILocationManager>();

                await levelLoader.LoadLevelAsync(levelFullname);

                var level = levelLoader.GetLoadedLevel();
                var levelConfigs = config.Get<ProgressionLevelConfig>();
                var levelConfig = levelConfigs[_levelData.Config.Uid];
                level.SetupRemoteData(new LevelRemoteData(levelConfig, new LevelState { Stage = 0 }, false), spawnerSettingsManager.SpawnerSettings);
                playerManager.SetCurrentLevel(level);
                CurrentStatus = CommandStatus.Success;
            }
            else
            {
                base.Execute(context);
            }
        }
    }
}