using System;
using System.Collections.Generic;
using BBB.Core.Analytics;
using BBB.Core.Analytics.TechAnalytics.EventConstants;
using BBB.Core.Analytics.TechAnalytics.Managers;
using BBB.DI;
using BBB.EndGameEvents;
using BBB.Leanplum;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.GoalsService;
using BBB.RaceEvents;
using BBB.ScreenRecording;
using BBB.Social;
using BBB.TeamEvents;
using BBB.UI.Level.Controllers;
using BBB.Wallet;
using FBConfig;
using Lidgren.Network;
using Newtonsoft.Json;
using RPC.Social;
using UnityEngine;
using EventNames = BBB.Core.Analytics.EventNames;

namespace BBB
{
    public interface ILevelAnalyticsReporter
    {
        void BoostUsed(string boosterId);
        void RegisterPowerUpCreatedDuringGame();
        void RegisterItemDestroyedOn(Tile tile);
        void RegisterFirstMove();
        void RegisterWastedMove();
        void RegisterPlayerMove(NetRandomGenerationValues randomState, IPlayerInput playerInput);
        void LevelStarted(ILevel level, IConfig config, string activeGameEvent);
        string GetReplayData();
        void ReportLevelEnded(LevelOutcome levelOutcome, ILevel level, int evShuffleCount, List<string> boostersToReport, AssistState originalAssistState, AssistState progressAchieved);
        void MovesBought(Price price);
        void RegisterAppliedAutoBoosters(List<AutoBoostInstance> appliedAutoBoosters);
        void RegisterMovesAdded(NetRandomGenerationValues randomState, int addedMoves);
        void ReportStuckEvent(ILevel level, string detailedStr);
        void RegisterSuperDiscoBallValueUpdate(NetRandomGenerationValues randomState, float value);
    }

    public enum LevelResult
    {
        Lose = 0,
        Win = 1,
        Exit = 2,
        ExitWithoutPlaying = 3
    }

    public class LevelAnalyticsReporter : IContextInitializable, ILevelAnalyticsReporter, IContextReleasable
    {
        private int _numTimesPlayed;
        private int _difficulty;
        private int _boostsUsed;
        private int _superBoostsUsed;
        private DateTime _levelStartTime;
        private int _movesBoughtCount;
        private double _totalUsdSpent;
        private int _totalCoinsSpent;
        private int _numberOfGeneratedPowerUps;
        private int _numberOfUsedPowerUps;
        private int _numberOfCrushedSimpleItems;
        private int _secondsUntilFirstMove;
        private int _numberOfWastedMoves;
        private int _numberOfTimesWheelSpinsBought;
        private int _movesWonFromWheelSpins;

        private IConfig _config;
        private GameController _gameController;
        private GoalsSystem _goalSystem;
        private IEventDispatcher _eventDispatcher;
        private IGameEventManager _gameEventManager;
        private IRaceEventManager _raceEventManager;
        private IRoyaleEventManager _royaleEventManager;
        private ITeamEventManager _teamEventManager;
        private ILivesManager _livesManager;
        private IInventory Inventory => _playerManager.PlayerInventory;
        private IPlayerManager _playerManager;
        private bool _stuckReported;
        private readonly ReplayLevelSystem _replayLevelSystem = new ();
        private IScreensBuilder _screensBuilder;
        private IButlerGiftManager _butlerGiftManager;
        private readonly Dictionary<string, int> _boostersUsed = new();
        private ChallengeTriviaManager _challengeTriviaManager;
        private SdbManager _sdbManager;

        public void InitializeByContext(IContext context)
        {
            _config = context.Resolve<IConfig>();
            _gameController = context.Resolve<GameController>();
            _goalSystem = context.Resolve<GoalsSystem>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _gameEventManager = context.Resolve<IGameEventManager>();
            _raceEventManager = context.Resolve<IRaceEventManager>();
            _royaleEventManager = context.Resolve<IRoyaleEventManager>();
            _teamEventManager = context.Resolve<ITeamEventManager>();
            _livesManager = context.Resolve<ILivesManager>();
            _playerManager = context.Resolve<IPlayerManager>();
            _screensBuilder = context.Resolve<IScreensBuilder>();
            _butlerGiftManager = context.Resolve<IButlerGiftManager>();
            _challengeTriviaManager = context.Resolve<ChallengeTriviaManager>();
            _sdbManager = context.Resolve<SdbManager>();
            
            _eventDispatcher.RemoveListener<IapPurchasedEvent>(IapPurchaseHandler);
            _eventDispatcher.AddListener<IapPurchasedEvent>(IapPurchaseHandler);
            
            _eventDispatcher.RemoveListener<SuperBoostInvokedEvent>(RegisterSuperBoosterActivated);
            _eventDispatcher.AddListener<SuperBoostInvokedEvent>(RegisterSuperBoosterActivated);
            
            _eventDispatcher.RemoveListener<GachaSpinPurchasedEvent>(OnGachaSpinPurchasedEvent);
            _eventDispatcher.AddListener<GachaSpinPurchasedEvent>(OnGachaSpinPurchasedEvent);

            _eventDispatcher.RemoveListener<MovesWonEvent>(OnMovesWonEvent);
            _eventDispatcher.AddListener<MovesWonEvent>(OnMovesWonEvent);
        }

        public void ReleaseByContext(IContext context)
        {
            _eventDispatcher.RemoveListener<IapPurchasedEvent>(IapPurchaseHandler);
            _eventDispatcher.RemoveListener<SuperBoostInvokedEvent>(RegisterSuperBoosterActivated);
        }

        public void ResetDefaults()
        {
            _numTimesPlayed = default;
            _difficulty = default;
            _boostsUsed = default;
            _superBoostsUsed = default;
            _levelStartTime = default;
            _movesBoughtCount = default;
            _totalUsdSpent = default;
            _totalCoinsSpent = default;
            _numberOfGeneratedPowerUps = default;
            _numberOfUsedPowerUps = default;
            _numberOfCrushedSimpleItems = default;
            _secondsUntilFirstMove = default;
            _numberOfWastedMoves = default;
            _numberOfTimesWheelSpinsBought = default;
            _movesWonFromWheelSpins = default;
        }

        private int GetAdBonusValue()
        {
            if (Inventory.BonusBooster.IsNullOrEmpty())
                return 0;

            switch (Inventory.BonusBooster)
            {
                case "":
                    return 0;
                case InventoryItems.ExtraMoveBooster:
                    return 1;
                case InventoryBoosters.LineCrushBooster:
                    return 2;
                case InventoryBoosters.BombBooster:
                    return 3;
                case InventoryBoosters.LightningStrikeBooster:
                    return 4;
                default:
                    Debug.LogError($"Unhandled ad bonus: {Inventory.BonusBooster}");
                    return 0;
            }
        }

        private void OnGachaSpinPurchasedEvent(GachaSpinPurchasedEvent gachaSpinPurchasedEvent)
        {
            var paid = gachaSpinPurchasedEvent.Arg0;
            var price = gachaSpinPurchasedEvent.Arg1;
    
            if (!paid)
                return;

            if (price.CurrenciesLength > 0)
            {
                for (var i = 0; i < price.CurrenciesLength; i++)
                {
                    var currency = price.Currencies(i);
                    
                    if(!currency.HasValue)
                        continue;
                    
                    _totalCoinsSpent += (int)currency.Value.Value;
                    break;
                }
            }
            
            _numberOfTimesWheelSpinsBought++;
        }

        private void IapPurchaseHandler(IapPurchasedEvent ev)
        {
            _totalUsdSpent += ev.Arg0.TotalUsdPrice;
        }

        private void ReportLevelStarted(ILevel level)
        {
            var competitionEventTypeScore = GetGameEventTypeScore(GameEventGameplayType.Competition);
            var collectionEventTypeScore = GetGameEventTypeScore(GameEventGameplayType.Collection);
            var sideMapEventTypeScore = GetGameEventTypeScore(GameEventGameplayType.SideMap);
            var endOfContentEventTypeScore = GetGameEventTypeScore(GameEventGameplayType.EndOfContent);
            var raceEventTypeScore = GetRaceEventScore();
            var sweepstakesEventTypeScore = GetGameEventTypeScore(GameEventGameplayType.Sweepstakes);
            var levelVersion = level.VersionHash;
            var assetVersion = level.AssetHash.ToString();
            var playerStreak = GetPlayerStreak();
            var gameEventsDict = GetGameEvents(level);
            var gameEvents = string.Join(",", new List<string>(gameEventsDict.Keys));
            var gameEventsMultiplier = JsonConvert.SerializeObject(gameEventsDict);
            var duration = LoadingTimeMetricsManager.GetLevelLoadingDuration();
            var fennecPerksWinStreak = _playerManager.Player.ButlerWinStreak;
            var fennecPerksLongestWinStreak = _playerManager.Player.ButlerLongestWinStreak;
            
            var dict = new Dictionary<string, object>
            {
                {CustomEventParameters.LevelUid, level.Config.Uid},
                {CustomEventParameters.LevelNum, level.GetNum(_config)},
                {CustomEventParameters.City, level.Config.LocationUid},
                {CustomEventParameters.Difficulty, _difficulty},
                {CustomEventParameters.EventList, gameEvents},
                {CustomEventParameters.EventScoreMultiplier, gameEventsMultiplier},
                {CustomEventParameters.NumberTimesPlayed, _numTimesPlayed},
                {CustomEventParameters.CompetitionEventTypeScore, competitionEventTypeScore, competitionEventTypeScore > 0},
                {CustomEventParameters.CollectionEventTypeScore, collectionEventTypeScore, collectionEventTypeScore > 0},
                {CustomEventParameters.SideMapEventTypeScore, sideMapEventTypeScore, sideMapEventTypeScore > 0},
                {CustomEventParameters.EndOfContentEventTypeScore, endOfContentEventTypeScore, endOfContentEventTypeScore > 0},
                {CustomEventParameters.RaceEventTypeScore, raceEventTypeScore, raceEventTypeScore > 0},
                {CustomEventParameters.SweepstakesEventTypeScore, sweepstakesEventTypeScore, sweepstakesEventTypeScore > 0},
                {CustomEventParameters.LevelVersion, levelVersion},
                {CustomEventParameters.LayoutVersion, assetVersion},
                {CustomEventParameters.PlayerStreak, playerStreak},
                {CustomEventParameters.FennecsPerks, _butlerGiftManager.InitialStreak},
                {CustomEventParameters.FennecsPerksWinStreak, fennecPerksWinStreak},
                {CustomEventParameters.FennecsPerksLongestWinStreak, fennecPerksLongestWinStreak},
                {CustomEventParameters.SdbActive, _sdbManager.SdbActiveWhenLevelStart},
                {EventParameters.Duration , duration},
            };
            if (_challengeTriviaManager.IsEnvironmentReady())
            {
                dict.Add(CustomEventParameters.ChallengeTriviaProgress, _challengeTriviaManager.CurrentWinCount);
            }
            if (_sdbManager.IsEnvironmentReady())
            {
                dict.Add(CustomEventParameters.SdbProgress, _sdbManager.CurrentWinCount);
            }
            
            Analytics.LogEvent(new LevelStartedEvent(dict));

            var propertiesRecording = new Dictionary<string, object>
            {
                {CustomEventParameters.LevelUid, level.Config.Uid},
                {CustomEventParameters.Difficulty, _difficulty},
                {CustomEventParameters.NumberTimesPlayed, _numTimesPlayed},
                {CustomEventParameters.LevelVersion, levelVersion},
                {CustomEventParameters.LayoutVersion, assetVersion},
                {CustomEventParameters.PlayerStreak, playerStreak},
            };

            RedordingManager.SendLevelStartedEvent(level.Config.Uid, propertiesRecording);
        }

        private int GetGameEventTypeScore(GameEventGameplayType gameEventGameplayType)
        {
            if (_gameEventManager == null)
            {
                return 0;
            }

            var currentScreenType = _screensBuilder.CurrentScreenType;
            var gameEvent = _gameEventManager.GetHighestPriorityEvent(ev =>
                ev.Status == GameEventStatus.Active && ev.GameplayType == gameEventGameplayType && ev.CanShowInScreen(currentScreenType));

            return gameEvent?.CurrentScore ?? 0;
        }

        private int GetRaceEventScore()
        {
            var currentScreenType = _screensBuilder.CurrentScreenType;
            var raceEvent = _raceEventManager?.GetHighestPriorityEvent();
            if (raceEvent != null && RaceEvent.CanShowInScreen(currentScreenType) && raceEvent.Joined)
            {
                return raceEvent.CurrentScore;
            }
            return 0;
        }

        private Dictionary<string, int> GetGameEvents(ILevel _)
        {
            var result = new Dictionary<string, int>();
            
            var currentScreenType = _screensBuilder.CurrentScreenType;
            foreach (var gameEvent in _gameEventManager.GetEvents(ev =>
                         ev.Status == GameEventStatus.Active && ev.CanShowInScreen(currentScreenType)))
            {
                switch (gameEvent)
                {
                    case null:
                        continue;
                    case SideMapGameEvent {IntroductionAlreadyShown: true} sideMapGameEvent:
                    {
                        var multiplier = 0;
                        if (sideMapGameEvent.IsMultiplierScoreStreakActive())
                        {
                            multiplier = sideMapGameEvent.GetScoreMultiplier(true);
                        }
                        result.Add(sideMapGameEvent.Uid, multiplier);
                        break;
                    }
                    case CompetitionGameEvent competitionGameEvent:
                    {
                        if (competitionGameEvent.IsQualifiedToJoin && competitionGameEvent.IntroductionAlreadyShown)
                        {
                            var multiplier = 0;
                            if (competitionGameEvent.IsMultiplierScoreStreakActive())
                            {
                                multiplier = competitionGameEvent.GetScoreMultiplier(true);
                            }
                            result.Add(competitionGameEvent.Uid, multiplier);
                        }

                        break;
                    }
                    default:
                    {
                        var multiplier = 0;
                        if (gameEvent.IsMultiplierScoreStreakActive())
                        {
                            multiplier = gameEvent.GetScoreMultiplier(true);
                        }
                        result.Add(gameEvent.Uid, multiplier);
                        break;
                    }
                }
            }

            foreach (var royaleEvent in _royaleEventManager.GetAllEvents())
            {
                if (royaleEvent != null && RoyaleEvent.CanShowInScreen(currentScreenType) && royaleEvent.Joined)
                {
                    result.Add(royaleEvent.Uid, 0);
                }
            }

            foreach (var teamEvent in _teamEventManager.Events)
            {
                if (teamEvent != null && TeamEvent.CanShowInScreen(currentScreenType) && teamEvent.CanSendScore())
                {
                    result.Add(teamEvent.Uid, 0);
                }
            }

            foreach (var raceEvent in _raceEventManager.GetAllEvents())
            {
                if (raceEvent == null || !RaceEvent.CanShowInScreen(currentScreenType) || !raceEvent.Joined || raceEvent.Status != ResultRaceStatus.Active) continue;
                
                var multiplier = 0;
                if (raceEvent.IsMultiplierScoreStreakActive())
                {
                    multiplier = raceEvent.GetScoreMultiplier(true);
                }
                result.Add(raceEvent.Uid, multiplier);
            }

            return result;
        }

        public void ReportLevelEnded(LevelOutcome levelOutcome, ILevel level, int shuffleCount, List<string> inventoryPowerUps,
            AssistState originalAssistState, AssistState progressAchievedAssistState)
        {
            var initialMoves = level.TurnsLimit;
            var movesLeft = _gameController.RemainingMoves;
            var city = level.Config.LocationUid;
            var levelNum = level.GetNum(_config);
            var levelUid = level.Config.Uid;

            var goalProgressValues = _goalSystem.GetGoalProgressValues();
            var lossReason = levelOutcome.GetLossReason(goalProgressValues);
            var levelResult = (int)levelOutcome.GetResult();

            var duration = (int) (DateTime.Now - _levelStartTime).TotalSeconds;

            // Lives should be already processed by now, so if user has loose here we should have -1 live
            var livesRemaining = _livesManager.NumberOfLives;
            var gameEventsDict = GetGameEvents(level);
            var gameEvents = string.Join(",", new List<string>(gameEventsDict.Keys));

            var levelVersion = level.VersionHash;
            var assetHash = level.AssetHash.ToString();
            var playerStreak = GetPlayerStreak();
            
            var levelPlayedEvent = new LevelPlayedEvent(
                levelResult,
                duration,
                initialMoves,
                movesLeft,
                _boostsUsed,
                _superBoostsUsed,
                inventoryPowerUps,
                originalAssistState,
                progressAchievedAssistState,
                _difficulty,
                city,
                levelNum,
                levelUid,
                _numTimesPlayed,
                _movesBoughtCount,
                gameEvents,
                gameEventsDict,
                lossReason,
                _totalUsdSpent,
                _totalCoinsSpent,
                livesRemaining,
                shuffleCount,
                GetAdBonusValue(),
                AssistParams.OriginalAimAtWinningLevel ? _playerManager.GetOnAimToWinSkillModifier() : _playerManager.GetOnAimToLoseSkillModifier(),
                GetReplayData(),
                levelVersion,
                playerStreak,
                _butlerGiftManager.InitialStreak,
                _numberOfGeneratedPowerUps,
                _numberOfUsedPowerUps,
                _numberOfCrushedSimpleItems,
                _secondsUntilFirstMove,
                _numberOfWastedMoves,
                _boostersUsed,
                _numberOfTimesWheelSpinsBought,
                _movesWonFromWheelSpins,
                initialMoves + _gameController.ExtraMovesAdded,
                assetHash,
                _sdbManager.SdbActiveWhenLevelStart
            );

            Analytics.LogEvent(levelPlayedEvent);            
            
            if (levelOutcome != LevelOutcome.ExitWithoutPlaying && _playerManager.Player.LossStreak > 1)
            {
                Analytics.LogEventIntoSpecificService<BBB.Core.Analytics.LeanplumWrapper>(new LostLevelStreakEvent(_playerManager.Player.LossStreak));
            }
            
            Inventory.BonusBooster = string.Empty;

            _replayLevelSystem.RegisterLevelEnded();

            var propertiesRecording = new Dictionary<string, object>
            {
                {CustomEventParameters.LevelUid, level.Config.Uid},
                {CustomEventParameters.Difficulty, _difficulty},
                {CustomEventParameters.NumberTimesPlayed, _numTimesPlayed},
                {CustomEventParameters.LevelVersion, levelVersion},
                {CustomEventParameters.LayoutVersion, assetHash},
                {CustomEventParameters.PlayerStreak, playerStreak},
            };

            RedordingManager.SendLevelEndedEvent(levelUid, propertiesRecording);
        }

        private int GetPlayerStreak()
        {
            var winStreak = _playerManager.Player.WinStreak;
            var lossStreak = _playerManager.Player.LossStreak;

            if (winStreak > lossStreak)
            {
                return winStreak;
            }
            return -lossStreak;
        }

        private void SetupDifficulty(int stage)
        {
            var difficulty = stage + 1;
            difficulty = Mathf.Clamp(difficulty, 1, 4);
            _difficulty = difficulty;
        }

        public void RegisterAppliedAutoBoosters(List<AutoBoostInstance> appliedAutoBoosters)
        {
            if (appliedAutoBoosters.IsNullOrEmpty())
                return;
            _replayLevelSystem.RegisterAppliedAutoBoosters(appliedAutoBoosters);
        }

        public void BoostUsed(string boosterId)
        {
            if (_boostersUsed.TryGetValue(boosterId, out var value))
            {
                _boostersUsed[boosterId] = value + 1;
            }
            else
            {
                _boostersUsed.Add(boosterId, 1);
            }
            
            _boostsUsed++;
        }
        
        private void OnMovesWonEvent(MovesWonEvent obj)
        {
            _movesWonFromWheelSpins += obj.Arg0;
        }

        public void ReportStuckEvent(ILevel level, string detailedStr)
        {
            if (_stuckReported) return;
            
            var levelUid = level.Config.Uid;
            Analytics.LogEvent(new LevelStuckEvent(
                levelUid: levelUid,
                detailedStuckLog: detailedStr,
                replayRecord: GetReplayData()));
            _stuckReported = true;
        }

        public void LevelStarted(ILevel level, IConfig config, string activeGameEvent)
        {
            _levelStartTime = DateTime.Now;
            _numTimesPlayed = level.GetNumPlayed();
            SetupDifficulty(level.Stage);

            ReportLevelStarted(level);
            ClearLevelData();

            _replayLevelSystem.RegisterStartLevel(level, config, activeGameEvent, _playerManager, _gameController);
        }
        
        public void MovesBought(Price price)
        {
            _movesBoughtCount++;

            if (price.CurrenciesLength == 0)
                return;
            
            var currency = price.Currencies(0);
            if (currency.HasValue)
            {
                _totalCoinsSpent += (int)currency.Value.Value;
            }
        }
        
        public string GetReplayData()
        {
            var data = _replayLevelSystem.GetReplayDataAsString();
            return data;
        }

        public void RegisterPlayerMove(NetRandomGenerationValues randomState, IPlayerInput playerInput)
        {
            _replayLevelSystem.RegisterPlayerMove(randomState, playerInput);
        }

        public void RegisterSuperBoosterActivated(SuperBoostInvokedEvent superBoostInvokedEvent)
        {
            if (superBoostInvokedEvent.Arg1 == Coords.OutOfGrid) return;
            
            _superBoostsUsed++;
            _replayLevelSystem.RegisterSuperBoostActivated(superBoostInvokedEvent.Arg0, superBoostInvokedEvent.Arg1);
        }

        public void RegisterMovesAdded(NetRandomGenerationValues randomState, int addedMoves)
        {
            _replayLevelSystem.RegisterMovesAdded(randomState, addedMoves);
        }
        
        public void RegisterSuperDiscoBallValueUpdate(NetRandomGenerationValues randomState, float updatedValue)
        {
            _replayLevelSystem.RegisterSuperDiscoBallValueUpdate(randomState, updatedValue);
        }

        public void RegisterPowerUpCreatedDuringGame()
        {
            _numberOfGeneratedPowerUps++;
        }

        public void RegisterFirstMove()
        {
            _secondsUntilFirstMove = (int) (DateTime.Now - _levelStartTime).TotalSeconds;
        }

        public void RegisterWastedMove()
        {
            _numberOfWastedMoves++;
        }

        public void RegisterItemDestroyedOn(Tile tile)
        {
            RegisterIfSimpleDestroyedOn(tile);
            RegisterIfPowerUpDestroyedOn(tile);
        }

        private void RegisterIfSimpleDestroyedOn(Tile tile)
        {
            if (tile.IsRegularTile)
            {
                _numberOfCrushedSimpleItems++;
            }
        }

        private void RegisterIfPowerUpDestroyedOn(Tile tile)
        {
            if (tile.IsBoost)
            {
                _numberOfUsedPowerUps++;
            }
        }

        private void ClearLevelData()
        {
            _stuckReported = false;
            _boostersUsed.Clear();
            _boostsUsed = 0;
            _superBoostsUsed = 0;
            _movesBoughtCount = 0;
            _totalUsdSpent = 0;
            _totalCoinsSpent = 0;
            _numberOfGeneratedPowerUps = 0;
            _numberOfCrushedSimpleItems = 0;
            _numberOfCrushedSimpleItems = 0;
            _secondsUntilFirstMove = 0;
            _numberOfWastedMoves = 0;
            _numberOfTimesWheelSpinsBought = 0;
            _movesWonFromWheelSpins = 0;
        }
    }
}
