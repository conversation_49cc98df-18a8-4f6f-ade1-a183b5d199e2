using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Text;
using BBB.Core;
using BBB.M3Editor;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Tools;
using BBB.Wallet;
using Lidgren.Network;
using UnityEngine;
using UnityEngine.UI.Extensions;

namespace BBB
{
    public partial class ReplayLevelSystem
    {
        private byte Version { get; set; }
        private bool HasDebugInfo { get; set; }
        public uint RandomSeed { get; private set; }
        private string LevelUId { get; set; }
        private int LevelStage { get; set; }
        private string ConfigHash { get; set; }
        private Hash128 LevelHash { get; set; }
        private DateTime StartLevelUtc { get; set; }
        private string GameEvent { get; set; }
        public SortedDictionary<int, List<AutoBooster>> AutoBoosters { get; } = new();
        public List<Record> Records { get; } = new();
        public float GetOnAimToWinSkillModifier { get; private set; }
        public float GetOnAimToLoseSkillModifier { get; private set; }
        public bool AimAtWinningLevel { get; private set; }
        public float InitialSuperDiscoBall { get; private set; }
        public string AssistSystemUid { get; private set; }

        private bool _useFunnel;

        private static readonly Dictionary<string, AutoBoosterType> AutoBoostersMapping = new()
        {
            [InventoryBoosters.LineCrushBooster] = AutoBoosterType.LineBreaker,
            [InventoryBoosters.BombBooster] = AutoBoosterType.Bomb,
            [InventoryBoosters.LightningStrikeBooster] = AutoBoosterType.ColorBomb,
            [InventoryBoosters.PropellerBooster] = AutoBoosterType.Propeller,
            [InventoryBoosters.PropellerBoosterButler] = AutoBoosterType.PropellerButler,
            [InventoryBoosters.BombBoosterButler] = AutoBoosterType.BombButler,
            [InventoryBoosters.ColorBombBoosterButler] = AutoBoosterType.ColorBombButler,
            [InventoryBoosters.LineBreakerBoosterButler] = AutoBoosterType.LineBreakerButler,
        };

        public string GetReplayDataAsString()
        {
            return Convert.ToBase64String(ToBinaryData());
        }

        public void SetFromBinaryData(string base64Data)
        {
            var zipData = Convert.FromBase64String(base64Data);
            var rawData = CLZF2.Decompress(zipData);
            using var ms = new MemoryStream(rawData);
            var br = new BinaryReader(ms);

            // Format version
            Version = br.ReadByte();

            // If there is extra debug data
            HasDebugInfo = br.ReadByte() != 0;

            // RandomSystem.Seed
            RandomSeed = br.ReadUInt32();

            // Level UID
            var levelUidSize = br.ReadByte();
            var levelUidBytes = br.ReadBytes(levelUidSize);
            LevelUId = Encoding.UTF8.GetString(levelUidBytes, 0, levelUidSize);

            // Level Stage
            LevelStage = br.ReadByte();

            // 20 bytes of config hash to string
            var configHashBytes = br.ReadBytes(20);
            ConfigHash = BitConverter.ToString(configHashBytes).Replace("-", String.Empty).ToLowerInvariant();

            // 16 bytes of level hash
            var levelHashBytes = br.ReadBytes(16);
            var levelHashStr = BitConverter.ToString(levelHashBytes).Replace("-", String.Empty);
            LevelHash = Hash128.Parse(levelHashStr);

            // Start session UTC
            StartLevelUtc = DateTime.FromBinary(br.ReadInt64());

            // Active game event
            var gameEventSize = br.ReadByte();
            var gameEventBytes = br.ReadBytes(gameEventSize);
            GameEvent = Encoding.UTF8.GetString(gameEventBytes, 0, gameEventSize);

            // Auto boosters
            AutoBoosters.Clear();
            var autoBoosterRecordCount = br.ReadByte();
            for (var i = 0; i < autoBoosterRecordCount; ++i)
            {
                var turnIndex = br.ReadByte();
                var turnBoosterCount = br.ReadByte();
                var boosters = new List<AutoBooster>(turnBoosterCount);
                for (var j = 0; j < turnBoosterCount; ++j)
                {
                    var autoBooster = new AutoBooster((AutoBoosterType) br.ReadByte(), Record.ReadCoord(br));
                    boosters.Add(autoBooster);
                }

                AutoBoosters.Add(turnIndex, boosters);
            }

            // Player Actions
            Records.Clear();
            var recCount = br.ReadUInt16();
            for (var i = 0; i < recCount; ++i)
                Records.Add(Record.Deserialize(br));

            //Assist System
            GetOnAimToWinSkillModifier = br.ReadSingle();
            GetOnAimToLoseSkillModifier = br.ReadSingle();
            AimAtWinningLevel = br.ReadBoolean();
            
            //Super Disco Ball
            InitialSuperDiscoBall = br.ReadSingle();
            
            // Assist System Version
            var assistSystemUidSize = br.ReadByte();
            var assistSystemUidBytes = br.ReadBytes(assistSystemUidSize);
            AssistSystemUid = Encoding.UTF8.GetString(assistSystemUidBytes, 0, assistSystemUidSize);
        }

        private byte[] ToBinaryData()
        {
            using var ms = new MemoryStream();
            var bw = new BinaryWriter(ms);

            // Current version of the binary format
            bw.Write((byte) 5);

            // If there is extra debug data
            bw.Write((byte) 0);

            // RandomSystem.Seed
            bw.Write(RandomSeed);

            // level UID (with leading size in bytes)
            var levelUidBytes = Encoding.UTF8.GetBytes(LevelUId);
            bw.Write((byte) levelUidBytes.Length);
            bw.Write(levelUidBytes);

            // Level Stage
            bw.Write((byte) LevelStage);

            // 20 bytes of config hash
            var configHashBytes = new byte[20];
            for (var i = 0; i < ConfigHash.Length; i += 2)
                configHashBytes[i / 2] = (byte) Convert.ToInt32(ConfigHash[i] + ConfigHash[i + 1]);
            bw.Write(configHashBytes);

            // 16 bytes of level hash
            var levelHashBytes = new byte[16];
            var levelHashStr = LevelHash.ToString();
            for (var i = 0; i < levelHashStr.Length; i += 2)
                levelHashBytes[i / 2] = (byte) Convert.ToInt32(levelHashStr[i] + levelHashStr[i + 1]);
            bw.Write(levelHashBytes);

            // Start session UTC
            bw.Write(StartLevelUtc.ToBinary());

            // Active game event
            var gameEventBytes = Encoding.UTF8.GetBytes(GameEvent);
            bw.Write((byte) gameEventBytes.Length);
            bw.Write(gameEventBytes);

            // Auto boosters
            bw.Write((byte) AutoBoosters.Count);
            foreach (var (turnIndex, boosters) in AutoBoosters)
            {
                bw.Write((byte) turnIndex);
                bw.Write((byte) boosters.Count);
                foreach (var booster in boosters)
                {
                    bw.Write((byte) booster.Type);
                    Record.WriteCoord(bw, booster.Pos);
                }
            }

            // Player actions
            bw.Write((ushort) Records.Count);
            foreach (var rec in Records)
                rec.Serialize(bw);

            //Assist System
            bw.Write(GetOnAimToWinSkillModifier);
            bw.Write(GetOnAimToLoseSkillModifier);
            bw.Write(AimAtWinningLevel);
            
            //Super Disco Ball
            bw.Write(InitialSuperDiscoBall);
            
            // Assist System Version
            var assistSystemUidBytes = Encoding.UTF8.GetBytes(AssistSystemUid);
            bw.Write((byte) assistSystemUidBytes.Length);
            bw.Write(assistSystemUidBytes);

            var rawData = ms.ToArray();
            var zipData = CLZF2.Compress(rawData);
            return zipData;
        }

        public void RegisterStartLevel(ILevel level, IConfig config, string activeGameEvent, IPlayerManager playerManager, GameController gameController)
        {
            RandomSeed = RandomSystem.Seed;
            ConfigHash = config.GetHash<FBConfig.ProgressionLevelConfig>();
            LevelHash = level.AssetHash;
            LevelUId = level.LevelUid;
            LevelStage = level.Stage;
            GameEvent = activeGameEvent;
            AutoBoosters.Clear();
            Records.Clear();
            GetOnAimToWinSkillModifier = playerManager.GetOnAimToWinSkillModifier();
            GetOnAimToLoseSkillModifier = playerManager.GetOnAimToLoseSkillModifier();
            AimAtWinningLevel = AssistParams.AimAtWinningLevel;
            InitialSuperDiscoBall = gameController.Settings.SuperDiscoBall;
            AssistSystemUid = level.AssistSystemUid;
#if UNITY_EDITOR
            _useFunnel = !M3Editor.M3Editor.IsCurrentSceneLevelEditorScene();
            StartLevelUtc = _useFunnel ? DateTime.UtcNow : DateTimeOffset.FromUnixTimeSeconds(0).UtcDateTime;
#else
            _useFunnel = true;
            StartLevelUtc = DateTime.UtcNow;
#endif
            if (_useFunnel)
            {
                var replayStringData = GetReplayDataAsString();
                FunnelTracker.StartFunnel(Funnel.Match3Level, replayStringData);
            }
        }

        public void RegisterAppliedAutoBoosters(IEnumerable<AutoBoostInstance> appliedAutoBoosters)
        {
            var autoBoosters = new List<AutoBooster>();
            foreach (var booster in appliedAutoBoosters)
            {
                if (booster.CellPos == Coords.OutOfGrid)
                {
                    BDebug.LogError(LogCat.Match3, $"Applied Booster {booster.Name} is out of grid");
                    break;
                }

                if (AutoBoostersMapping.TryGetValue(booster.Name, out var autoBoosterType))
                {
                    autoBoosterType = autoBoosterType switch
                    {
                        AutoBoosterType.LineBreaker when booster.ExtraInfo != null &&
                                                         (SimplifiedDirections) booster.ExtraInfo ==
                                                         SimplifiedDirections.Vertical => AutoBoosterType.ColumnBreaker,
                        AutoBoosterType.LineBreakerButler when booster.ExtraInfo != null &&
                                                               (SimplifiedDirections) booster.ExtraInfo ==
                                                               SimplifiedDirections.Vertical => AutoBoosterType
                            .ColumnBreakerButler,
                        _ => autoBoosterType
                    };

                    autoBoosters.Add(new AutoBooster(autoBoosterType, booster.CellPos ?? Coords.OutOfGrid));
                }
                else
                {
                    Debug.LogError($"ReplaySystem: cannot register applied auto booster: {booster.Name} at {booster.CellPos}");
                }
            }

            if (autoBoosters.IsNullOrEmpty()) return;

            var turnNumber = Records.Count == 0 ? 0 : Records.Count - 1;
            if (!AutoBoosters.TryAdd(turnNumber, autoBoosters))
            {
                AutoBoosters[turnNumber].AddRange(autoBoosters);
            }
        }

        public void RegisterPlayerMove(NetRandomGenerationValues randomState, IPlayerInput playerInput)
        {
            if (Records.Count == 0)
            {
                RandomSeed = RandomSystem.Seed;
            }

            PlayerAction playerAction;
            Coords sourcePos;
            Coords? targetPos = null;

            switch (playerInput)
            {
                case PlayerInputSingleTap tap:
                    playerAction = PlayerAction.SingleTap;
                    sourcePos = tap.TargetCoords;
                    break;
                case PlayerInputDoubleTap dTap:
                    playerAction = PlayerAction.DoubleTap;
                    sourcePos = dTap.TargetCoords;
                    break;
                case PlayerInputSwap swap:
                    playerAction = PlayerAction.Swap;
                    sourcePos = swap.CoordsPair.FirstCoords;
                    targetPos = swap.CoordsPair.SecondCoords;
                    break;
                case PlayerInputItemShovel booster:
                    playerAction = PlayerAction.Booster;
                    sourcePos = booster.TargetCoord;
                    break;

                case PlayerInputItemVerticalBooster booster:
                    playerAction = PlayerAction.VerticalBooster;
                    sourcePos = booster.TargetCoord;
                    break;

                case PlayerInputItemHorizontalBooster booster:
                    playerAction = PlayerAction.HorizontalBooster;
                    sourcePos = booster.TargetCoord;
                    break;

                default:
                    Debug.LogError($"Unsupported player input type: {playerInput}");
                    return;
            }

            Records.Add(targetPos.HasValue ? new Record(randomState, playerAction, sourcePos, targetPos.Value) : new Record(randomState, playerAction, sourcePos));
            ReplaceFunnel();
        }

        public void RegisterSuperBoostActivated(NetRandomGenerationValues randomState, Coords hitPosition)
        {
            Records.Add(new Record(randomState, PlayerAction.SuperBoost, hitPosition));
            ReplaceFunnel();
        }

        public void RegisterMovesAdded(NetRandomGenerationValues randomState, int addedMoves)
        {
            Records.Add(new Record(randomState, PlayerAction.AddedMoves, addedMoves));
            ReplaceFunnel();
        }
        
        public void RegisterSuperDiscoBallValueUpdate(NetRandomGenerationValues randomState, float updateValue)
        {
            Records.Add(new Record(randomState, PlayerAction.SuperDiscoBall, updateValue));
            ReplaceFunnel();
        }
        
        private void ReplaceFunnel()
        {
            if (_useFunnel)
            {
                FunnelTracker.ReplaceFunnel(Funnel.Match3Level, GetReplayDataAsString());
            }
        }

        public void RegisterLevelEnded()
        {
            if (_useFunnel)
            {
                FunnelTracker.EndFunnel(Funnel.Match3Level);
            }
        }

        public override string ToString()
        {
            var sb = new StringBuilder();
            sb.Append("Assist System: ").Append(AssistSystemUid).Append("; Replay data version: ").Append(Version)
                .Append("; Debug info: ").Append(HasDebugInfo).AppendLine();
            sb.Append("RandomSystem.Seed: ").Append(RandomSeed).AppendLine();
            sb.Append("Level: ").Append(LevelUId).Append('.').Append(LevelStage + 1).AppendLine();
            sb.Append("Config hash: ").AppendLine(ConfigHash);
            sb.Append("Level hash: ").Append(LevelHash).AppendLine();
            sb.Append("Start level UTC: ").AppendLine(StartLevelUtc.ToString(CultureInfo.InvariantCulture));
            sb.Append("Game Event: '").AppendLine(GameEvent);
            sb.Append("Applied boosters: ");

            if (AutoBoosters.TryGetValue(0, out var booster))
            {
                foreach (var autoBooster in booster)
                {
                    const string atString = " at ";
                    const string semiColon = "; ";
                    sb.Append(autoBooster.Type).Append(atString).Append(autoBooster.Pos).Append(semiColon);
                }
            }

            sb.AppendLine().Append("Action count: ").Append(Records.Count);
            sb.AppendLine().Append("Assist System Win: ").Append(GetOnAimToWinSkillModifier).AppendLine();
            sb.Append("Assist System Lose: ").Append(GetOnAimToLoseSkillModifier).AppendLine();
            sb.Append("Assist System Aim At Winning Level: ").Append(AimAtWinningLevel).AppendLine();
            sb.Append("Initial Super Disco Ball Value: ").Append(InitialSuperDiscoBall).AppendLine();

            return sb.ToString();
        }

        public void PopulateListView(ReplayListItem itemTemplate, List<ReplayListItem> listInstances,
            RectTransform listContent, Action<ReplayListItem> onItemSelected)
        {
            Util.SetListItemInstancesExactCount(listInstances, Records.Count, itemTemplate, listContent);

            var index = 0;
            var offsetY = 0f;
            var sizeY = itemTemplate.GetComponent<RectTransform>().rect.height;
            foreach (var item in listInstances)
            {
                AutoBoosters.TryGetValue(index, out var autoBoosters);
                var record = Records[index++];
                item.Setup(record, index, autoBoosters);
                if (onItemSelected != null)
                {
                    item.OnSelectEvent -= onItemSelected;
                    item.OnSelectEvent += onItemSelected;
                }

                item.GetComponent<RectTransform>().anchoredPosition = new Vector2(0, -offsetY);
                offsetY += sizeY;
                item.gameObject.SetActive(true);
            }

            listContent.sizeDelta = new Vector2(listContent.sizeDelta.x, offsetY);
        }
    }
}