using System.Collections.Generic;
using System.IO;
using Lidgren.Network;
using UnityEngine;

namespace BBB
{
    public partial class ReplayLevelSystem
    {
        public enum PlayerAction : byte
        {
            SingleTap,
            DoubleTap,
            Swap,
            Booster,
            SuperBoost,
            AddedMoves,
            VerticalBooster,
            HorizontalBooster,
            SuperDiscoBall
        }

        public enum AutoBoosterType : byte
        {
            <PERSON><PERSON>reaker,
            ColumnBreaker,
            Bomb,
            ColorBomb,
            Propeller,
            PropellerButler,
            BombButler,
            ColorBombButler,
            LineBreakerButler,
            ColumnBreakerButler
        }

        public struct AutoBooster
        {
            public readonly AutoBoosterType Type;
            public Coords Pos;

            public AutoBooster(AutoBoosterType type, Coords pos)
            {
                Type = type;
                Pos = pos;
            }
        }

        public class Record
        {
            public PlayerAction PlayerAction { get; }
            public List<Coords> Positions { get; } = new();

            public NetRandomGenerationValues RandomState;

            public readonly int AddedMoves;

            public readonly float SuperDiscoBall;

            public readonly float TimeStamp;

            private Record(NetRandomGenerationValues randomState, PlayerAction playerAction, float timestamp = -1f)
            {
                RandomState = randomState;
                PlayerAction = playerAction;
                TimeStamp = timestamp >= 0f ? timestamp : Time.realtimeSinceStartup;
            }

            public Record(NetRandomGenerationValues randomState, PlayerAction playerAction, Coords sourcePos,
                float timeStamp = -1f) : this(randomState, playerAction, timeStamp)
            {
                Positions.Add(sourcePos);
            }

            public Record(NetRandomGenerationValues randomState, PlayerAction playerAction, Coords sourcePos,
                Coords targetPos, float timeStamp = -1f) : this(randomState, playerAction, timeStamp)
            {
                Positions.Add(sourcePos);
                Positions.Add(targetPos);
            }

            public Record(NetRandomGenerationValues randomState, PlayerAction playerAction, int addMoves,
                float timeStamp = -1f) : this(randomState, playerAction, timeStamp)
            {
                AddedMoves = addMoves;
            }

            public Record(NetRandomGenerationValues randomState, PlayerAction playerAction, float superDiscoBall,
                float timeStamp = -1f) : this(randomState, playerAction, timeStamp)
            {
                SuperDiscoBall = superDiscoBall;
            }

            public static Record Deserialize(BinaryReader br)
            {
                var mx = br.ReadUInt32();
                var my = br.ReadUInt32();
                var mz = br.ReadUInt32();
                var mw = br.ReadUInt32();
                var randomState = new NetRandomGenerationValues(mx, my, mz, mw);

                var playerAction = (PlayerAction)br.ReadByte();

                switch (playerAction)
                {
                    case PlayerAction.SingleTap:
                    case PlayerAction.DoubleTap:
                    case PlayerAction.Booster:
                    case PlayerAction.SuperBoost:
                    case PlayerAction.VerticalBooster:
                    case PlayerAction.HorizontalBooster:
                        return new Record(randomState, playerAction, ReadCoord(br), br.ReadSingle());

                    case PlayerAction.Swap:
                        return new Record(randomState, playerAction, ReadCoord(br), ReadCoord(br), br.ReadSingle());

                    case PlayerAction.AddedMoves:
                        return new Record(randomState, PlayerAction.AddedMoves, br.ReadByte(), br.ReadSingle());

                    case PlayerAction.SuperDiscoBall:
                        return new Record(randomState, PlayerAction.SuperDiscoBall, br.ReadSingle(), br.ReadSingle());

                    default:
                        Debug.LogError(
                            $"ReplayLevelSystem.Record.Deserialize: Got unknown Player Action: '{(byte)playerAction}'");
                        return null;
                }
            }

            public void Serialize(BinaryWriter bw)
            {
                bw.Write(RandomState.MX);
                bw.Write(RandomState.MY);
                bw.Write(RandomState.MZ);
                bw.Write(RandomState.MW);

                bw.Write((byte)PlayerAction);
                switch (PlayerAction)
                {
                    case PlayerAction.SingleTap:
                    case PlayerAction.DoubleTap:
                    case PlayerAction.Booster:
                    case PlayerAction.VerticalBooster:
                    case PlayerAction.HorizontalBooster:
                        WriteCoord(bw, Positions[0]);
                        break;
                    case PlayerAction.Swap:
                        WriteCoord(bw, Positions[0]);
                        WriteCoord(bw, Positions[1]);
                        break;
                    case PlayerAction.SuperBoost:
                        WriteCoord(bw, Positions[0]);
                        break;
                    case PlayerAction.AddedMoves:
                        bw.Write((byte)AddedMoves);
                        break;
                    case PlayerAction.SuperDiscoBall:
                        bw.Write(SuperDiscoBall);
                        break;
                }
                
                bw.Write(TimeStamp);
            }

            public static void WriteCoord(BinaryWriter bw, Coords pos)
            {
                var singleByte = (byte)(((pos.X & 0xf) << 4) | (pos.Y & 0xf));
                bw.Write(singleByte);
            }

            public static Coords ReadCoord(BinaryReader br)
            {
                var singleByte = br.ReadByte();
                return new Coords(singleByte >> 4, singleByte & 0xf);
            }
        }
    }
}