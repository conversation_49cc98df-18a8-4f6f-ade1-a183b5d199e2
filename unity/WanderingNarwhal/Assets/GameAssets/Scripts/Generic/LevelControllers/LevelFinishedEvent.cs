using GameAssets.Scripts.Messages;

namespace BBB.UI
{
    /// <summary>
    /// Level finished event after 'game result predicted' stage.
    /// It is invoked only when level really finished. Doesn't include exit or exit-without-playing.
    /// </summary>
    /// <remarks>
    /// Level-result-predicted is called when lose or win condition has been met.
    /// If level exited without playing or finishing then this event will not be called. -VK
    /// </remarks>
    public class LevelFinishedEvent : Message<string>, IResettableEvent
    {
        public bool isWin;
        public LevelOutcome outcome;
        public void Reset()
        {
            isWin = default;
            outcome = default;
        }
    }
}