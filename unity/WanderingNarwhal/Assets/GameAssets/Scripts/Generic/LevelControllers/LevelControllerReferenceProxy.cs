using BBB.UI.Level;
using GameAssets.Scripts.Generic;
using GameAssets.Scripts.Match3.Settings;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.UI
{
    public class LevelControllerReferenceProxy : BbbMonoBehaviour
    {
        [SerializeField] private Camera _levelCamera;
        [SerializeField] private LevelTitle _levelTitle;
        [SerializeField] private LevelControllerDebugMenu _debugMenu;
        [SerializeField] private Button _menuButton;
        [SerializeField] private TilesResources _tilesResources;
        [SerializeField] private TileResourceSelector _tilesResourcesExtra;
        [SerializeField] private M3Settings _m3Settings;
        [SerializeField] private LevelRevealer _levelRevealer;
        [SerializeField] private LevelSkipper _levelSkipper;
        [SerializeField] private LevelExitMenuController _levelExitMenuController;
        [SerializeField] private StagePaletteApplier _stagePaletteApplier;
        [SerializeField] private CanvasScaler _canvasScaler;
        
        [field: SerializeField] public PaletteApplier PaletteApplier { get; private set; }
        [field: SerializeField] public TextMeshProUGUI EventTitle { get; private set; }

        public Camera LevelCamera => _levelCamera;

        public LevelExitMenuController LevelExitMenuController => _levelExitMenuController;

        public LevelTitle LevelTitle => _levelTitle;

        public LevelControllerDebugMenu DebugMenu => _debugMenu;

        public Button MenuButton => _menuButton;

        public TilesResources TileResources => _tilesResources;

        public TileResourceSelector TilesResourcesExtra => _tilesResourcesExtra;

        public M3Settings M3Settings => _m3Settings;

        public LevelRevealer LevelRevealer => _levelRevealer;

        public LevelSkipper LevelSkipper => _levelSkipper;


        public StagePaletteApplier StagePaletteApplier => _stagePaletteApplier;
        
        public CanvasScaler CanvasScaler => _canvasScaler;
        
    }
}
