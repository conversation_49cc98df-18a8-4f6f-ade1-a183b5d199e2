using BebopBee;

namespace BBB
{
    public class LeaderboardItem
    {
        public string Uid { get; }
        public string Avatar { get; set; }
        public string Name { get; set; }
        public int Score { get; set; }
        public int Rank { get; set; }

        public LeaderboardItem(string avatar, int score, string name, string uid)
        {
            Avatar = avatar;
            Score = score;
            Name = name;
            Uid = uid;
        }

        public bool IsLocalPlayer(IAccountManager accountManager)
        {
            return Uid == accountManager.Profile.Uid;
        }
    }
}