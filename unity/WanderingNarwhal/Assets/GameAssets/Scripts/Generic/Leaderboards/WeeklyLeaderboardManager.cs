using System;
using System.Collections.Generic;
using BBB.BrainCloud;
using BBB.Core;
using BBB.Core.Wallet;
using BBB.DI;
using BBB.UI;
using BBB.Wallet;
using BebopBee.Core;
using BebopBee.Core.UI;
using BrainCloud;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using FBConfig;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.Core.TimeManager;
using GameAssets.Scripts.CurrenciesRewardModalUI;
using Newtonsoft.Json;
using PBGame;
using UnityEngine;

namespace BBB
{
    public class WeeklyLeaderboardManager
    {
        private const string WeeklyLeaderboardUid = "weeklyleaderboard";
        
        private WeeklyLeaderboardConfig? _weeklyLeaderboardConfig;
        private BrainCloudLeaderboardConfig _bcConfig;
        private TimeManager _timeManager;
        private IPlayerManager _playerManager;
        private ILockManager _lockManager;
        private BrainCloudManager _brainCloudManager;
        private WeeklyLeaderboardRewardsManager _weeklyLeaderboardRewardsManager;
        private ILeaderboardManager _leaderboardManager;
        private IConfig _config;
        private IWalletManager _walletManager;
        private IModalsBuilder _modalsBuilder;
        private IUIWalletManager _uiWalletManager;
        private IEventDispatcher _eventDispatcher;
        private WeeklyLeaderboardState _state;
        private int _rewardMultiplier;
        private Tweener _autoRefreshTweener;

        public WeeklyLeaderboardStatus Status
        {
            get
            {
                if (_state == null)
                {
                    return WeeklyLeaderboardStatus.Inactive;
                }

                if (HaveJoinedSomePeriod())
                {
                    return DoesLocalTimeSpanContain()
                        ? WeeklyLeaderboardStatus.Active
                        : WeeklyLeaderboardStatus.Ended;
                }

                return DoesRemoteTimeSpanContain()
                    ? WeeklyLeaderboardStatus.Inactive
                    : WeeklyLeaderboardStatus.OutOfBounds;
            }
        }

        public bool ShouldShowRewardMultiplierSticker => _rewardMultiplier > 1;
        public int RewardMultiplier => _rewardMultiplier;

        public int WeeklyTrophiesScore => _state.CurrentScore;
        public int LastDeltaTrophies { get; private set; }
        /// <summary>
        /// in brainCloud we have a separate leaderboard for debug purposes called weeklyleaderboard_debug
        /// it has an adhoc rotation type for QA to be able to test leaderboard finale flow at any convenient time
        /// </summary>
        /// <returns>weeklyleaderboard or weeklyleaderboard_debug string</returns>
        public string GetWeeklyLeaderboardUidForBrainCloud()
        {
            var resultUid = LeaderboardConstants.WeeklyLbEventUid;
            const string debugPostfix = "_debug";
            if (_weeklyLeaderboardConfig?.DebugMode ?? false)
            {
                resultUid += debugPostfix;
            }
            return resultUid;
        }

        public bool DebugMode => _weeklyLeaderboardConfig?.DebugMode ?? false;

        public bool ActiveTimeNow
        {
            get
            {
                if (_state == null)
                {
                    Debug.LogError("Trying to access ActiveTimeNow having no state");
                    return false;
                }

                return DoesLocalTimeSpanContain();
            }
        }

        public void Init(IContext context)
        {
            _playerManager = context.Resolve<IPlayerManager>();
            _lockManager = context.Resolve<ILockManager>();
            _uiWalletManager = context.Resolve<IUIWalletManager>();
            _walletManager = context.Resolve<IWalletManager>();
            _modalsBuilder = context.Resolve<IModalsBuilder>();
            _timeManager = context.Resolve<TimeManager>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _weeklyLeaderboardRewardsManager = context.Resolve<WeeklyLeaderboardRewardsManager>();
            _leaderboardManager = context.Resolve<ILeaderboardManager>();
            _brainCloudManager = context.Resolve<BrainCloudManager>();
            _config = context.Resolve<IConfig>();
            _state = _playerManager.Player.WeeklyLeaderboardState;
            _weeklyLeaderboardConfig = _config.TryGetDefaultFromDictionary<WeeklyLeaderboardConfig>();
            var gameEventConfig = _config.Get<GameEventConfig>();
            _rewardMultiplier = gameEventConfig.GetRewardMultiplier(WeeklyLeaderboardUid);
            TryRefreshLeaderboardPeriod();
            TrySubscribeToEnd();
        }
        
        private void TrySubscribeToEnd()
        {
            if (!HaveJoinedSomePeriod() || !DoesLocalTimeSpanContain())
                return;

            ResetAutoRefreshTweener();
            
            var timeTillEnd = (float)GetTimeLeft().TotalSeconds;
            if (timeTillEnd > 0)
            {
                _autoRefreshTweener = Rx.Invoke(timeTillEnd + 1f, _ =>
                {
                    TryRefreshLeaderboardPeriod();
                    ResetAutoRefreshTweener();
                });
            }
        }
        
        //Auto refreshes the leaderboard if the player doesn't have a reward.             
        //Otherwise, it should be handled by the Priority Handler and show the leaderboard
        private void TryRefreshLeaderboardPeriod()
        {
            if (Status != WeeklyLeaderboardStatus.Ended)
                return;
            
            var reward = _weeklyLeaderboardRewardsManager.GetBestOwnReward();
            if (!RewardsUtility.IsRewardValid(reward))
            {
                EndCurrentWeeklyLeaderboard();
                GetWeeklyLeaderboardConfigAsync().Forget();
            }
        }

        public TimeSpan GetTimeLeft(string arg = null)
        {
            if (_state == null)
            {
                Debug.LogError("Trying to access GetTimeLeft while having no state");
                return default;
            }
            
            var endTimeStamp = Util.UnixTimeStampInMsecsToDateTime((long)_state.EndTimeOfLastJoinedPeriod);
            var result = endTimeStamp - GetCurrentUtcDateTime();

            return result.TotalMilliseconds >= 0 ? result : default;
        }

        public DateTime GetEndTimeOfLastJoinedPeriod()
        {
            if (_state.EndTimeOfLastJoinedPeriod > 0.0)
            {
                return Util.UnixTimeStampInMsecsToDateTime((long)_state.EndTimeOfLastJoinedPeriod);
            }

            return default;
        }

        public RotatingLeaderboardPeriodParams ParamsLastJoinedPeriod()
        {
            var result = new RotatingLeaderboardPeriodParams
            {
                EndTimeOfLastJoinedPeriod = (long)_state.EndTimeOfLastJoinedPeriod,
                TournamentCodeOfLastJoinedPeriod = _state.TournamentCodeOfLastJoinedPeriod
            };

            return result;
        }

        private void JoinCurrentPeriodIfNeeded(BrainCloudLeaderboardConfig config)
        {
            if (!HaveJoinedSomePeriod())
            {
                JoinCurrentPeriod(config);
                TrySubscribeToEnd();
            }
            else
            {
                BDebug.LogError(LogCat.Leaderboards, "LB DEBUG: Can not join leaderboard: no config found");
            }
        }

        private bool HaveJoinedSomePeriod()
        {
            return Math.Abs(_state.EndTimeOfLastJoinedPeriod) > Mathf.Epsilon;
        }

        private void JoinCurrentPeriod(BrainCloudLeaderboardConfig config)
        {
            if (config == null)
            {
                BDebug.LogError(LogCat.Leaderboards, "LB DEBUG: Can not join leaderboard: no config found");
                return;
            }

            if (config.CurrentPeriod != null && config.CurrentPeriod.EndingAt > GetCurrentUtcDateTime().UTCDateTimeToUTCMillis())
            {
                _state.EndTimeOfLastJoinedPeriod = config.CurrentPeriod.EndingAt;

                string firstTConfigCode = null;
                if (config.TConfigs is { Values: { Count: > 0 } })
                {
                    foreach (var tConfig in config.TConfigs.Values)
                    {
                        firstTConfigCode = tConfig.TConfigCode;
                        break;
                    }
                }

                _state.TournamentCodeOfLastJoinedPeriod = firstTConfigCode;

               _playerManager.MarkDirty();
            }
            else
            {
                var endingAt = config.CurrentPeriod?.EndingAt ?? 0;
                BDebug.LogError(LogCat.Leaderboards, "LB DEBUG: Can not join leaderboard, endingAt = " + endingAt);

                _leaderboardManager.ResetWeeklyData();
                Reset();
                _leaderboardManager.ReloadWeeklyData(true);
            }
        }

        private void ResetStateToDefault()
        {
            var stateType = _state.GetType();
            foreach (var property in stateType.GetProperties())
            {
                var propertyType = property.PropertyType;
                var defaultValue = propertyType.IsValueType ? Activator.CreateInstance(propertyType) : null;
                property.SetValue(_state, defaultValue, null);
            }
        }

        public void EndCurrentWeeklyLeaderboard()
        {
            if (Status != WeeklyLeaderboardStatus.Ended)
            {
                BDebug.Log(LogCat.Leaderboards, "LB DEBUG: EndCurrentWeeklyLeaderboard: returned because not ended");
                return;
            }

            if (!ConnectivityStatusManager.ConnectivityReachable)
            {
                BDebug.Log(LogCat.Leaderboards, "LB DEBUG: EndCurrentWeeklyLeaderboard: returned because internet is not reachable");
                return;
            }
            
            var position = _leaderboardManager.GetOwnPlayerRankPosition(LeaderboardFilterTypes.PlayersWeekly);
            var reward = _weeklyLeaderboardRewardsManager.GetBestOwnReward();
            _leaderboardManager.ClearLastSeenRanks(LeaderboardFilterTypes.PlayersWeekly);

            if (RewardsUtility.IsRewardValid(reward))
            {
                BDebug.Log(LogCat.Leaderboards, "LB DEBUG: EndCurrentWeeklyLeaderboard: reward is valid: " + JsonConvert.SerializeObject(reward));
                
                var analyticsData = _weeklyLeaderboardRewardsManager.GetWeeklyEventCurFlowData();

                var rewardDictionary = new Dictionary<string, long>();
                foreach (var kv in reward)
                {
                    rewardDictionary[kv.Key] = kv.Value;
                }

                var transaction = new Transaction()
                    .AddTag(TransactionTag.WeeklyLeaderboard)
                    .SetAnalyticsData(analyticsData.Category, analyticsData.Family, analyticsData.Item)
                    .Earn(rewardDictionary);

                var transactionController = _walletManager.TransactionController;
                transactionController.MakeTransaction(transaction);
                
                var weeklyEventCongrats = _modalsBuilder
                    .CreateModalView<WeeklyEventCongratsController>(ModalsType.WeeklyEventCongrats);
                weeklyEventCongrats.Setup(RewardsUtility.IsRewardValid(reward),
                    position, LeaderboardFilterTypes.PlayersWeekly, () => ShowReward(reward));

                weeklyEventCongrats.ShowModal(ShowMode.Delayed);
            }
            else
            {
                BDebug.Log(LogCat.Leaderboards, "LB DEBUG: EndCurrentWeeklyLeaderboard: reward is not valid");
            }

            _leaderboardManager.ResetWeeklyData();
            Reset();
            
            _eventDispatcher.TriggerEvent(_eventDispatcher.GetMessage<LeaderboardMadeDirty>());
            
            _leaderboardManager.ReloadWeeklyData(false);
        }
        
        private void ShowReward(Dictionary<string, int> reward)
        {
            if (!RewardsUtility.IsRewardValid(reward))
                return;

            var rewardModal = _modalsBuilder.CreateModalView<CurrenciesRewardModalController>(ModalsType.CurrenciesRewardModal);

            var isRewardCollected = false;
            rewardModal.SetupInitialParams(reward,
                LocalizationManagerHelper.AnnouncementRewardTitle,
                LocalizationManagerHelper.AnnouncementRewardSubtitle, OnRewardModalClose);

            void OnRewardModalClose(HashSet<string> skippedCurrencies)
            {
                if (isRewardCollected)
                    return;

                isRewardCollected = true;

                _uiWalletManager.VisualizeAllTransactionsWithTag(TransactionTag.WeeklyLeaderboard, skippedCurrencies);
            }

            rewardModal.ShowModal(ShowMode.Delayed);
        }

        public int TryAddWeeklyTrophy(int trophies = 1)
        {
            LastDeltaTrophies = trophies;
            
            if (Status == WeeklyLeaderboardStatus.Active && ActiveTimeNow) 
            {
                if (_state != null)
                {
                    _state.CurrentScore += trophies;
                    _state.LastAddedScoreDelta = trophies;
                    return _state.CurrentScore;
                }   
            }

            LastDeltaTrophies = 0;
            return 0;
        }
        
        public bool TrySubmitToWeeklyLeaderboard(Action<bool> callback = null)
        {
            if (!ConnectivityStatusManager.ConnectivityReachable)
            {
                callback.SafeInvoke(false);
                return false;
            }
            
            if (LastDeltaTrophies > 0) {
                var brainCloudLbUid = GetWeeklyLeaderboardUidForBrainCloud();
                _brainCloudManager.PostScoreToLeaderboard(brainCloudLbUid, LastDeltaTrophies, null, SuccessCallback, FailureCallback);
                return true;

                void SuccessCallback()
                {
                    _leaderboardManager.ReloadAllData(true);
                    callback.SafeInvoke(true);
                }

                void FailureCallback()
                {
                    Debug.Log("PostScoreToLeaderboard Failed");
                }
            }

            callback.SafeInvoke(false);
            return false;
        }

        public bool IsWeeklyTournamentUnlocked()
        {
            return !_lockManager.IsLocked(LeaderboardConstants.TROPHIES_LOCK_ID, LockItemType.Quests) && !_lockManager.IsLocked(LeaderboardConstants.WEEKLY_TOURNAMENT_LOCK_ID, LockItemType.Quests);
        }

        public bool IsMultiplierScoreStreakActive()
        {
            if (Status != WeeklyLeaderboardStatus.Active)
            {
                return false;
            }
            
            if (!(_weeklyLeaderboardConfig?.DoubleScoreStreakEnabled ?? true))
            {
                return false;
            }

            return _weeklyLeaderboardConfig.Value.ScoreMultiplierLength > 0;
        }
        
        public int GetScoreMultiplier(bool includeCurrentAttempt = false)
        {
            return GameEventUtils.GetScoreMultiplier(includeCurrentAttempt ? _state.MultiplierStreakCached : _state.MultiplierStreak, _weeklyLeaderboardConfig.Value.ScoreMultiplier, _weeklyLeaderboardConfig.Value.ScoreMultiplierLength);
        }
        
        public int GetLowestMultiplier()
        {
            return GameEventUtils.GetLowestMultiplier(_weeklyLeaderboardConfig.Value.ScoreMultiplier, _weeklyLeaderboardConfig.Value.ScoreMultiplierLength);
        }

        public void DecrementFailedAttempt()
        {
            if (_state == null) return;
            
            _state.FailedWinAttempts--;
            if (_state.FailedWinAttempts < 0)
            {
                _state.FailedWinAttempts = 0;
                Debug.LogWarning("Failed attempts shouldn't be a negative number. Check Increment and Decrement logic");
            }
                
            _state.MultiplierStreak = ++_state.MultiplierStreakCached;
        }

        /// <summary>
        /// Increment on the first move and decrement on win to catch the killing app cases
        /// </summary>
        public void IncrementFailedAttempt()
        {
            if (_state == null) return;
            
            _state.FailedWinAttempts++;
            _state.MultiplierStreakCached = _state.MultiplierStreak;
            _state.MultiplierStreak = 0;
        }

        private DateTime GetCurrentUtcDateTime()
        {
            var offset = _weeklyLeaderboardConfig?.DebugLocalTimeOffsetInSeconds ?? 0;
            return _timeManager.GetCurrentDateTime().AddSeconds(offset);
        }

        private bool DoesLocalTimeSpanContain()
        {
            var endTimeOfLastJoined = GetEndTimeOfLastJoinedPeriod();
            var utcNow = GetCurrentUtcDateTime();
            return utcNow < endTimeOfLastJoined;
        }

        private bool DoesRemoteTimeSpanContain()
        {
            if (_bcConfig == null) return false;

            var currentUtcDateTime = GetCurrentUtcDateTime();
            return _bcConfig.DoesMyTimeSpanContain(currentUtcDateTime);
        }

        public async UniTask GetWeeklyLeaderboardConfigAsync()
        {
            if (_bcConfig != null)
            {
                BDebug.Log(LogCat.Leaderboards, "LB DEBUG: GetWeeklyLeaderboardConfigAsync: config is already there");
                return;
            }

            if (!ConnectivityStatusManager.ConnectivityReachable) 
                return;
            
            var weeklyLeaderboardId = GetWeeklyLeaderboardUidForBrainCloud();
            
            var taskCompletionSource = new UniTaskCompletionSource<bool>();
            BDebug.Log(LogCat.Leaderboards, "LB DEBUG: GetWeeklyLeaderboardConfigAsync: weeklyLeaderboardId = " + weeklyLeaderboardId);
            _brainCloudManager.GetLeaderboardConfig(new List<string> {weeklyLeaderboardId}, configData =>
            {
                BDebug.Log(LogCat.Leaderboards,"LB DEBUG: GetWeeklyLeaderboardConfigAsync: response = " + JsonConvert.SerializeObject(configData));
                if (configData.Data.response.TryGetValue(weeklyLeaderboardId, out var config))
                {
                    var now = GetCurrentUtcDateTime();
                    if (HaveJoinedSomePeriod())
                    {
                        _bcConfig = config;
                    }
                    else if (config.DoesMyTimeSpanContain(now))
                    {
                        _bcConfig = config;
                        JoinCurrentPeriodIfNeeded(config);
                    }
                }
                else
                {
                    BDebug.Log(LogCat.Leaderboards,"LB DEBUG: GetWeeklyLeaderboardConfigAsync: response: id not found in dict");
                }
                taskCompletionSource.TrySetResult(true);
            }, () =>
            {
                BDebug.Log(LogCat.Leaderboards,"LB DEBUG: GetWeeklyLeaderboardConfigAsync: failure weeklyLeaderboardId = " + weeklyLeaderboardId);
            });

            await taskCompletionSource.Task;
        }

        public void SetScore(int trophies)
        {
            if (Status == WeeklyLeaderboardStatus.Active)
            {
                if (_state == null) return;
                
                BDebug.Log(LogCat.Leaderboards,"LB DEBUG: SetScore: trophies = " + trophies);
                if (trophies != -1)
                {
                    _state.CurrentScore = trophies;
                    _state.LastAddedScoreDelta = 0;
                }
            }
            else
            {
                BDebug.Log(LogCat.Leaderboards,"LB DEBUG: SetScore: not active, trophies = " + trophies);
            }
        }

        public void Reset()
        {
            BDebug.Log(LogCat.Leaderboards,"LB DEBUG: Reset weekly leaderboard");
            _bcConfig = null;
            LastDeltaTrophies = 0;
            ResetStateToDefault();
        }

        private void ResetAutoRefreshTweener()
        {
            _autoRefreshTweener?.Kill();
            _autoRefreshTweener = null;
        }

        public void Restart()
        {
            ResetAutoRefreshTweener();
        }
    }
}