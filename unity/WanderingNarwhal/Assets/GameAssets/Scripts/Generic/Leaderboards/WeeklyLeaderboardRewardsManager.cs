using System.Collections.Generic;
using BBB.BrainCloud;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.DI;
using UnityEngine;

namespace BBB
{
    public class WeeklyLeaderboardRewardsManager : IContextInitializable
    {
        private const string WeeklyCategory = LeaderboardFilterTypes.PlayersWeekly;

        private readonly List<Dictionary<string, int>> _rewardsList = new();
        private ILeaderboardManager _leaderboardManager;
        private WeeklyLeaderboardManager _weeklyLeaderboardManager;

        public void InitializeByContext(IContext context)
        {
            _leaderboardManager = context.Resolve<ILeaderboardManager>();
            _weeklyLeaderboardManager = context.Resolve<WeeklyLeaderboardManager>();
        }

        public Dictionary<string, int> GetRewardFor(int position)
        {
            return GetRewardFor_Internal(position);
        }

        private Dictionary<string, int> GetRewardFor_Internal(int position)
        {
            if (position <= 0)
            {
                BDebug.Log(LogCat.Social, "Can not use <= 0 position for weekly leaderboard rewards");
                return null;
            }

            var dataForFilter = _leaderboardManager.GetFilteredScoresData(WeeklyCategory);
            if (dataForFilter.ScoresCount() == 0)

                if (dataForFilter.ScoresCount() == 0 || dataForFilter.IsOnlyOwn())
                    return null;

            var placeIndex = position - 1;
            if (placeIndex < _rewardsList.Count)
            {
                return _rewardsList[placeIndex];
            }

            return null;
        }

        public Dictionary<string, int> GetBestRewardFor(string userId)
        {
            var position = _leaderboardManager.GetOtherPlayerRankPosition(WeeklyCategory, userId);
            return GetRewardFor_Internal(position);
        }

        public Dictionary<string, int> GetBestOwnReward()
        {
            var position = _leaderboardManager.GetOwnPlayerRankPosition(WeeklyCategory);
            return GetRewardFor_Internal(position);
        }

        public GameEventCurrencyFlowData GetWeeklyEventCurFlowData()
        {
            var selectedPosition = -1;
            var found = false;
            var position = _leaderboardManager.GetOwnPlayerRankPosition(WeeklyCategory);
            var rewardForCategory = GetRewardFor_Internal(position);
            if (rewardForCategory != null)
            {
                selectedPosition = position;
                found = true;
            }

            var result = new GameEventCurrencyFlowData
            {
                Category = CurrencyFlow.GameEvents.Name,
                Family = CurrencyFlow.GameEvents.WeeklyLeaderboard,
                Item = found ? LeaderboardFilterTypes.CategoryToAnalyticsItem(WeeklyCategory, selectedPosition) : CurrencyFlow.GameEvents.ConsolationPrize
            };

            return result;
        }

        public void UpdateRewards(IList<BCPayoutRule> payoutRules)
        {
            if (payoutRules == null)
            {
                Debug.LogError("Payout rules are null");
                return;
            }

            _rewardsList.Clear();
            var rewardMultiplier = _weeklyLeaderboardManager.RewardMultiplier;

            foreach (var rule in payoutRules)
            {
                if (rule.Rank == null)
                {
                    Debug.LogError("One of the rank objects in payout rules is null which is not supposed to happen");
                    continue;
                }

                var rewards = rule.Reward?.GetRewards();
                if (rewards == null)
                {
                    Debug.LogError("One of the reward objects in payout rules is null which is not supposed to happen");
                    continue;
                }

                if (rule.Rank.RankAbs == 0 && rule.Rank.RankUpTo != 0)
                {
                    if (_rewardsList.Count < rule.Rank.RankUpTo)
                    {
                        for (var i = _rewardsList.Count; i < rule.Rank.RankUpTo; i++)
                        {
                            _rewardsList.Add(null);
                        }
                    }

                    for (var i = 0; i < rule.Rank.RankUpTo; i++)
                    {
                        if (_rewardsList[i] != null) continue;
                        
                        if (rewardMultiplier > 1)
                        {
                            var multipliedRewards = new Dictionary<string, int>();
                            foreach (var kvp in rewards)
                            {
                                multipliedRewards[kvp.Key] = kvp.Value * rewardMultiplier;
                            }
                            _rewardsList[i] = multipliedRewards;
                        }
                        else
                        {
                            _rewardsList[i] = rewards;
                        }
                    }
                }
                else if (rule.Rank.RankAbs != 0)
                {
                    if (_rewardsList.Count < rule.Rank.RankAbs)
                    {
                        for (var i = _rewardsList.Count; i < rule.Rank.RankAbs; i++)
                        {
                            _rewardsList.Add(null);
                        }
                    }

                    var rankIndex = rule.Rank.RankAbs - 1;

                    if (rewardMultiplier > 1)
                    {
                        var multipliedRewards = new Dictionary<string, int>();

                        foreach (var kvp in rewards)
                        {
                            multipliedRewards[kvp.Key] = kvp.Value * rewardMultiplier;
                        }

                        _rewardsList[rankIndex] = multipliedRewards;
                    }
                    else
                    {
                        _rewardsList[rankIndex] = rewards;
                    }
                }
            }
        }
    }
}