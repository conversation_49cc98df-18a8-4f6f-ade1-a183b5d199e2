using System;
using System.Collections.Generic;
using System.ComponentModel;
using RPC.Social;

namespace BBB
{
    public class LeaderboardFilterTypes
    {
        public const string TeamsWorld = "TeamsWorld";
        public const string TeamsCountry = "TeamsCountry";
        public const string PlayersWorld = "PlayersWorld";
        public const string PlayersCountry = "PlayersCountry";
        public const string PlayersWeekly = "PlayersWeekly";

        private const string DigitsFormat = "D2";
        private const string WorldTop = "world_top_";
        private const string CountryTop = "country_top_";

        private static readonly HashSet<string> ValidaTabNames = new()
        {
            TeamsWorld,
            TeamsCountry,
            PlayersWorld,
            PlayersCountry,
            PlayersWeekly,
        };

        public static bool IsValid(string tabName)
        {
            return ValidaTabNames.Contains(tabName);
        }
        
        public static LeaderboardNotifier.NotifierType CategoryToNotifierType(string category) => category switch
        {
            TeamsWorld => LeaderboardNotifier.NotifierType.TeamsWorld,
            TeamsCountry => LeaderboardNotifier.NotifierType.TeamsCountry,
            PlayersWorld => LeaderboardNotifier.NotifierType.PlayersWorld,
            PlayersCountry => LeaderboardNotifier.NotifierType.PlayersCountry,
            PlayersWeekly => LeaderboardNotifier.NotifierType.PlayersWeekly,
            _ => throw new Exception($"Leaderboard category {category} doesn't exist.")
        };

        public static string CategoryToAnalyticsItem(string category, int position) => category switch
        {
            PlayersWeekly or PlayersWorld or TeamsWorld => $"{WorldTop}{position.ToString(DigitsFormat)}",
            PlayersCountry or TeamsCountry => $"{CountryTop}{position.ToString(DigitsFormat)}",
            _ => string.Empty
        };
    }
}