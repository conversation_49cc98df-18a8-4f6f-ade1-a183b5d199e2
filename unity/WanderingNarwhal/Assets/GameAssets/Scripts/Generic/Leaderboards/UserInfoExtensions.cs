using BBB.BrainCloud;
using BebopBee;
using BebopBee.Social;
using RPC.Social;
using RPC.Teams;
using UnityEngine.SocialPlatforms;

namespace BBB
{
    public static class UserInfoExtensions
    {
        public static LeaderboardManager.Score ToScoreData(this SocialUserInfo userData, int position, bool isOwnPlayer)
        {
            return new LeaderboardManager.Score()
            {
                Uid = userData.Uid,
                Name = userData.Name,
                Country = userData.Country,
                Position = position,
                Trophies = userData.Trophies,
                Avatar = userData.Avatar,
                LastLocation = userData.LastUnlockedLocation,
                LastLevel = userData.HighestPassedLevel,
                IsOwnPlayer = isOwnPlayer,
            };
        }
        
        public static LeaderboardManager.Score ToScoreData(this SocialTeamInfo socialTeamInfo, int position, bool isOwnTeam)
        {
            return new LeaderboardManager.Score()
            {
                Uid = socialTeamInfo.Uid,
                Name = socialTeamInfo.Name,
                Country = socialTeamInfo.Country,
                Position = position,
                Trophies = socialTeamInfo.Trophies,
                Avatar = socialTeamInfo.Avatar,
                IsOwnPlayer = isOwnTeam,
            };
        }

        public static SocialTeamInfo ToSocialTeamInfo(this TeamData teamData)
        {
            return new SocialTeamInfo()
            {
                Uid = teamData.TeamUid,
                Name = teamData.Name,
                Country = teamData.Country,
                Trophies = teamData.Score,
                Avatar = teamData.Icon,
            };
        }

        public static SocialUserInfo ToSocialUserInfo(this IPublicProfile userProfile, int trophies)
        {
            var result = new SocialUserInfo
            {
                Uid = userProfile.Uid,
                Name = userProfile.Name,
                Avatar = userProfile.Avatar,
                Country = userProfile.Country,
                HighestPassedLevel = userProfile.HighestPassedLevelId,
                LastUnlockedLocation = userProfile.LastUnlockedLocationId,
                Trophies = trophies
            };

            return result;
        }

        public static SocialUserInfo ToSocialUserInfo(this BCPlayerLeaderboardEntry user)
        {
            var result = new SocialUserInfo
            {
                Uid = user.PlayerId,
                Name = user.Name,
                Avatar = user.PictureUrl,
                Country = user.SummaryFriendData?.Country ?? ProfileUtils.DefaultCountry,
                Trophies = user.Score,
                LastUnlockedLocation = user.SummaryFriendData?.LastUnlockedLocationId ?? ProfileUtils.DefaultLocation,
                HighestPassedLevel = user.SummaryFriendData?.HighestPassedLevelId ??  ProfileUtils.DefaultLevel,
            };

            return result;
        }

        public static SocialTeamInfo ToSocialTeamInfo(this BCGroupLeaderboardEntry group)
        {
            var result = new SocialTeamInfo
            {
                Uid = group.GroupId,
                Name = group.GroupName,
                Avatar = group.GroupSummaryData?.Icon ?? string.Empty,
                Country = group.GroupSummaryData?.Country ?? string.Empty,
                Trophies = group.Score
            };

            return result;
        }
    }
}