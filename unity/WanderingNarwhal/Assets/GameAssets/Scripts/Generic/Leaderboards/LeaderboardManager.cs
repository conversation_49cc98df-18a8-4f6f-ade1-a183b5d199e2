using System.Collections.Generic;
using BBB.Core;
using BBB.DI;
using BBB.Modals;
using BBB.Wallet;
using BebopBee;
using BebopBee.Core.UI;
using GameAssets.Scripts.SocialScreens.Teams;
using RPC.Social;
using BBB.BrainCloud;
using Cysharp.Threading.Tasks;
using Newtonsoft.Json;
using System;
using BBB.Core.Analytics;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.Database;
using Realms;

namespace BBB
{
    public sealed partial class LeaderboardManager : ILeaderboardManager
    {
        private const int CategoriesCount = 5;
        
        private readonly List<string> _allCategories = new(CategoriesCount)
        {
            LeaderboardFilterTypes.PlayersWeekly,
            LeaderboardFilterTypes.PlayersWorld,
            LeaderboardFilterTypes.PlayersCountry
        };

        private readonly string[] _groupCategories =
        {
            LeaderboardFilterTypes.TeamsWorld,
            LeaderboardFilterTypes.TeamsCountry
        };

        private abstract class CategoryScoresData
        {
            public FilteredScoresData Data = new();
            public bool Loaded;
        }

        private class CategoryScoresDataWithCaching<T> : CategoryScoresData where T : class
        {
            public T RemoteData;
        }
        
        private Dictionary<string, CategoryScoresData> _filteredScoresDataByCategory;
        
        private readonly List<string> _playerFiltersToSend = new();

        private readonly List<ScreenType> _loadingInvokingScreens = new()
        {
            ScreenType.None,
            ScreenType.LoadingScreen,
            ScreenType.LevelScreen
        };

        private IModalsBuilder _modalsBuilder;
        private IModalsManager _modalsManager;
        private ISocialManager _socialManager;
        private WeeklyLeaderboardRewardsManager _weeklyRewardManager;
        private WeeklyLeaderboardManager _weeklyLeaderboardManager;
        private IAccountManager _accountManager;
        private IEventDispatcher _eventDispatcher;
        private ILockManager _lockManager;
        private LeaderboardModalController _leaderboardModalController;
        private BrainCloudManager _brainCloudManager;
        private GameNotificationManager _notificationManager;
        

        private IScreensManager _screensManager;
        private IPlayerManager _playerManager;

        private RanksDeltas _cachedDeltas;

        public bool GotAtLeastOneResponse { get; private set; }
        private string OwnUserUid => _brainCloudManager.ProfileId;

        private int GetWeeklyTrophiesScoreInternal()
        {
            var data = _filteredScoresDataByCategory[LeaderboardFilterTypes.PlayersWeekly].Data;
            return data.GetTrophies(OwnUserUid).Trophies;
        }

        public void Init(IContext context)
        {
            _notificationManager = context.Resolve<GameNotificationManager>();
            _screensManager = context.Resolve<IScreensManager>();
            _accountManager = context.Resolve<IAccountManager>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            context.Resolve<IGameEventManager>();
            context.Resolve<IWalletManager>();
            _modalsManager = context.Resolve<IModalsManager>();
            _modalsBuilder = context.Resolve<IModalsBuilder>();
            _playerManager = context.Resolve<IPlayerManager>();
            _socialManager = context.Resolve<ISocialManager>();
            _lockManager = context.Resolve<ILockManager>();
            _weeklyRewardManager = context.Resolve<WeeklyLeaderboardRewardsManager>();
            _weeklyLeaderboardManager = context.Resolve<WeeklyLeaderboardManager>();
            _brainCloudManager = context.Resolve<BrainCloudManager>();

            _cachedDeltas = new RanksDeltas();
            Subscribe();

            InitCategories();
        }

        private void InitCategories()
        {
            if (_filteredScoresDataByCategory != null) return;

            _allCategories.AddRange(_groupCategories);
            _filteredScoresDataByCategory = new(CategoriesCount);
            foreach (var category in _allCategories)
            {
                _filteredScoresDataByCategory[category] = IsGroupCategory(category) ? new CategoryScoresDataWithCaching<BCGroupLeaderboardData>() : new CategoryScoresDataWithCaching<BCPlayerLeaderboardData>();
            }
        }

        private bool IsGroupCategory(string category)
        {
            foreach (var groupCategory in _groupCategories)
            {
                if (category == groupCategory) return true;
            }

            return false;
        }

        private void Subscribe()
        {
            Unsubscribe();
            
            ConnectivityStatusManager.ConnectivityChanged += ConnectivityChanged;
            _accountManager.TeamChanged += ReloadTeamsData;
            _accountManager.TeamDataChanged += ReloadTeamsData;
            _accountManager.OnAvatarChanged += UpdateLocalPlayerAvatar;
            _accountManager.ProfileUpdated += OnProfileUpdated;
        }

        private void Unsubscribe()
        {
            ConnectivityStatusManager.ConnectivityChanged -= ConnectivityChanged;
            
            if (_accountManager == null) return;

            _accountManager.TeamChanged -= ReloadTeamsData;
            _accountManager.TeamDataChanged -= ReloadTeamsData;
            _accountManager.OnAvatarChanged -= UpdateLocalPlayerAvatar;
            _accountManager.ProfileUpdated -= OnProfileUpdated;
        }
        
        public bool IsTrophiesUnlocked()
        {
            return !_lockManager.IsLocked(LeaderboardConstants.TROPHIES_LOCK_ID, LockItemType.Quests);
        }

        public void UpdateLocalPlayerState(LocalPlayerUpdateData updateData, bool silentHandling)
        {
            if (updateData.Trophies <= 0) return;

            foreach (var category in _allCategories)
            {
                if (!IsGroupCategory(category)) continue;

                UpdateLocalPlayerScoreFor(_filteredScoresDataByCategory[category].Data, updateData);
            }

            if (!silentHandling)
            {
                RefreshRanksAndNotifyUI();
            }    
        }

        private void UpdateLocalPlayerAvatar()
        {
            foreach (var category in _allCategories)
            {
                var scoresData = _filteredScoresDataByCategory[category].Data;
                var index = -1;

                foreach (var scoreData in scoresData.GetScores())
                {
                    index++;

                    if (!scoreData.IsOwnPlayer) continue;

                    var newScoreData = scoreData;
                    newScoreData.Avatar = _accountManager.Profile.Avatar;
                    scoresData.Replace(newScoreData, index);
                    break;
                }
            }
        }

        public int GetOtherPlayerRankPosition(string category, string userId)
        {
            if (_filteredScoresDataByCategory == null || !_filteredScoresDataByCategory.TryGetValue(category, out var scoresData))
            {
                return -1;
            }

            var score = scoresData.Data.FindPlayerInScoresWithIndex(userId, out _);
            if (score.HasValue)
            {
                return score.Value.Position;
            }

            return -1;
        }

        public int GetOwnPlayerRankPosition(string category)
        {
            return _filteredScoresDataByCategory != null && _filteredScoresDataByCategory.TryGetValue(category, out var scoresData)
                ? scoresData.Data.UserPosition
                : 0;
        }

        public FilteredScoresData GetFilteredScoresData(string category)
        {
            return _filteredScoresDataByCategory[category].Data;
        }

        public bool HasWeeklyRewardForCurrentOwnPosition()
        {
            var position = GetOwnPlayerRankPosition(LeaderboardFilterTypes.PlayersWeekly);
            return position >= 0 && _weeklyRewardManager.GetRewardFor(position) != null;
        }

        /// <summary>
        /// Check if player is not last in scores list.
        /// </summary>
        private bool IsExistAnyPlayerAboveRankPosition(int rankPosition, string category)
        {
            if (!_filteredScoresDataByCategory.TryGetValue(category, out var filteredScores)) return false;

            var count = filteredScores.Data.ScoresCount();
            for (var i = 0; i < count; i++)
            {
                if (filteredScores.Data[i].Position > rankPosition) return true;

            }

            return false;
        }

        public void LoadScores()
        {
            foreach (var item in _filteredScoresDataByCategory.Values)
            {
                if (!item.Data.Loading) continue;
                
                BDebug.Log(LogCat.General, "Loading ranks start rejected: loading is already started");
                return;
            }
            if (!_loadingInvokingScreens.Contains(_screensManager.GetPreviousScreenType())) return;
            ReloadAllData(true);
        }

        private void SetAllScoresLoading(bool loading)
        {
            foreach (var categoryData in _filteredScoresDataByCategory.Values)
            {
                categoryData.Data.Loading = loading;
            }
        }
        
        private void SetCategoryScoreLoading(string category, bool loading)
        {
            if (_filteredScoresDataByCategory.TryGetValue(category, out var categoryData))
            {
                categoryData.Data.Loading = loading;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="affectUi">weather to fire event to update UI after the asynchronous state update is completed</param>
        /// <param name="reset">Reset is needed to clean the leaderboard if weekly ended. But if we are reloading the data, and have no time to refresh,
        /// it is better to keep at least some previous result</param>
        public void ReloadWeeklyData(bool affectUi)
        {
            SetCategoryScoreLoading(LeaderboardFilterTypes.PlayersWeekly, true);
            GetScoresAsync(LeaderboardCategory.LeaderboardCategoryWeekly, affectUi).Forget();
        }

        public void ReloadAllData(bool affectUi)
        {
            SetAllScoresLoading(true);
            GetScoresAsync(LeaderboardCategory.LeaderboardCategoryAll, affectUi).Forget();
        }

        public void ResetWeeklyData()
        {
            if (_filteredScoresDataByCategory[LeaderboardFilterTypes.PlayersWeekly] is CategoryScoresDataWithCaching<BCPlayerLeaderboardData> weeklyScoreData)
            {
                weeklyScoreData.Data = new FilteredScoresData();
                weeklyScoreData.RemoteData = null;
            }
            DbManager.DeleteDataById<BCPlayerLeaderboardData>(LeaderboardFilterTypes.PlayersWeekly);
        }

        private void ReloadTeamsData()
        {
            foreach (var category in _groupCategories)
            {
                _filteredScoresDataByCategory[category].Data = new FilteredScoresData
                {
                    Loading = true
                };
            }

            GetScoresAsync(categoryFilter: LeaderboardCategory.LeaderboardCategoryGlobal).Forget();
        }

        public bool HasData(string filter)
        {
            var data = _filteredScoresDataByCategory[filter].Data;
            return data != null && data.ScoresCount() > 0;
        }

        public bool HasValidDataInAnyCategory()
        {
            foreach (var filteredScoresData in _filteredScoresDataByCategory.Values)
            {
                if (filteredScoresData.Data is { Loading: false, UserPosition: >= 0 } && filteredScoresData.Data.ScoresCount() > 0) return true;

            }
            return false;
        }

        public RanksDeltas GetLastDeltas()
        {
            return _cachedDeltas;
        }

        public (string, int) GetCategoryBasedOnNotifications(string fallbackTab = LeaderboardFilterTypes.PlayersCountry)
        {
            var notifier = _notificationManager.GetLeaderboardNotifier(LeaderboardNotifier.NotifierType.PlayersWeekly);
            var notifierStatus = notifier.GetStatus();
            if (notifierStatus > 0) return (LeaderboardFilterTypes.PlayersWeekly, notifierStatus);

            notifier = _notificationManager.GetLeaderboardNotifier(LeaderboardNotifier.NotifierType.PlayersCountry);
            notifierStatus = notifier.GetStatus();
            if (notifierStatus > 0) return (LeaderboardFilterTypes.PlayersCountry, notifierStatus);

            notifier = _notificationManager.GetLeaderboardNotifier(LeaderboardNotifier.NotifierType.PlayersWorld);
            notifierStatus = notifier.GetStatus();
            if (notifierStatus > 0) return (LeaderboardFilterTypes.PlayersWorld, notifierStatus);

            notifier = _notificationManager.GetLeaderboardNotifier(LeaderboardNotifier.NotifierType.TeamsCountry);
            notifierStatus = notifier.GetStatus();
            if (notifierStatus > 0) return (LeaderboardFilterTypes.TeamsCountry, notifierStatus);

            notifier = _notificationManager.GetLeaderboardNotifier(LeaderboardNotifier.NotifierType.TeamsWorld);
            notifierStatus = notifier.GetStatus();
            if (notifierStatus > 0) return (LeaderboardFilterTypes.TeamsWorld, notifierStatus);

            return (fallbackTab, notifierStatus);
        }

        public void AutoShowLeaderboard(string tab = LeaderboardFilterTypes.PlayersCountry, ShowMode showMode = ShowMode.Delayed)
        {
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.AutoPopups.Name, DauInteractions.AutoPopups.GameEvent, CurrencyFlow.GameEvents.WeeklyLeaderboard));
            DauInteractions.TapOnAutoPopups.AwaitLogs(DauInteractions.TapOnAutoPopups.GameEventClick, DauInteractions.TapOnAutoPopups.GameEventClose);
            ShowLeaderboard(tab, showMode);
        }

        public void ShowLeaderboard(string tab = LeaderboardFilterTypes.PlayersCountry, ShowMode showMode = ShowMode.Delayed)
        {
            if (!IsTrophiesUnlocked()) 
                return;

            _leaderboardModalController = _modalsBuilder.CreateModalView<LeaderboardModalController>(ModalsType.LeaderboardModal);
            _leaderboardModalController.SetupCategory(tab);
            _leaderboardModalController.ShowModal(showMode);
        }

        public void HideLeaderboard()
        {
            if (_leaderboardModalController != null && _modalsManager.IsShowingModal(typeof(LeaderboardModalController)))
            {
                _leaderboardModalController.Hide();
            }
        }

        public void ClearLastSeenRanks(string filter)
        {
            if (_playerManager?.Player?.SocialInfo != null)
            {
                var socialInfo = _playerManager.Player.SocialInfo;
                socialInfo.SetLastSeenRank(filter, 0);
                socialInfo.AssignSeenToVisualized(filter);
            }

            _cachedDeltas.SetDelta(filter, 0);
        }

        private void ConnectivityChanged(bool reachable)
        {
            if (!reachable) return;

            ReloadAllData(false);
        }

        private void OnProfileUpdated(IPlayer _)
        {
            foreach (var item in _filteredScoresDataByCategory.Values)
            {
                item.Data.Clear();
            }
            ReloadAllData(false);
        }
        
        private async UniTask GetScoresAsync(LeaderboardCategory categoryFilter = LeaderboardCategory.LeaderboardCategoryGlobal, bool affectUi = true)
        {
            var joinedPeriodsParams = new Dictionary<string, RotatingLeaderboardPeriodParams>();
            var debugMode = false;
            
            var isGlobal = (categoryFilter & LeaderboardCategory.LeaderboardCategoryGlobal) != 0;
            var isWeekly = (categoryFilter & LeaderboardCategory.LeaderboardCategoryWeekly) != 0;
            
            _playerFiltersToSend.Clear();
            var groupFiltersToSend = Array.Empty<string>();
            if (isGlobal)
            {
                _playerFiltersToSend.Add(LeaderboardFilterTypes.PlayersWorld);
                _playerFiltersToSend.Add(LeaderboardFilterTypes.PlayersCountry);
                
                groupFiltersToSend = _groupCategories;
            }
            
            if (isWeekly)
            {
                _playerFiltersToSend.Add(LeaderboardFilterTypes.PlayersWeekly);
            }
            
            if (!ConnectivityStatusManager.ConnectivityReachable)
            {
                TryToLoadFromCache(_playerFiltersToSend, groupFiltersToSend, joinedPeriodsParams, affectUi);
                return;
            }

            if (isWeekly)
            {
                await _weeklyLeaderboardManager.GetWeeklyLeaderboardConfigAsync();
                var weeklyLeaderboardId = _weeklyLeaderboardManager.GetWeeklyLeaderboardUidForBrainCloud();
                debugMode = _weeklyLeaderboardManager.DebugMode;
                BDebug.Log(LogCat.Leaderboards, "LB DEBUG: GetScoresAsync: wlb id = " + weeklyLeaderboardId);
                var parasOfLastJoined = _weeklyLeaderboardManager.ParamsLastJoinedPeriod();
                joinedPeriodsParams[weeklyLeaderboardId] = parasOfLastJoined;
                BDebug.Log(LogCat.Leaderboards, "LB DEBUG: GetScoresAsync: endDateOfLastJoined = " + parasOfLastJoined.EndTimeOfLastJoinedPeriod);
                BDebug.Log(LogCat.Leaderboards, "LB DEBUG: GetScoresAsync: tournamentCode = " + parasOfLastJoined.TournamentCodeOfLastJoinedPeriod);
            }
            
            var uniTaskCompletionSource = new UniTaskCompletionSource();
            _brainCloudManager.GetLeaderboards(_playerFiltersToSend, groupFiltersToSend, debugMode, joinedPeriodsParams,
                data =>
                {
                    BDebug.Log(LogCat.General, "leaderboard fetch debug log: " + data.DebugLog);
                    BDebug.Log(LogCat.Leaderboards, "LB DEBUG: GetScoresAsync: response = " + JsonConvert.SerializeObject(data));
                    GotAtLeastOneResponse = true;

                    var playerLeaderboards = data.Response.PlayerLeaderboards;
                    var groupLeaderboards = data.Response.GroupLeaderboards;
                    ProcessRemoteLeaderboards(playerLeaderboards, groupLeaderboards, joinedPeriodsParams, affectUi);
                    SaveLeaderboards(playerLeaderboards, groupLeaderboards);
                    
                    uniTaskCompletionSource.TrySetResult();
                }, () =>
                {
                    SetAllScoresLoading(false);
                    uniTaskCompletionSource.TrySetResult();
                });

            await uniTaskCompletionSource.Task;
        }

        private void TryToLoadFromCache(List<string> playerFilters, string[] groupFilters, Dictionary<string, RotatingLeaderboardPeriodParams> joinedPeriodsParams, bool affectUi)
        {
            var playerLeaderboards = new Dictionary<string, BCPlayerLeaderboardData>();
            var groupLeaderboards = new Dictionary<string, BCGroupLeaderboardData>();
            foreach (var category in playerFilters)
            {
                if (_filteredScoresDataByCategory[category] is not CategoryScoresDataWithCaching<BCPlayerLeaderboardData> categoryData) continue;

                if (!categoryData.Loaded && categoryData.Data.GetScores().IsNullOrEmpty())
                {
                    categoryData.Loaded = true;
                    categoryData.RemoteData = LoadLeaderboardFromCache<BCPlayerLeaderboardData>(category);
                }
                playerLeaderboards.Add(category, categoryData.RemoteData);
            }
            foreach (var category in groupFilters)
            {
                if (_filteredScoresDataByCategory[category] is not CategoryScoresDataWithCaching<BCGroupLeaderboardData> categoryData) continue;

                if (!categoryData.Loaded && categoryData.Data.GetScores().IsNullOrEmpty())
                {
                    categoryData.Loaded = true;
                    categoryData.RemoteData = LoadLeaderboardFromCache<BCGroupLeaderboardData>(category);
                }
                groupLeaderboards.Add(category, categoryData.RemoteData);
            }
            ProcessRemoteLeaderboards(playerLeaderboards, groupLeaderboards, joinedPeriodsParams, affectUi);
        }

        private T LoadLeaderboardFromCache<T>(string category) where T: RealmObject
        {
            try 
            {
                return DbManager.LoadDataById<T>(category);
            }
            catch (Exception e)
            {
                BDebug.LogError(LogCat.General, $"Error while loading leaderboard {category}: {e}");
            }
            return null;
        }
        
        private void SaveLeaderboards( Dictionary<string, BCPlayerLeaderboardData> playerLeaderboards, Dictionary<string, BCGroupLeaderboardData> groupLeaderboards)
        {
            foreach (var (category, leaderboardData) in playerLeaderboards)
            {
                if (_filteredScoresDataByCategory[category] is not CategoryScoresDataWithCaching<BCPlayerLeaderboardData> categoryData
                    || (categoryData.RemoteData != null && categoryData.RemoteData.Equals(leaderboardData))) continue;

                SavePlayerLeaderboard(category, leaderboardData, categoryData);
            }
            foreach (var (category, leaderboardData) in groupLeaderboards)
            {
                if (_filteredScoresDataByCategory[category] is not CategoryScoresDataWithCaching<BCGroupLeaderboardData> categoryData
                    || (categoryData.RemoteData != null && categoryData.RemoteData.Equals(leaderboardData))) continue;

                SaveGroupLeaderboard(category, leaderboardData, categoryData);
            }
        }

        private void SavePlayerLeaderboard(string category, BCPlayerLeaderboardData leaderboard, CategoryScoresDataWithCaching<BCPlayerLeaderboardData> categoryData)
        {
            categoryData.RemoteData = leaderboard;
            categoryData.Loaded = true;
            try
            {
                leaderboard.Category = category;
                DbManager.SaveData(leaderboard);
            }
            catch (Exception e)
            {
                BDebug.LogError(LogCat.General, $"Error while saving leaderboard {category}: {e}");
            }
        }
        
        private void SaveGroupLeaderboard(string category, BCGroupLeaderboardData leaderboard, CategoryScoresDataWithCaching<BCGroupLeaderboardData> categoryData)
        {
            categoryData.RemoteData = leaderboard;
            categoryData.Loaded = true;
            try
            {
                leaderboard.Category = category;
                DbManager.SaveData(leaderboard);
            }
            catch (Exception e)
            {
                BDebug.LogError(LogCat.General, $"Error while saving leaderboard {category}: {e}");
            }
        }

        private void ProcessRemoteLeaderboards(
            Dictionary<string, BCPlayerLeaderboardData> playerLeaderboards,
            Dictionary<string, BCGroupLeaderboardData> groupLeaderboards,
            Dictionary<string, RotatingLeaderboardPeriodParams> joinedPeriodsParams,
            bool affectUi)
        {
            if (!playerLeaderboards.IsNullOrEmpty())
            {
                foreach (var (category, leaderboardData) in playerLeaderboards)
                {
                    var isLoading = _filteredScoresDataByCategory[category].Data.Loading;
                    SetCategoryScoreLoading(category, false);
                    
                    if (category == LeaderboardFilterTypes.PlayersWeekly && !TryHandleWeeklyLeaderboardData(leaderboardData)) continue;

                    if (leaderboardData == null || !CanProcessRemoteData(category, leaderboardData.PeriodNotFound, isLoading, joinedPeriodsParams)) continue;

                    ProcessRemotePlayerLeaderboard(category, leaderboardData);
                }
            }

            if (!groupLeaderboards.IsNullOrEmpty())
            {
                foreach (var (category, leaderboardData) in groupLeaderboards)
                {
                    var isLoading = _filteredScoresDataByCategory[category].Data.Loading;
                    SetCategoryScoreLoading(category, false);
                    
                    if (leaderboardData == null || !CanProcessRemoteData(category, leaderboardData.PeriodNotFound, isLoading, joinedPeriodsParams)) continue;

                    ProcessRemoteGroupLeaderboard(category, leaderboardData);
                }
            }

            var trophies = GetWeeklyTrophiesScoreInternal();
            _weeklyLeaderboardManager.SetScore(trophies);
            if (affectUi)
            {
                RefreshRanksAndNotifyUI();
            }
        }

        private bool TryHandleWeeklyLeaderboardData(BCPlayerLeaderboardData leaderboardData)
        {
            if (leaderboardData == null) return false;

            var payoutRules = leaderboardData.PayoutRules;
            _weeklyRewardManager.UpdateRewards(payoutRules);

            if (!leaderboardData.PeriodNotFound) return true;

            ResetWeeklyData();
            _weeklyLeaderboardManager.Reset();
            
            if (ConnectivityStatusManager.ConnectivityReachable)
            {
                ReloadWeeklyData(true);
            }

            return false;
        }

        private void ProcessRemotePlayerLeaderboard(string category,
            BCPlayerLeaderboardData leaderboardData)
        {
            var data = _filteredScoresDataByCategory[category].Data;
            data.Clear();

            var playerLeaderboards = leaderboardData.Leaderboard;
            if (playerLeaderboards.IsNullOrEmpty()) return;
            
            var hasOwnPlayerEntry = false;
            SocialUserInfo ownPlayerInfo = null;
            var isWeeklyCategory = category == LeaderboardFilterTypes.PlayersWeekly;
            var playerTrophies = isWeeklyCategory ? _weeklyLeaderboardManager.WeeklyTrophiesScore : _accountManager.Profile.Trophies;
            if (playerTrophies > 0)
            {
                ownPlayerInfo = _accountManager.Profile.ToSocialUserInfo(playerTrophies);
                foreach (var user in playerLeaderboards)
                {
                    if (user.PlayerId != OwnUserUid) continue;
                    
                    hasOwnPlayerEntry = true;
                    break;
                }
            }
            
            int currentRank;
            var highestPositionNumber = 0;
            var rankOffset = 0;
            for (var i = 0; i < playerLeaderboards.Count; i++)
            {
                var user = playerLeaderboards[i];
                
                currentRank = user.Rank + rankOffset;
                highestPositionNumber = Math.Max(highestPositionNumber, currentRank);
                SocialUserInfo currentPlayerInfo;
                
                var isOwnPlayer = user.PlayerId == OwnUserUid;
                
                if (!isOwnPlayer && !hasOwnPlayerEntry && ownPlayerInfo != null && ownPlayerInfo.Trophies >= user.Score)
                {
                    isOwnPlayer = true;
                    hasOwnPlayerEntry = true;
                    currentPlayerInfo = ownPlayerInfo;
                    rankOffset = 1;
                    i--;
                }
                else
                {
                    currentPlayerInfo = user.ToSocialUserInfo();
                    if (isOwnPlayer && !isWeeklyCategory && playerTrophies > currentPlayerInfo.Trophies)
                    {
                        BDebug.Log(LogCat.Leaderboards,$"Player scores received from server: {currentPlayerInfo.Trophies} are less than actual: {playerTrophies}");
                        currentPlayerInfo.Trophies = playerTrophies;
                    }
                }

                var score = currentPlayerInfo.ToScoreData(currentRank, isOwnPlayer);
                data.Add(score);
            }

            if (hasOwnPlayerEntry || ownPlayerInfo == null) return;

            currentRank = highestPositionNumber + 1;
            var ownScore = ownPlayerInfo.ToScoreData(currentRank, true);
            data.Add(ownScore);
        }

        private void ProcessRemoteGroupLeaderboard(string category,
            BCGroupLeaderboardData leaderboardData)
        {
            var data = _filteredScoresDataByCategory[category].Data;
            data.Clear();

            var groupLeaderboards = leaderboardData.Leaderboard;
            if (groupLeaderboards.IsNullOrEmpty()) return;

            var hasOwnGroupEntry = false;
            SocialTeamInfo ownTeamInfo = null;
            if (_accountManager.IsInTeam)
            {
                ownTeamInfo = _accountManager.Profile.CurrentTeam.ToSocialTeamInfo();
                foreach (var group in groupLeaderboards)
                {
                    if (ownTeamInfo.Uid != group.GroupId) continue;
                    
                    hasOwnGroupEntry = true;
                    break;
                }
            }

            int currentRank;
            var highestPositionNumber = 0;
            var rankOffset = 0;
            for (var i = 0; i < groupLeaderboards.Count; i++)
            {
                var group = groupLeaderboards[i];
                
                currentRank = group.Rank + rankOffset;
                highestPositionNumber = Math.Max(highestPositionNumber, currentRank);
                SocialTeamInfo currentTeamInfo;

                var isOwnGroup = ownTeamInfo?.Uid == group.GroupId;
                if (!isOwnGroup && !hasOwnGroupEntry && ownTeamInfo != null && ownTeamInfo.Trophies >= group.Score)
                {
                    isOwnGroup = true;
                    hasOwnGroupEntry = true;
                    currentTeamInfo = ownTeamInfo;
                    rankOffset = 1;
                    i--;
                }
                else
                {
                    currentTeamInfo = group.ToSocialTeamInfo();
                }
                
                var score = currentTeamInfo.ToScoreData(currentRank, isOwnGroup);
                data.Add(score);
            }

            if (hasOwnGroupEntry || ownTeamInfo == null) return;

            // TODO: Shall we send this from the server?
            currentRank = highestPositionNumber + 1;
            var ownGroupScore = ownTeamInfo.ToScoreData(currentRank, true);
            data.Add(ownGroupScore);
        }

        private bool CanProcessRemoteData(string category, bool periodNotFound, bool isLoading,
            Dictionary<string, RotatingLeaderboardPeriodParams> joinedPeriodsParams)
        {
            if (joinedPeriodsParams.TryGetValue(category, out var periodParams) &&
                periodParams.EndTimeOfLastJoinedPeriod != 0 && periodNotFound)
            {
                BDebug.Log(LogCat.Leaderboards,
                    $"LB DEBUG: GetScoresAsync: response: for leaderboard {category} period not found in the server, period end date = {periodParams.EndTimeOfLastJoinedPeriod}");
                return false;
            }

            if (isLoading) return true;

            BDebug.Log(LogCat.Leaderboards,
                $"LB DEBUG: GetScoresAsync: response: for leaderboard {category} data is not loading");
            return false;
        }

        private void RefreshRanksAndNotifyUI()
        {
            if (!_socialManager.IsSocialUnlocked()) return;

            var socialInfo = _playerManager.Player.SocialInfo;
            foreach (var category in _allCategories)
            {
                var rank = GetOwnPlayerRankPosition(category);
                if (rank > 0)
                {
                    socialInfo.SetLastSeenRank(category, rank);
                }

                if (!IsExistAnyPlayerAboveRankPosition(socialInfo.GetLastSeenRank(category), category)) continue;

                var difference = socialInfo.GetVisualizedSeenRankDif(category);
                _cachedDeltas.SetDelta(category, difference);
                    
                if (difference == 0) continue;

                var leaderboardRankChangedEvent = _eventDispatcher.GetMessage<LeaderboardRankChangedEvent>();
                leaderboardRankChangedEvent.Set(LeaderboardFilterTypes.CategoryToNotifierType(category));
                _eventDispatcher.TriggerEvent(leaderboardRankChangedEvent);
            }
        }

        private void UpdateLocalPlayerScoreFor(FilteredScoresData data, LocalPlayerUpdateData updateData)
        {
            var newTrophies = updateData.Trophies;
            var lastIndex = data.ScoresCount() - 1;
            var newExpectedOwnIndex = lastIndex;
            var oldOwnIndex = -1;

            Score? ownScoreInstance = null;
            for (var i = lastIndex; i >= 0; i--)
            {
                var scoreInstance = data[i];

                if (newTrophies > scoreInstance.Trophies)
                {
                    newExpectedOwnIndex = i;
                }

                if (!scoreInstance.IsOwnPlayer || oldOwnIndex != -1) continue;

                oldOwnIndex = i;
                ownScoreInstance = scoreInstance;
            }

            if (ownScoreInstance == null) return;

            var ownScoreValue = ownScoreInstance.Value;

            data.UserTrophies = newTrophies;

            ownScoreValue.Trophies = newTrophies;
            ownScoreValue.LastLocation = updateData.LastLocation;
            ownScoreValue.LastLevel = updateData.HighestLevel;

            var positionUpdated = false;
            if (newExpectedOwnIndex != lastIndex && newExpectedOwnIndex != oldOwnIndex)
            {
                data.UserPosition = newExpectedOwnIndex + 1;
                ownScoreValue.Position = data.UserPosition;
                positionUpdated = true;
            }

            data.Replace(ownScoreValue, oldOwnIndex);

            if (!positionUpdated) return;
            
            data.SortScores((a, b) => -a.Trophies.CompareTo(b.Trophies));
            var lastIndexToIterate = newExpectedOwnIndex != lastIndex ? lastIndex : lastIndex - 1;
            for (var i = 0; i <= lastIndexToIterate; i++)
            {
                var score = data[i];
                score.Position = i + 1;
                data.Replace(score, i);
            }
        }

        public void ResetLeaderboards()
        {
            DbManager.DropAllCollections<BCPlayerLeaderboardData>();
            DbManager.DropAllCollections<BCGroupLeaderboardData>();
        }

        public void Restart()
        {
            Unsubscribe();
        }
    }
}