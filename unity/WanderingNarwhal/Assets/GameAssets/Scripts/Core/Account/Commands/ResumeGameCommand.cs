using BebopBee;

namespace Core.Account.Commands
{
    class ResumeGameCommand : AccountCommandBase
    {
        private readonly string _sessionId;
        private readonly string _currentTrivia;
        private readonly bool _resetTrivia;

        public ResumeGameCommand(string sessionId, string currentTrivia, bool resetTrivia)
        {
            _sessionId = sessionId;
            _currentTrivia = currentTrivia;
            _resetTrivia = resetTrivia;
        }

        public override void Execute(IAccountManager instance)
        {
            instance.SetTrivia(_currentTrivia, _resetTrivia);
        }
    }
}