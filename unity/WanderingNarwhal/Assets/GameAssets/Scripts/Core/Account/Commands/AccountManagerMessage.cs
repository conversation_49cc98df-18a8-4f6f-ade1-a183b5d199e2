using Bebopbee.Core.Systems.GamemessengerBase;
using BebopBee;

namespace Core.Account.Commands
{
    public class AccountManagerMessage : IGameMessage
    {
        private readonly AccountCommandBase _command;

        public AccountManagerMessage(AccountCommandBase command)
        {
            _command = command;
        }

        public void Apply(IAccountManager accountManager)
        {
            _command.Execute(accountManager);
        }
        public bool IsValid()
        {
            return _command != null;
        }
    }
}