using System.Collections.Generic;
using System;

namespace BebopBee.Social
{
    public class MatchInviteData
    {
        public MatchInviteData()
        {
            Region = -1;
            GameArena = -1;
            RoomName = null;
            SentTime = 0;
        }

        public MatchInviteData(int region, string roomName, int gameArena, double sentTime)
        {
            Region = region;
            GameArena = gameArena;
            RoomName = roomName;
            SentTime = sentTime;
        }

        public int Region { get; private set; }
        public int GameArena { get; private set; }
        public string RoomName { get; private set; }
        public double SentTime { get; private set; }

        public bool IsValid
        {
            get { return Region != -1 && GameArena != -1 && !string.IsNullOrEmpty(RoomName); }
        }

        public override string ToString()
        {
            return string.Format("region={0} arena={1} roomName={2}", Region, GameArena, RoomName);
        }

        public Dictionary<string, object> Serialize()
        {
            var data = new Dictionary<string, object>();
            data.Add(SocialRequestKeys.Region, Region);
            data.Add(SocialRequestKeys.GameArena, GameArena);
            data.Add(SocialRequestKeys.RoomName, RoomName);
            data.Add(SocialRequestKeys.SentTime, SentTime);
            return data;
        }

        public void Deserialize(Dictionary<string, object> data)
        {
            Region = Convert.ToInt32(data[SocialRequestKeys.Region]);
            GameArena = Convert.ToInt32(data[SocialRequestKeys.GameArena]);
            RoomName = Convert.ToString(data[SocialRequestKeys.RoomName]);
            SentTime = Convert.ToDouble(data[SocialRequestKeys.SentTime]);
        }
    }

}