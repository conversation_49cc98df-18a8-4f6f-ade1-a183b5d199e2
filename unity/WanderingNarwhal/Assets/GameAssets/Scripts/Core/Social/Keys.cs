namespace BebopBee.Social
{
    public static class SocialRequestKeys
    {
        public const string Uid = "uid";
        public const string FromPlayerId = "from_uid";
        public const string FromName = "from_name";
        public const string FromAvatar = "from_avatar";
        public const string FromCountry = "from_country";
        public const string Type = "type";
        public const string SentTime = "sent_time";
        public const string Region = "region";
        public const string RoomName = "room_name";
        public const string GameArena = "game_arena";
        public const string Title = "title";
        public const string Message = "message";
        public const string CreatedAt = "created_at";
        public const string Recruited = "recruited";
        public const string GiftType = "gift_type";
        public const string GiftPackId = "gift_pack_id";
        public const string CustomData = "data";
        public const string IapPackUid = "iap_uid";
        public const string Reward = "reward";
        public const string POiUid = "poi_uid";
    }

    //use lower case in string values
    public static class SocialRequestTypes
    {
        public const string Invite = "invite";
        public const string MatchInvite = "matchinvite";
        public const string PoiShare = "poishare";
        public const string BuddyGift = "buddygift";
        public const string GenericGift = "gift";
    }

    public static class SocialConstants
    {
        public const string AvatarsTag = "Avatars";
        public const string FlagTag = "Flags";
        public const string Teams = "Teams";
        public const string Attachments = "Attachments";
    }
}
