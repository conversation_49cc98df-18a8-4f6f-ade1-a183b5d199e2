using System;
using System.Collections.Generic;
using BBB.UI;
using BebopBee.Social;
using System.Collections;
using UnityEngine;

namespace BBB.Core.Social
{
    public class InboxManager
    {
        private readonly List<IInboxProfile> _inboxList = new();
        private readonly List<IInboxProfile> _friendshipList = new();
        private readonly List<IInboxProfile> _giftLivesList = new();
        private readonly IEventDispatcher _dispatcher;
        private readonly ICoroutineExecutor _coroutineExecutor;

        private Coroutine _delayedRefreshRoutine;

        public InboxManager(IEventDispatcher dispatcher, ICoroutineExecutor coroutineExecutor)
        {
            _dispatcher = dispatcher;
            _coroutineExecutor = coroutineExecutor;
        }

        private InboxItem CreateBaseInboxItem(PlayerComEventBase inboxEvent)
        {
            var inboxItem = new InboxItem
            {
                Title = inboxEvent.Title,
                Message = inboxEvent.Message,
                Name = inboxEvent.FromName,
                Avatar = inboxEvent.FromAvatar,
                Country = inboxEvent.FromCountry,
                EventData = inboxEvent,
                Uid = inboxEvent.FromPlayerId,
            };
            return inboxItem;
        }

        [Obsolete("Obsolete")]
        private void AddToInbox(IInboxProfile inboxItem)
        {
            var isDuplicated = false;

            foreach (var elem in _inboxList)
            {
                if (elem.UserAction != inboxItem.UserAction || elem.EventData.FromPlayerId != inboxItem.EventData.FromPlayerId) continue;
                
                isDuplicated = true;
                break;
            }

            if (!isDuplicated)
            {
                _inboxList.Add(inboxItem);
            }
            
            switch (inboxItem.UserAction)
            {
                case UserActionType.AcceptGift:
                case UserActionType.SendGift:
                case UserActionType.SendLife:
                case UserActionType.AcceptLife:
                case UserActionType.AcceptBuddyGift:
                {
                    var existsInGiftLivesList = false;
                    foreach (var elem in _giftLivesList)
                    {
                        if (elem.UserAction != inboxItem.UserAction || elem.EventData.FromPlayerId != inboxItem.EventData.FromPlayerId) continue;
                        
                        existsInGiftLivesList = true;
                        break;
                    }

                    if (!existsInGiftLivesList)
                    {
                        _giftLivesList.Add(inboxItem);
                    }

                    break;
                }
    
                case UserActionType.AcceptFriendShip:
                case UserActionType.FriendRequestAccepted:
                {
                    var existsInFriendshipList = false;
                    foreach (var elem in _friendshipList)
                    {
                        if (elem.UserAction != inboxItem.UserAction || elem.EventData.FromPlayerId != inboxItem.EventData.FromPlayerId) continue;
                        
                        existsInFriendshipList = true;
                        break;
                    }

                    if (!existsInFriendshipList)
                    {
                        _friendshipList.Add(inboxItem);
                    }

                    break;
                }
            }

            if (_dispatcher == null || inboxItem.EventData == null || inboxItem.Uid.IsNullOrEmpty()) return;
            
            // Send event to notifier system, which will update UI notification badge.
            var newInboxItemEvent = _dispatcher.GetMessage<NewInboxItemEvent>();
            newInboxItemEvent.Set(inboxItem.EventData.CreationTimestamp, inboxItem.UserAction);
            _dispatcher.TriggerEvent(newInboxItemEvent);

            // This can be called multiple times during one frame,
            // so we need to invoke event only once
            // with help of coroutine.
            _delayedRefreshRoutine ??= _coroutineExecutor.StartCoroutine(LateRefreshInvoke());
        }

        private IEnumerator LateRefreshInvoke()
        {
            yield return null;

            _delayedRefreshRoutine = null;
        }

        [Obsolete("Obsolete")]
        public void AddInboxItem(GiftReceivedEvent inboxEvent)
        {
            if (inboxEvent == null) return;
            var inboxItem = CreateBaseInboxItem(inboxEvent);

            inboxItem.UserAction = inboxEvent.GiftType switch
            {
                GiftType.Gift => UserActionType.AcceptGift,
                GiftType.Life => UserActionType.AcceptLife,
                _ => inboxItem.UserAction
            };

            AddToInbox(inboxItem);
        }

        [Obsolete("Obsolete")]
        public void AddInboxItem(GiftRequestEvent inboxEvent)
        {
            if (inboxEvent == null) return;
            
            var inboxItem = CreateBaseInboxItem(inboxEvent);
            inboxItem.UserAction = UserActionType.SendGift;
            AddToInbox(inboxItem);
        }

        [Obsolete("Obsolete")]
        public void AddInboxItem(FriendshipAcceptedEvent inboxEvent)
        {
            if (inboxEvent == null) return;
            
            var inboxItem = CreateBaseInboxItem(inboxEvent);
            inboxItem.UserAction = UserActionType.FriendRequestAccepted;
            AddToInbox(inboxItem);
        }

        [Obsolete("Obsolete")]
        public void AddInboxItem(FriendshipRequestedEvent inboxEvent)
        {
            if (inboxEvent == null) return;
            
            var inboxItem = CreateBaseInboxItem(inboxEvent);
            inboxItem.UserAction = UserActionType.AcceptFriendShip;
            AddToInbox(inboxItem);
        }

        [Obsolete("Obsolete")]
        public void AddInboxItem(BuddyGiftReceivedEvent inboxEvent)
        {
            if (inboxEvent == null) return;
            
            var inboxItem = CreateBaseInboxItem(inboxEvent);
            inboxItem.UserAction = UserActionType.AcceptBuddyGift;
            AddToInbox(inboxItem);
        }

        public List<IInboxProfile> GetInbox()
        {
            return _inboxList;
        }
        
    }
}