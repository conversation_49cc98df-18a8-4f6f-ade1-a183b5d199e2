using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using RPC.Command;

namespace BebopBee.Social
{
    public class PlayerComEventsManager
    {
        private readonly Dictionary<PlayerComEventType, IPlayerComEventHandler> _eventTypeToHandlerMap 
                         = new Dictionary<PlayerComEventType, IPlayerComEventHandler>();

        private readonly IRPCService _rpcService;
        
        public PlayerComEventsManager(IRPCService rpcService)
        {
            _rpcService = rpcService;
        }

        public void AddEventHandler(PlayerComEventType playerComEventType, IPlayerComEventHandler handler)
        {
            _eventTypeToHandlerMap.Add(playerComEventType, handler);
        }

        public void SendEvent(PlayerComEventBase playerComEventToSend, bool executeRequestImmediately, Action<bool> onDone, params string[] sendToUids)
        {
            SendEvents(new List<PlayerComEventBase>{ playerComEventToSend }, executeRequestImmediately, onDone, sendToUids: sendToUids);
        } 

        private void SendEvents(List<PlayerComEventBase> itemEventsToSend, bool executeRequestImmediately, Action<bool> onDone, params string[] sendToUids)
        {
            bool isCallbackAttached = false;
            foreach (var itemEvent in itemEventsToSend)
            {
                var events = new Dictionary<string, Dictionary<string, string>>();
                var eventData = itemEvent.Serialize();

                foreach (var uid in sendToUids)
                    events[uid] = eventData;

                var typeName = itemEvent.EventType.ToString();

                RPCRequest.ResponseCallback callback = null;
                if (!isCallbackAttached)
                {
                    // Callback must be attached to only one request from the group.
                    isCallbackAttached = true;
                    callback           = (resp) => onDone?.Invoke(resp.Success);
                }
                var request = RPCFactory.SendEvents(
                    typeName,
                    events,
                    callback: callback);
                _rpcService.AddRequest(request);
            }

            if (executeRequestImmediately)
            {
                _rpcService.ExecuteRequests();
            }
        }

        public void GetAllMessages(Action<bool> callback = null)
        {
            GetEvents("INBOX", callback);
        }

        public void GetEventsOfType(Type eventType, Action<bool> callback = null)
        {
            var eventTypeStr = string.Format("INBOX.{0}", eventType.Name);
            GetEvents(eventTypeStr, callback);
        }

        private void GetEvents(string eventTypeStr, Action<bool> callback)
        {
            var request = RPCFactory.GetInbox(eventTypeStr, false, respose =>
            {
                if (respose.Success)
                    ProcessGetInboxResponse(respose);
                else
                   BDebug.LogErrorFormat(LogCat.Social, "Request for inbox events of type {0} failed", eventTypeStr);

                callback.SafeInvoke(respose.Success);
            });

            _rpcService.AddRequest(request);
            _rpcService.ExecuteRequests();
        }

        private void ProcessGetInboxResponse(RPCResponse response)
        {
            if (response.Status == ResultType.NoInternet) return;

            if (response.Result is not List<object> inboxResult) return;

            foreach (Dictionary<string, object> eventDict in inboxResult)
            {
                var eventObject = PlayerComEventBase.DeserializeEvent(eventDict);

                if (eventObject == null) continue;

                _eventTypeToHandlerMap.TryGetValue(eventObject.EventType, out var handler);

                if(handler != null)
                    handler.HandleEvent(eventObject);
                else
                   BDebug.LogErrorFormat(LogCat.Social, "Handler not found for event of type {0}", eventObject.EventType);
            }
        }
    }
}

