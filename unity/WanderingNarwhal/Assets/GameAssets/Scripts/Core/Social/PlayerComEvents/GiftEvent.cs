using System;
using System.Collections.Generic;
using BBB.MiniJSON;

namespace BebopBee.Social
{
    public enum GiftType
    {
        Life,
        FreeSpin,
        Gift,
    }

    public abstract class GiftEvent : PlayerComEventBase
    {
        public GiftType GiftType { get; protected set; }
        public string GiftPackId { get; protected set; }

        protected override void DeserializeData(Dictionary<string, object> data)
        {
            base.DeserializeData(data);
            var giftTypeString = data.GetSafe(SocialRequestKeys.GiftType).ToString();
            GiftType = (GiftType)Enum.Parse(typeof(GiftType), giftTypeString);
            
            GiftPackId = data.GetSafe(SocialRequestKeys.GiftPackId).ToString();
        }

        public override Dictionary<string, string> Serialize()
        {
            var data = base.Serialize();
            var eventData = new Dictionary<string, object>
            {
                {SocialRequestKeys.GiftType, GiftType},
                {SocialRequestKeys.GiftPackId, GiftPackId},
            };
            data[SocialRequestKeys.CustomData] = Json.Serialize(eventData);
            return data;
        }
    }
    public class GiftReceivedEvent : GiftEvent
    {
        public GiftReceivedEvent()
        {
        }

        public GiftReceivedEvent(GiftType giftType, string giftPackUid = "")
        {
            GiftType = giftType;
            GiftPackId = giftPackUid;
        }

        public override PlayerComEventType EventType
        {
            get { return GiftType == GiftType.Life ? PlayerComEventType.LifeReceived : PlayerComEventType.GiftReceived; }
        }
    }

    public class BuddyGiftReceivedEvent : PlayerComEventBase
    {
        public string IapPackUid { get; protected set; }

        public override PlayerComEventType EventType
        {
            get { return PlayerComEventType.BuddyGiftReceived; }
        }

        public BuddyGiftReceivedEvent()
        {
        }

        public BuddyGiftReceivedEvent(string iapUid)
        {
            IapPackUid = iapUid;
        }

        protected override void DeserializeData(Dictionary<string, object> data)
        {
            base.DeserializeData(data);
            IapPackUid = data.GetSafe(SocialRequestKeys.IapPackUid).ToString();
        }

        public override Dictionary<string, string> Serialize()
        {
            var data = base.Serialize();
            var eventData = new Dictionary<string, object> { { SocialRequestKeys.IapPackUid, IapPackUid }, };
            data[SocialRequestKeys.CustomData] = Json.Serialize(eventData);
            data[SocialRequestKeys.FromPlayerId] = Json.Serialize(FromPlayerId);
            return data;
        }
    }

    public class GiftRequestEvent : GiftEvent
    {
        public GiftRequestEvent()
        {
        }
        
        public GiftRequestEvent(GiftType giftType, string giftPackUid = "")
        {
            GiftType = giftType;
            GiftPackId = giftPackUid;
        }

        public override PlayerComEventType EventType
        {
            get { return PlayerComEventType.GiftRequest; }
        }
    }
}

