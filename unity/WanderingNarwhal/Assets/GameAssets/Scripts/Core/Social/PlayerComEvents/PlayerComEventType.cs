namespace BebopBee.Social
{
    //DO NOT RENAME VALUES IN PRODUCTION
    public enum PlayerComEventType
    {
        /// <summary>
        /// New friend requested.
        /// </summary>
        Invite,

        /// <summary>
        /// Friend accepted friendship request.
        /// </summary>
        FriendshipAccepted,

        MatchInvitation,
        LifeReceived,
        GiftReceived,
        GiftRequest,
        BuddyGiftReceived,
    }
}