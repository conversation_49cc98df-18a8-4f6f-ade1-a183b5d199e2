using System.Collections.Generic;
using BBB.MiniJSON;

namespace BebopBee.Social
{
    public class FriendshipAcceptedEvent : PlayerComEventBase
    {
        public bool Recruited { get; private set; }

        public FriendshipAcceptedEvent()
        {}

        public FriendshipAcceptedEvent(bool recruited)
        {
            Recruited = recruited;
        }

        public override PlayerComEventType EventType
        {
            get { return PlayerComEventType.FriendshipAccepted; }
        }

        public override Dictionary<string, string> Serialize()
        {
            var data = base.Serialize();
            var eventData = new Dictionary<string, object> {{SocialRequestKeys.Recruited, Recruited}};
            data[SocialRequestKeys.CustomData] = Json.Serialize(eventData);

            return data;
        }

        protected override void DeserializeData(Dictionary<string, object> data)
        {
            Recruited = (bool)data.Get(SocialRequestKeys.Recruited, false);
        }
    }
}