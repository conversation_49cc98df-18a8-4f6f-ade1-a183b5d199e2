using System.Collections.Generic;
using UnityEngine;
using System;
using BBB;
using BBB.Core;
using BBB.MiniJSON;

namespace BebopBee.Social
{
    public abstract class PlayerComEventBase
    {
        private static readonly Dictionary<string, Type> _eventStringToTypeMap = new Dictionary<string, Type>
        {
            { "Invite", typeof(FriendshipRequestedEvent) },
            { "FriendshipAccepted", typeof(FriendshipAcceptedEvent) },
            { "MatchInvitation", typeof(MatchInvitationEvent) },
            { "LifeReceived", typeof(GiftReceivedEvent) },
            { "GiftReceived", typeof(GiftReceivedEvent) },
            { "GiftRequest", typeof(GiftRequestEvent) },
            { "BuddyGiftReceived", typeof(BuddyGiftReceivedEvent) },
        };

        public abstract PlayerComEventType EventType { get; }
        public long CreationTimestamp { get; private set; }
        public string FromPlayerId { get; set; }
        public string FromAvatar { get; set; }
        public string FromName { get; set; }
        public string FromCountry { get; set; }
        public string Title { get; set; }
        public string Message { get; set; }

        protected virtual void DeserializeData(Dictionary<string, object> data)
        {
        }

        public virtual Dictionary<string, string> Serialize()
        {
            var data = new Dictionary<string,string>();
            data[SocialRequestKeys.CustomData] = "";
            data[SocialRequestKeys.Title] = Title;
            data[SocialRequestKeys.Message] = Message;
            return data;
        }

        public static PlayerComEventBase DeserializeEvent(Dictionary<string, object> eventDict)
        {
            var type = GetEventClassType(eventDict);
 
            if (type == null)
            {
                return null;
            }

            var instance = (PlayerComEventBase) Activator.CreateInstance(type);
            DeserializeBaseFields(eventDict, instance);
            DesiralizeCustomDataInEvent(eventDict, instance);
            return instance;
        }

        private static Type GetEventClassType(Dictionary<string, object> eventDict)
        {
            Type type = null;
            var eventType = "";
            try
            {
                eventType = eventDict.GetSafe(SocialRequestKeys.Type) as string;
                type = _eventStringToTypeMap[eventType];
            }
            catch
            {
                BDebug.LogErrorFormat(LogCat.Social, "Event type {0} not found", eventType);
            }
            return type;
        }

        private static void DeserializeBaseFields(Dictionary<string, object> eventDict, PlayerComEventBase instance)
        {
            ValidateEventData(eventDict);
            instance.FromPlayerId = eventDict.GetSafe(SocialRequestKeys.FromPlayerId) as string;
            instance.CreationTimestamp = Convert.ToInt64(eventDict.GetSafe(SocialRequestKeys.CreatedAt));
            instance.FromAvatar = eventDict.GetSafe(SocialRequestKeys.FromAvatar) as string;
            instance.FromName = eventDict.GetSafe(SocialRequestKeys.FromName) as string;
            instance.FromCountry = eventDict.GetSafe(SocialRequestKeys.FromCountry) as string;
            instance.Title = eventDict.GetSafe(SocialRequestKeys.Title) as string;
            instance.Message = eventDict.GetSafe(SocialRequestKeys.Message) as string;
        }

        private static void ValidateEventData(Dictionary<string, object> eventDict)
        {
            var isRequiredKeysReceived = eventDict.ContainsKey(SocialRequestKeys.FromPlayerId) &&
                                        eventDict.ContainsKey(SocialRequestKeys.CreatedAt) &&
                                        eventDict.ContainsKey(SocialRequestKeys.FromAvatar) &&
                                        eventDict.ContainsKey(SocialRequestKeys.FromName);

            if (!isRequiredKeysReceived)
            {
                Debug.LogError("[PlayerComEventBase] Not all required fields are received");
            }
        }

        private static void DesiralizeCustomDataInEvent(Dictionary<string, object> eventDict, PlayerComEventBase instance)
        {
            var eventDataJson = eventDict.GetSafe(SocialRequestKeys.CustomData) as string;
            var data = Json.Deserialize(eventDataJson) as Dictionary<string, object>;
            instance.DeserializeData(data);
        }
    }
}
