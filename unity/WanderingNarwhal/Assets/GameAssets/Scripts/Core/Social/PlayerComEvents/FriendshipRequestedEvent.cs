using System.Collections.Generic;
using BBB.MiniJSON;

namespace BebopBee.Social
{
    public class FriendshipRequestedEvent : PlayerComEventBase
    {
        public bool Recruited { get; private set; }

        public FriendshipRequestedEvent()
        {}

        public FriendshipRequestedEvent(bool recruited)
        {
            Recruited = recruited;
        }

        public override PlayerComEventType EventType
        {
            get { return PlayerComEventType.Invite; }
        }

        public override Dictionary<string, string> Serialize()
        {
            var data = base.Serialize();
            var eventData = new Dictionary<string, object> {{SocialRequestKeys.Recruited, Recruited}};
            data[SocialRequestKeys.CustomData] = Json.Serialize(eventData);
            return data;
        }

        protected override void DeserializeData(Dictionary<string, object> data)
        {
            Recruited = (bool)data.Get(SocialRequestKeys.Recruited, false);
        }
    }
}
