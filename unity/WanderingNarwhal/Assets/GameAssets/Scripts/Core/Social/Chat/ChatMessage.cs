using System;
using System.Collections.Generic;
using BBB.BrainCloud;
using GameAssets.Scripts.SocialScreens.Teams.TeamChat.Messages;

namespace BBB.Social.Chat
{
    public class ChatMessage
    {
        public string Id { get; set; }
        public string ChannelId { get; set; }
        public long CreatedAt { get; set; }
        public long UpdatedAt { get; set; }
        public string Text { get; set; }
        public ChatUser Sender { get; set; }
        public Dictionary<string, string> I18n { get; set; }
        public HashSet<string> Helps { get; set; }
        public Dictionary<string, HashSet<string>> Reactions { get; set; }
        public HashSet<string> IapClaims { get; set; }
        private string _type;
        public string Type { get => _type;
            set {
                _type = value;
                Enum.TryParse(_type, out _messageType);
            }
        }
        public Dictionary<string, object> AdditionalProperties { get; set; }
        private MessageType _messageType;
        public List<ChatMessageAttachement> Attachments;

        public MessageType GetMessageType()
        {
            return _messageType;
        }

        public static ChatMessage LoadFromResponse(BCMessageData messageData)
        {
            var chatMessage = new ChatMessage
            {
                Id = messageData.msgId,
                ChannelId = messageData.chId,
                CreatedAt = messageData.date / 1000,
                Text = messageData.content.text,
                Type = messageData.content.rich.MessageType,
                Sender = new ChatUser 
                {
                    Id = messageData.from?["id"],
                    Name = messageData.from?["name"],
                    Pic = messageData.from?["pic"],
                },
                Helps = messageData.content.rich.helps ?? new (),
                Reactions = messageData.content.rich.reactions ?? new (),
                IapClaims = messageData.content.rich.iapClaims ?? new (),
                I18n = new (),
                AdditionalProperties = messageData.content.rich.AdditionalProperties ?? new (),
                Attachments = messageData.content.rich.Attachments ?? new (),
            };
            return chatMessage;
        }
    }
}