using BBB.BrainCloud;

namespace BBB.Social.Chat
{
    public class ChatPresence
    {
        public ChatUser User;
        public bool? IsOnline;
        public bool? IsTyping;

        public static ChatPresence FromBcPresenceData(BCPresenceData presence)
        {
            bool? isTyping = null;
            object typing = false;
            if (presence.activity?.TryGetValue("typing", out typing) ?? false)
            {
                isTyping = (bool) typing;
            }

            return new ChatPresence {
                User = new ChatUser {
                    Id = (string) presence.User?["id"],
                    Name = (string) presence.User?["name"],
                    Pic =  (string) presence.User?["pic"],
                },
                IsOnline = presence.online,
                IsTyping = isTyping
            };
        }
    }
}