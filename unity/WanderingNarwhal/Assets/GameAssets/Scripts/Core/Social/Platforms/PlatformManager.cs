using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using Cysharp.Threading.Tasks;

namespace BebopBee.Social
{
    public abstract class PlatformManager
    {
        protected readonly IAccountManager AccountManager;
        
        protected PlatformManager(IAccountManager accountManager)
        {
            AccountManager = accountManager;
        }
        
        public abstract void Update();
        public abstract UniTask<PlatformCredentialData> LoginAsync();
        public abstract void Logout();
        
        protected static string GenerateRandomString(int length)
        {
            if (length <= 0)
            {
                throw new ArgumentException("Length must be positive", nameof(length));
            }
            
            var result = new char[length];
            var randomBytes = new byte[length];

            const string charset = "0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._";
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(randomBytes);
            }

            for (var i = 0; i < length; i++)
            {
                result[i] = charset[randomBytes[i] % charset.Length];
            }
            return new string(result);
        }
        
        protected static string GenerateSHA256NonceFromRawNonce(string rawNonce)
        {
            if (string.IsNullOrEmpty(rawNonce))
            {
                throw new ArgumentException("Raw nonce cannot be null or empty", nameof(rawNonce));
            }

            using var sha256 = SHA256.Create();
            var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(rawNonce));
                
            var sb = new StringBuilder();
            foreach (var b in hash)
            {
                sb.Append(b.ToString("x2"));
            }
            return sb.ToString();
        }
    }
    
    public abstract class PlatformCredentialData
    {
        public bool Cancelled;
        public string AccessToken;
    }
}
