using System;
using System.Text;
using AppleAuth;
using AppleAuth.Enums;
using AppleAuth.Extensions;
using AppleAuth.Interfaces;
using AppleAuth.Native;
using BBB;
using BBB.Core;
using BBB.DI;
using Cysharp.Threading.Tasks;

namespace BebopBee.Social
{
    public class AppleSignInManager : PlatformManager
    {
        private readonly IAppleAuthManager _appleAuthManager;

        public AppleSignInManager(IContext context) : base(context.Resolve<IAccountManager>())
        {
            if (!AppleAuthManager.IsCurrentPlatformSupported) return;
            
            // Creates a default JSON deserializer, to transform JSON Native responses to C# instances
            var deserializer = new PayloadDeserializer();
            // Creates an Apple Authentication manager with the deserializer
            _appleAuthManager = new AppleAuthManager(deserializer);
        }
        
        public override void Update()
        {
            _appleAuthManager?.Update();
        }
        
        public override async UniTask<PlatformCredentialData> LoginAsync()
        {
            if (!AppleAuthManager.IsCurrentPlatformSupported)
            {
                BDebug.LogError(LogCat.Login, "Apple login is not supported on this platform.");
                return null;
            }
            
            var rawNonce = GenerateRandomString(32);
            var appleIdCredential = await LoginWithAppleAsync(rawNonce);
            if (appleIdCredential == null)
            {
                BDebug.Log(LogCat.Login, "Apple login cancelled.");
                return new AppleCredentialData { Cancelled = true };
            }

            var appleIdToken = Encoding.UTF8.GetString(appleIdCredential.IdentityToken);
            var authorizationCode = Encoding.UTF8.GetString(appleIdCredential.AuthorizationCode);
            BDebug.Log(LogCat.Login, $"Apple credential data - token: {appleIdToken}, rawNonce: {rawNonce}, authorizationCode: {authorizationCode}");

            UpdateAppleInfo(appleIdCredential, appleIdToken);

            return new AppleCredentialData
            {
                AccessToken = authorizationCode,
                IdToken = appleIdToken,
                RawNonce = rawNonce
            };
        }
        
        private UniTask<IAppleIDCredential> LoginWithAppleAsync(string rawNonce)
        {
            var tcs = new UniTaskCompletionSource<IAppleIDCredential>();

            var nonce = GenerateSHA256NonceFromRawNonce(rawNonce);
            BDebug.Log(LogCat.Login, $"Apple login start, rawNonce: {rawNonce}, nonce: {nonce}.");
            var loginArgs = new AppleAuthLoginArgs(LoginOptions.IncludeEmail | LoginOptions.IncludeFullName, nonce);
            _appleAuthManager.LoginWithAppleId(loginArgs, credential =>
            {
                if (credential is not IAppleIDCredential appleIdCredential)
                {
                    BDebug.LogError(LogCat.Login, "Apple login failed. invalid credential.");
                    tcs.TrySetException(new Exception("Invalid Apple credential type."));
                    return;
                }

                BDebug.Log(LogCat.Login, "Apple login success.");
                tcs.TrySetResult(appleIdCredential);
            },
            error =>
            {
                var authorizationErrorCode = error.GetAuthorizationErrorCode();
                if (authorizationErrorCode == AuthorizationErrorCode.Canceled)
                {
                    tcs.TrySetResult(null);
                    return;
                }
                BDebug.LogError(LogCat.Login, $"Apple login failed - code: {authorizationErrorCode}, error: {error}");
                tcs.TrySetException(new Exception("Apple login failed: " + error));
            });

            return tcs.Task;
        }

        private void UpdateAppleInfo(IAppleIDCredential appleIdCredential, string appleIdToken)
        {
            if (appleIdCredential.User.IsNullOrEmpty())
            {
                BDebug.LogWarning(LogCat.Login, "Apple returned null uid.");
            }
            
            var appleFamilyName = appleIdCredential.FullName?.FamilyName;
            var appleGivenName = appleIdCredential.FullName?.GivenName;
            var appleNickname = appleIdCredential.FullName?.Nickname;
            var appleEmail = appleIdCredential.Email;
            BDebug.Log(LogCat.Login, $"Apple update profile info - user: {appleIdCredential.User}, token: {appleIdToken}, givenName: {appleGivenName}, familyName: {appleFamilyName}, nickname: {appleNickname}, email: {appleEmail}");
            AccountManager.UpdateAppleInfo(appleIdToken, appleFamilyName, appleGivenName, appleEmail);
        }

        public override void Logout()
        {
        }
    }
    
    public class AppleCredentialData : PlatformCredentialData
    {
        public string IdToken;
        public string RawNonce;
    }
}