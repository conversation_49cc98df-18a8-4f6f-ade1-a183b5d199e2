using System;
using System.Collections.Generic;
using UnityEngine;

namespace BebopBee.Social
{
        public enum AccountType
        {
            None = 0,
            GameCenter = 1, //OBSOLETE
            Google = 2,
            Facebook = 3,
            Line = 4,
            Apple = 5
        }
        
        public static class AccountTypeExtensions
        {
            private static readonly List<AccountType> ActiveTypesCache = new ();

            public static void Initialize(FBConfig.SystemConfig systemConfig)
            {
                ActiveTypesCache.Clear();
                if (systemConfig.FacebookSignInEnabled)
                {
                    ActiveTypesCache.Add(AccountType.Facebook);
                }
                if (systemConfig.GoogleSignInEnabled)
                {
                    ActiveTypesCache.Add(AccountType.Google);
                }
                if (AppDefinesConverter.UnityIos && systemConfig.AppleSignInEnabled)
                {
                    ActiveTypesCache.Add(AccountType.Apple);
                }
            }

            public static List<AccountType> GetAllActiveTypes()
            {
                return ActiveTypesCache;
            }
        }
}
