#if UNITY_IOS && !UNITY_EDITOR
#define UNITY_IOS_DEVICE
#endif

using System.Collections.Generic;
using Facebook.Unity;
using System;
using BBB;
using BBB.Core;
using BBB.DI;
using GameAssets.Scripts.Utils;
using Cysharp.Threading.Tasks;
using Json = Facebook.MiniJSON.Json;

namespace BebopBee.Social
{
    public class FacebookManager : PlatformManager
    {
        private const string ProfileName = "name";
        private const string ProfileEmail = "email";
        
        private static readonly string[] Permissions = { "public_profile", "email" };

        private static bool IsLoggedIn => FB.IsLoggedIn;
        private static bool IsInitialized => FB.IsInitialized;

        private static string TokenString =>
#if UNITY_IOS_DEVICE
            FB.Mobile.CurrentAuthenticationToken()?.TokenString;
#else
            AccessToken.CurrentAccessToken?.TokenString;
#endif
        
        private static string UserId =>
#if UNITY_IOS_DEVICE
            FB.Mobile.CurrentProfile()?.UserID;
#else
            AccessToken.CurrentAccessToken?.UserId;
#endif
        
        public FacebookManager(IContext context) : base(context.Resolve<IAccountManager>())
        {
            if (IsInitialized)
            {
                BDebug.Log(LogCat.Login, "Facebook Manager already Initialized.");
                return;
            }

            FB.Init(GameConstants.FacebookAppId, GameConstants.FacebookClientToken, onInitComplete: () =>
            {
                BDebug.Log(LogCat.Login, IsLoggedIn ? "<color=green>Facebook init complete: logged in</color>" : "Facebook init complete: <color=red>not logged in</color>");
            });
        }
        
        public override void Update()
        {
        }

        public override async UniTask<PlatformCredentialData> LoginAsync()
        {
            if (!IsInitialized)
            {
                BDebug.LogError(LogCat.Login, "Facebook SDK not initialized.");
                return null;
            }
            
            var rawNonce = GenerateRandomString(32);
            var result = await FacebookLoginAsync(rawNonce);
            if (result.Cancelled)
            {
                BDebug.Log(LogCat.Login, "Facebook login cancelled.");
                return new FacebookDefaultCredentialData { Cancelled = true };
            }

            try
            {
                await UpdateFacebookInfo();
            }
            catch (Exception e)
            {
                BDebug.LogError(LogCat.Login, $"Facebook getting profile failed: {e}");
            }

#if UNITY_IOS_DEVICE
            return new FacebookIosCredentialData { IdToken = TokenString, RawNonce = rawNonce };
#else
            return new FacebookDefaultCredentialData { AccessToken = TokenString };
#endif
        }

        private UniTask<ILoginResult> FacebookLoginAsync(string rawNonce)
        {
            var tcs = new UniTaskCompletionSource<ILoginResult>();
            
#if UNITY_IOS_DEVICE
            var nonce = GenerateSHA256NonceFromRawNonce(rawNonce);
            BDebug.Log(LogCat.Login, $"Facebook limited login start, rawNonce: {rawNonce}, nonce: {nonce}.");
            FB.Mobile.LoginWithTrackingPreference(LoginTracking.LIMITED, Permissions, nonce, result =>
#else
            BDebug.Log(LogCat.Login, "Facebook login start.");
            FB.LogInWithReadPermissions(Permissions, result =>
#endif
            {
                if (!result.Cancelled)
                {
                    if (!IsLoggedIn)
                    {
                        BDebug.LogError(LogCat.Login, $"Facebook login failed: {result.Error}");
                        tcs.TrySetException(new Exception(result.Error));
                        return;
                    }

                    var token = TokenString;
                    if (token.IsNullOrEmpty())
                    {
                        BDebug.LogError(LogCat.Login, "Facebook login callback: Token is null or empty");
                        tcs.TrySetException(new Exception("Token is null or empty."));
                        return;
                    }

                    BDebug.Log(LogCat.Login, $"Facebook login success. userId: {UserId}, token: {token}");
                }
                tcs.TrySetResult(result);
            });

            return tcs.Task;
        }

        private async UniTask UpdateFacebookInfo()
        {
#if UNITY_IOS_DEVICE
            var profile = FB.Mobile.CurrentProfile();
            if (profile == null)
            {
                throw new Exception("Failed to load facebook profile info - CurrentProfile is null.");
            }
            var userName = profile.Name;
            var userId = profile.UserID;
            var userEmail = profile.Email;
            var profileImageUrl = profile.ImageURL;
#else
            var profileInfo = await RequestProfileInfoAsync(TokenString);
            var profileImageUrl = string.Empty;
            if (profileInfo.TryGetValue("picture", out var pictureObject))
            {
                var pictureDict = (Dictionary<string, object>)pictureObject;
                if (pictureDict != null && pictureDict.TryGetValue("data", out var data))
                {
                    var picture = (Dictionary<string, object>)data;
                    if (picture.TryGetValue("url", out var value))
                    {
                        profileImageUrl = (string)value;
                    }
                }
            }
            var userName = (string)profileInfo.GetValueOrDefault(ProfileName, string.Empty);
            var userEmail = (string)profileInfo.GetValueOrDefault(ProfileEmail, string.Empty);
            var userId = UserId;
#endif
                    
            BDebug.Log(LogCat.Login, $"Facebook update profile info - name: {userName}, email: {userEmail}, pictureUrl: {profileImageUrl}");
            AccountManager.UpdateFacebookInfo(userId, userName, profileImageUrl, userEmail);
        }

        private UniTask<Dictionary<string, object>> RequestProfileInfoAsync(string accessToken)
        {
            var tcs = new UniTaskCompletionSource<Dictionary<string, object>>();
            BDebug.Log(LogCat.Login, "Facebook requesting profile info.");
            /* "me?fields=id,name,picture.redirect(false).type(square).width(100).height(100)";*/
            var query = FacebookQueryBuilder.StartBuilding()
                .My()
                .Fields()
                .Id()
                .FirstName()
                .LastName()
                .Name()
                .Email()
                .SquarePicture(100, 100)
                .AccessToken(accessToken)
                .EndBuilding();

            FB.API(query, HttpMethod.GET, result =>
            {
                if (!string.IsNullOrEmpty(result.Error))
                {
                    BDebug.LogError(LogCat.Login, $"Failed to load facebook profile info - error: {result.Error} , query: {query}");
                    tcs.TrySetException(new Exception(result.Error));
                    return;
                }

                if (Json.Deserialize(result.RawResult) is not Dictionary<string, object> resultObject)
                {
                    BDebug.LogError(LogCat.Login, $"[FacebookManager::RequestProfileInfoAsync] response parse error: {result.Error}");
                    tcs.TrySetException(new Exception($"Facebook profile response parse error: {result.Error}"));
                    return;
                }
                
                BDebug.Log(LogCat.Login, "<color=green>[FacebookManager::RequestProfileInfoAsync] User facebook info successfully loaded</color>");
                tcs.TrySetResult(resultObject);
            });
            
            return tcs.Task;
        }

        public override void Logout()
        {
            BDebug.Log(LogCat.Login, "Facebook logout.");
            FB.LogOut();
        }
    }

    public class FacebookDefaultCredentialData : PlatformCredentialData
    {
        
    }
    
    public class FacebookIosCredentialData : PlatformCredentialData
    {
        public string IdToken;
        public string RawNonce;
    }
}