using BBB;
using BBB.Core;
using UnityEngine;

namespace BebopBee.Social
{
    public class FacebookQueryBuilder
    {
        private string _query;

        private bool _buildingFields = false;
        
        private FacebookQueryBuilder()
        {

        }
        
        public static FacebookQueryBuilder StartBuilding()
        {
             var builder = new FacebookQueryBuilder();
             builder._query = string.Empty;
             return builder;
        }

        public string EndBuilding()
        {
           if (_query.EndsWith("=") || _query.EndsWith("?"))
              BDebug.LogError(LogCat.Social, "Failed to build query "+ _query);
           else if (_query.EndsWith(","))
                _query = _query.Remove(_query.Length-1);

            return _query;
        }
        
        public FacebookQueryBuilder My()
        {
           _query += "me?";
           return this;
        }

        public FacebookQueryBuilder InvitableFriends()
        {
            _query += "/me/invitable_friends?";
            return this;
        }
        
        public FacebookQueryBuilder Fields()
        {
            _buildingFields = true;
           _query += "fields=";
           return this;
        }         

        public FacebookQueryBuilder Id()
        {
            if(!_buildingFields)
               BDebug.LogErrorFormat(LogCat.Social, "Can not add field to {0} query, specify fields= first", _query);

            _query += "id,";
            return this;
        }

        public FacebookQueryBuilder Name()
        {
            if (!_buildingFields)
               BDebug.LogErrorFormat(LogCat.Social, "Can not add field to {0} query, specify fields= first", _query);

            _query += "name,";
            return this;
        }
        
        public FacebookQueryBuilder Email()
        {
            if (!_buildingFields)
                BDebug.LogErrorFormat(LogCat.Social, "Can not add field to {0} query, specify fields= first", _query);

            _query += "email,";
            return this;
        }
        

        public FacebookQueryBuilder FirstName()
        {
            if (!_buildingFields)
               BDebug.LogErrorFormat(LogCat.Social, "Can not add field to {0} query, specify fields= first", _query);

            _query += "first_name,";
            return this;
        }

        public FacebookQueryBuilder LastName()
        {
            if (!_buildingFields)
               BDebug.LogErrorFormat(LogCat.Social, "Can not add field to {0} query, specify fields= first", _query);

            _query += "last_name,";
            return this;
        }

        public FacebookQueryBuilder Friends()
        {
            _query += "me/friends?";
            return this;
        }

        public FacebookQueryBuilder SquarePicture(int width, int height)
        {
            if (!_buildingFields)
               BDebug.LogErrorFormat(LogCat.Social, "Can not add field to {0} query, specify fields= first", _query);

            _query += string.Format("picture.redirect(false).type(square).width({0}).height({1}),", width, height);
            return this;
        }

        public FacebookQueryBuilder AccessToken(string tokenString)
        {
            if (string.IsNullOrEmpty(tokenString))
                return this;

            if (_buildingFields)
            {
                if (_query.EndsWith(","))
                    _query = _query.Remove(_query.Length - 1);

                _query += "&";
            }
               

            _query += string.Format("access_token={0},", tokenString);
            return this;
        }
    }

}
