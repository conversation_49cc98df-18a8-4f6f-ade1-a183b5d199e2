using System;
using Assets.SimpleSignIn.Google.Scripts;
using BBB;
using BBB.Core;
using BBB.DI;
using Cysharp.Threading.Tasks;

namespace BebopBee.Social
{

    public class GoogleSignInManager : PlatformManager
    {
        private readonly GoogleAuth _googleAuth;

        public GoogleSignInManager(IContext context) : base(context.Resolve<IAccountManager>())
        {
            _googleAuth = new GoogleAuth();
        }

        public override void Update()
        {
        }
        
        public override async UniTask<PlatformCredentialData> LoginAsync()
        {
            var userInfo = await GoogleLoginAsync();
            if (userInfo == null)
            {
                BDebug.Log(LogCat.Login, "Google login cancelled.");
                return new GoogleCredentialData { Cancelled = true };
            }
            
            var tokenResponse = await GetTokenResponseAsync();
            var isValid = await ValidateSignatureAsync(tokenResponse.IdToken);

            if (!isValid) return null;

            UpdateGoogleInfo(tokenResponse, userInfo);
            
            return new GoogleCredentialData
            {
                AccessToken = tokenResponse.AccessToken,
                IdToken = tokenResponse.IdToken
            };
        }

        private UniTask<UserInfo> GoogleLoginAsync()
        {
            var tcs = new UniTaskCompletionSource<UserInfo>();
            BDebug.Log(LogCat.Login, "Google login start.");
            _googleAuth.SignIn((success, error, userInfo) =>
            {
                if (!success)
                {
                    if(error.Contains("cancelled"))
                    {
                        tcs.TrySetResult(null);
                        return;
                    }
                    BDebug.LogError(LogCat.Login, $"Google login failed with error: {error}");
                    tcs.TrySetException(new Exception(error));
                    return;
                }
                BDebug.Log(LogCat.Login, "Google login success.");
                tcs.TrySetResult(userInfo);
            }, caching: true);

            return tcs.Task;
        }
        
        private UniTask<TokenResponse> GetTokenResponseAsync()
        {
            var tcs = new UniTaskCompletionSource<TokenResponse>();
            BDebug.Log(LogCat.Login, "Google getting access token.");
            _googleAuth.GetTokenResponse((success, error, tokenResponse) =>
            {
                if (!success)
                {
                    BDebug.LogError(LogCat.Login, $"Google getting token failed with error: {error}");
                    tcs.TrySetException(new Exception(error));
                    return;
                }

                BDebug.Log(LogCat.Login, $"Google get access token successful, access token: {tokenResponse.AccessToken}");
                tcs.TrySetResult(tokenResponse);
            });

            return tcs.Task;
        }
        
        private UniTask<bool> ValidateSignatureAsync(string idToken)
        {
            var tcs = new UniTaskCompletionSource<bool>();
            var jwt = new JWT(idToken);
            BDebug.Log(LogCat.Login, $"Google validating token - id token: {idToken}, JWT Payload: {jwt.Payload}");
            jwt.ValidateSignature(_googleAuth.ClientId, (success, error) =>
            {
                if (!success)
                {
                    BDebug.LogError(LogCat.Login, $"Google id token validation failed with error: {error}");
                    tcs.TrySetResult(false);
                    return;
                }
                
                BDebug.Log(LogCat.Login, "Google id token validation success.");
                tcs.TrySetResult(true);
            });

            return tcs.Task;
        }

        private void UpdateGoogleInfo(TokenResponse tokenResponse, UserInfo userInfo)
        {
            BDebug.Log(LogCat.Login, $"Google update profile info - name: {userInfo.given_name}, email: {userInfo.email}, pictureUrl: {userInfo.picture}, idToken: {tokenResponse.IdToken}");
            AccountManager.UpdateGoogleInfo(tokenResponse.IdToken, userInfo.given_name, userInfo.picture, userInfo.email);
        }

        public override void Logout()
        {
            BDebug.Log(LogCat.Login, "Google logout.");
            _googleAuth.SignOut();
        }
    }
    
    public class GoogleCredentialData : PlatformCredentialData
    {
        public string IdToken;
    }
}