using System.Collections.Generic;
using System.Text;
using BBB;
using GameAssets.Scripts.Core.TimeManager;
using RPC.Teams;

namespace BebopBee.Social
{
    public class UserPublicProfile : IPublicProfile
    {
        public const string DefaultLevel = "level1";
        public static readonly string DefaultLocation = string.Empty;
        public const string DefaultCountry = "US";
        public bool IsDirty { get; set; }

        private static string _uId;
        public string Uid
        {
            get => _uId;
            set
            {
                _uId = value;

                IsDirty = true;
            }
        }

        private static string _avatar;
        public string Avatar
        {
            get => _avatar;
            set
            {
                _avatar = value;
                IsDirty = true;
            }
        }
        private static string _facebookAvatar;
        public string FacebookAvatar
        {
            get => _facebookAvatar;
            set
            {
                _facebookAvatar = value;
                
                if (!_facebookAvatar.IsNullOrEmpty() && (Avatar.IsNullOrEmpty() || GenericResourceProvider.IsDefaultAvatar(Avatar)))
                {
                    Avatar = _facebookAvatar;
                }
            }
        }

        private static string _name;
        public string Name
        {
            get => _name;
            set
            {
                _name = value;
                IsDirty = true;
            }
        }
        
        private static string _displayName;
        public string DisplayName
        {
            get => _displayName;
            set
            {
                _displayName = value;
                IsDirty = true;
            }
        }

        private static string _country;
        public string Country
        {
            get => _country;
            set
            {
                _country = value;
                IsDirty = true;
            }
        }

        private static string _lastUnlockedLocationId;
        public string LastUnlockedLocationId
        {
            get => _lastUnlockedLocationId;
            set
            {
                if (_lastUnlockedLocationId == value) return;
                
                _lastUnlockedLocationId = value;
                IsDirty = true;
            }
        }

        private static string _highestPassedLevelId;
        public string HighestPassedLevelId
        {
            get => _highestPassedLevelId;
            set
            {
                if (_highestPassedLevelId == value) return;

                _highestPassedLevelId = value;
                IsDirty = true;
            }
        }

        private static int _lastActiveTimeStamp;
        public int LastActiveTimeStamp
        {
            get => _lastActiveTimeStamp;
            set
            {
                if (_lastActiveTimeStamp == value) return;

                _lastActiveTimeStamp = value;
                IsDirty = true;
            }
        }

        public UserPublicProfile(Dictionary<string, object> data = null)
        {
            if (data != null)
            {
                Deserialize(data);
            }
        }

        public UserPublicProfile(TeamMemberData teamMemberData)
        {
            Uid = teamMemberData.Uid;
            Avatar = teamMemberData.Avatar;
            Name = teamMemberData.Name;
            Country = teamMemberData.Country;
        }

        public override string ToString()
        {
            var stringBuilder = new StringBuilder();

            stringBuilder.Append("dirty: ").Append(IsDirty).Append('\n')
                .Append("uid: ").AppendLine(Uid)
                .Append("avatar: ").AppendLine(Avatar)
                .Append("name: ").AppendLine(Name)
                .Append("display_name: ").AppendLine(DisplayName)
                .Append("lastUnlockedLocationId: ").AppendLine(LastUnlockedLocationId)
                .Append("highestPassedLevelId: ").AppendLine(HighestPassedLevelId)
                .Append("country: ").AppendLine(Country)
                .Append("lastActiveTimestamp: ").Append(LastActiveTimeStamp).Append('\n');

            return stringBuilder.ToString();
        }
        
        public Dictionary<string, object> Serialize(string userId = null)
        {
            var profileData = new Dictionary<string, object>
            {
                [IPublicProfile.UidKey] = Uid,
                [IPublicProfile.AvatarKey] = Avatar,
                [IPublicProfile.NameKey] = Name,
                [IPublicProfile.DisplayNameKey] = DisplayName,
                [IPublicProfile.LastUnlockedLocationIdKey] = LastUnlockedLocationId,
                [IPublicProfile.HighestPassedLevelIdKey] = HighestPassedLevelId
            };

            return profileData;
        }

        public void Deserialize(Dictionary<string, object> data)
        {
            Name = ProfileUtils.GetConvertToString(data, IPublicProfile.NameKey);
            Uid = ProfileUtils.GetConvertToString(data, IPublicProfile.UidKey);
            Avatar = ProfileUtils.GetConvertToString(data, IPublicProfile.AvatarKey);
            DisplayName = ProfileUtils.GetConvertToString(data, IPublicProfile.DisplayNameKey);
            LastUnlockedLocationId = ProfileUtils.IsStringValueNull(data, IPublicProfile.LastUnlockedLocationIdKey) ? LocationConstants.DefaultLocation : ProfileUtils.GetConvertToString(data, IPublicProfile.LastUnlockedLocationIdKey);
            HighestPassedLevelId = ProfileUtils.IsStringValueNull(data, IPublicProfile.HighestPassedLevelIdKey) ? ProfileUtils.DefaultLevel : ProfileUtils.GetConvertToString(data, IPublicProfile.HighestPassedLevelIdKey);
            Country = ProfileUtils.IsStringValueNull(data, IPublicProfile.CountryKey) ? PlatformUtil.GetCurrentCountryCode() : ProfileUtils.GetConvertToString(data, IPublicProfile.CountryKey);

            LastActiveTimeStamp = (int)ProfileUtils.GetConvertToDouble(data, IPublicProfile.LastActionTimeKey);
        }

        public string GetLastSeenStatusText(TimeManager timeManager)
        {
            var timeLastSeen = LastActiveTimeStamp;
            var timeSinceLastSeen = (int)timeManager.CurrentTimeStamp() - timeLastSeen;
            return LocalizationManager.GetLocalizedTextWithArgs(GetLocalizationKeyForLastActiveStatus(timeSinceLastSeen));
        }
        
        private static string GetLocalizationKeyForLastActiveStatus(int secondsSinceLastActive)
        {
            return secondsSinceLastActive switch
            {
                <= 60 => "SOCIAL_ACTIVE_STATUS_ONLINE",
                <= 60 * 60 * 24 => "SOCIAL_ACTIVE_STATUS_SEEN_TODAY",
                <= 60 * 60 * 24 * 2 => "SOCIAL_ACTIVE_STATUS_SEEN_YESTERDAY",
                <= 60 * 60 * 24 * 7 => "SOCIAL_ACTIVE_STATUS_SEEN_THIS_WEEK",
                <= 60 * 60 * 24 * 7 * 4 => "SOCIAL_ACTIVE_STATUS_SEEN_WEEKS_AGO",
                _ => "SOCIAL_ACTIVE_STATUS_SEEN_A_WHILE_AGO"
            };
        }
    }
}