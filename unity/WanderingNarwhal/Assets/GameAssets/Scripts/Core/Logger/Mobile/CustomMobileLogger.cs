using System.Runtime.InteropServices;

namespace BBB.Core.Logger.Mobile
{
    public class CustomMobileLogger : ILogger
    {
        #if UNITY_ANDROID
        [DllImport("customlog")]
        #else
        [DllImport("__Internal")]
        #endif
        private static extern void _CustomLogDebug(string tag, string message);

        #if UNITY_ANDROID
        [DllImport("customlog")]
        #else
        [DllImport("__Internal")]
        #endif
        private static extern void _CustomLogInfo(string tag, string message);

        #if UNITY_ANDROID
        [DllImport("customlog")]
        #else
        [DllImport("__Internal")]
        #endif
        private static extern void _CustomLogWarning(string tag, string message);

        #if UNITY_ANDROID
        [DllImport("customlog")]
        #else
        [DllImport("__Internal")]
        #endif
        private static extern void _CustomLogError(string tag, string message);


        public void Log(string message)
        {
            _CustomLogDebug("", message);
        }

        public void Log(string tag, string message)
        {
            _CustomLogDebug(tag, message);
        }

        public void LogWarning(string tag, string message)
        {
            _CustomLogWarning(tag, message);
        }

        public void LogError(string tag, string message)
        {
            _CustomLogError(tag, message);
        }
    }
}