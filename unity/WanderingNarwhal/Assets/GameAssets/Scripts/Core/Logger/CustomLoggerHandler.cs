using System;
using UnityEngine;
using Object = UnityEngine.Object;

namespace BBB
{
    public class CustomLoggerHandler : ILogHandler
    {
        private readonly ILogger _customLogger;
        private readonly string[] _fileExceptions = {"CustomLoggerHandler", "unity/build", "LogStopwatch"};

        private readonly string[] _methodsExceptions =
            {"UnityEngine.Logger", "UnityEngine.Debug", "BBB.CustomLoggerHandler", "Core.Debug.LogStopwatch"};

        public CustomLoggerHandler()
        {
            _customLogger = new CustomLogger();
        }

        public void LogFormat(LogType logType, Object context, string format, params object[] args)
        {
            try
            {
                var filenameInfo = GetFileNameInfo();
                var log = string.Format(format, args);

                switch (logType)
                {
                    case LogType.Error:
                        _customLogger.LogError("RedLion", $"{log} {filenameInfo}");
                        break;
                    case LogType.Assert:
                        _customLogger.LogError("RedLion", $"{log} {filenameInfo}");
                        break;
                    case LogType.Warning:
                        _customLogger.LogWarning("RedLion", $"{log} {filenameInfo}");
                        break;
                    case LogType.Log:
                        _customLogger.Log("RedLion", $"{log} {filenameInfo}");
                        break;
                    case LogType.Exception:
                        _customLogger.LogError("RedLion", $"{log} {filenameInfo}");
                        break;
                    default:
                        LogException(new ArgumentOutOfRangeException("logType", logType, null), null);
                        break;
                }
            }
            catch (Exception exception)
            {
                _customLogger.LogError("RedLion", $"{exception.Message}\n\t{exception.StackTrace}");
            }
        }

        private string GetFileNameInfo(Exception ex = null)
        {
            return string.Empty;
        }

        public void LogException(Exception exception, Object context)
        {
            var filenameInfo = GetFileNameInfo(exception);
            _customLogger.LogError("RedLion", $"{exception.Message}\n\t{exception.StackTrace} {filenameInfo}");
        }
    }
}