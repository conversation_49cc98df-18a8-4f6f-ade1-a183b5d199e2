#if UNITY_ANDROID || UNITY_IOS
using BBB.Core.Logger.Mobile;
#endif

namespace BBB
{
    public class CustomLogger : ILogger
    {
        private readonly ILogger _platFormLogger;

        public CustomLogger()
        {
#if !UNITY_EDITOR
    #if UNITY_ANDROID
            _platFormLogger = new CustomMobileLogger();
    #elif UNITY_IOS
            _platFormLogger = new CustomMobileLogger();
    #endif
#else
#endif
        }

        public void Log(string message)
        {
            _platFormLogger.Log(message);
        }

        public void Log(string tag, string message)
        {
            _platFormLogger.Log(tag, message);
        }

        public void LogWarning(string tag, string message)
        {
            _platFormLogger.LogWarning(tag, message);
        }

        public void LogError(string tag, string message)
        {
            _platFormLogger.LogError(tag, message);
        }
    }
}