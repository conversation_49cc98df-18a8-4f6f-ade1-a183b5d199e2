using System;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using UnityEngine;

namespace BBB
{
    public static class JsonValidation
    {
        public static bool IsValidJson(string str)
        {
            str = str.Trim();
            if ((str.StartsWith("{") && str.EndsWith("}")) || //For object
                (str.StartsWith("[") && str.EndsWith("]"))) //For array
            {
                try
                {
                    JToken.Parse(str);
                    return true;
                }
                catch (JsonReaderException jex)
                {
                    Debug.LogError(jex.Message);
                    return false;
                }
                catch (Exception ex)
                {
                    Debug.LogException(ex);
                    return false;
                }
            }

            return false;
        }
    }
}