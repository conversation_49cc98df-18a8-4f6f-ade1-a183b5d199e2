using System.Collections.Generic;
using UnityEngine;
#if UNITY_EDITOR || UNITY_IOS
using UnityEngine.iOS;
#endif

public static class SafeAreaProvider
{
    // That's intentional values. We have map of specification below in IosResolutions
    // But they have very significant gap beyond actual notch or bottom control line
    // So, to avoid a lot of empty space, we use this multipliers. They are empirically adjusted, previously we used flat 44px
    // But it works poorly beyond 44px
    private static readonly float IosTopOffsetMultiplier = 0.7f;
    private static readonly float IosBottomOffsetMultiplier = 0.4f;

    private static readonly float IosTopOffset = 47f;
    private static readonly float IosBottomOffset = 34f;

    private static readonly Rect? IphoneRect;

    static SafeAreaProvider()
    {
#if UNITY_EDITOR || UNITY_IOS
        switch (Device.generation)
        {
            // iphone 14 pro/max, 15 pro and 15 has custom top offset because of island
            case DeviceGeneration.iPhone14Pro
                or DeviceGeneration.iPhone14ProMax
                or DeviceGeneration.iPhone15
                or DeviceGeneration.iPhone15Plus
                or DeviceGeneration.iPhone15Pro
                or DeviceGeneration.iPhone15ProMax
                or DeviceGeneration.iPhone16
                or DeviceGeneration.iPhone16Plus:
                IosTopOffset = 59f;
                break;
            // 16th has custom top offset because of increased island
            case DeviceGeneration.iPhone16Pro
                or DeviceGeneration.iPhone16ProMax:
                IosTopOffset = 62f;
                break;
            // to handle unsupported iphones for later
            case DeviceGeneration.Unknown
                or DeviceGeneration.iPhoneUnknown:
                IosTopOffset = 62f;
                break;
        }

        foreach (var (width, height, pixelScale) in IosResolutions)
        {
            if (Screen.width != width || Screen.height != height)
                continue;

            var bottomOffset = IosBottomOffset * pixelScale * IosBottomOffsetMultiplier;
            var topOffset = IosTopOffset * pixelScale * IosTopOffsetMultiplier;
            IphoneRect = new Rect(0, bottomOffset, width, height - topOffset - bottomOffset);
            break;
        }
#endif
    }

    private static readonly List<(int width, int height, float pixelScale)> IosResolutions = new()
    {
        // iPhone 16 Pro Max 3x
        (1320, 2868, 3f),
        // iPhone 14 Pro Max, iPhone 15 Plus, iPhone 15 Pro Max, iPhone 16 Plus 3x, 
        (1290, 2796, 3f),
        // iPhone 14 Plus, 12 Pro Max, 13 Pro Max 3x
        (1284, 2778, 3f),
        // Iphone Xs Max, 11 Pro Max 3x
        (1242, 2688, 3f),
        // iPhone 16 Pro 3x
        (1206, 2622, 3f),
        // iPhone 14 Pro, iPhone 15, iPhone 15 Pro, iPhone 16 3x
        (1179, 2556, 3f),
        // iPhone 12, 12 Pro, 13, 13 Pro 3x
        (1170, 2532, 3f),
        // Iphone X, Xs, 11 Pro 3x
        (1125, 2436, 3f),
        // iPhone 12 Mini, 13 Mini 2.88x
        (1080, 2338, 2.88f),
        // iPhone 11, XR 2x
        (828, 1792, 2f),
    };

    public static Rect GetSafeAreaRect()
    {
        return IphoneRect ?? Screen.safeArea;
    }
}