using System;
using System.Web.UI.WebControls;
using BBB.Audio;
using BBB.Controller;
using BBB.DI;
using BBB.UI.Core;
using BebopBee.Core.Audio;
using BebopBee.Core.UI;
using UnityEngine;
using BBB.MMVibrations;
using BBB.MMVibrations.Plugins;

namespace BBB.Core
{
    public class BaseModalsController<TViewPresenter> : BaseController<TViewPresenter>, IModalsController<TViewPresenter>
        where TViewPresenter : IViewPresenter
    {
        // NOTE: when enabled, EasyTouch doesn’t raise gesture message when your touch is over UI Element
        private bool _easyTouchUiCompatibility;
        private IContext _context;

        private Coroutine _coroutine;
        private Action _discardCallback;
        private Action _cachedDoWhenReadyAction;
        private IVibrationsWrapper _vibrations;

        protected ICoroutineExecutor CoroutineExecutor;
        protected IModalsViewPresenter ModalsView => View as IModalsViewPresenter;
        protected virtual bool UseEasyTouchUICompatibility => true;
        public override bool ShouldHideHud => false;

        public override void Init(IContext previousContext)
        {
            base.Init(previousContext);

            if (_context != null) return;

            OnInitializeByContext(previousContext);
            OnContextInitialized();
            _context = previousContext;
            CoroutineExecutor = previousContext.Resolve<ICoroutineExecutor>();
        }

        public override void SetView(TViewPresenter view)
        {
            if (View != null && _context == null)
                return;

            view.Init(_context);
            _context = null;

            View = view;
            OnSetView();
        }

        protected virtual void OnSetView()
        {
        }

        public bool IsVisible()
        {
            return View != null && View.IsVisible();
        }

        /// <summary>
        /// Show modal via modals manager right now or with some delay if currently can't show new modals.
        /// By default modals will not show if other modals are currently active, use immediate option to override that.
        /// </summary>
        /// <param name="showMode">Option to show modal even if other modal is currently active.</param>
        /// <param name="setupParams"></param>
        /// <param name="playHaptic"></param>
        /// <remarks>
        /// If modal is not `immediate`, then it will not show if currently any modal is active, and it will be delayed until all active modals not closed.
        /// However, immediate modals can also be delayed by such blocking factors: map screen coins drop, camera movement or active tutorial step. These blocking factors can't be overriden.
        /// </remarks>
        public virtual void ShowModal(ShowMode showMode = ShowMode.Immediate, ModalSetupParamsBase setupParams = null, bool playHaptic = true)
        {
            if (playHaptic)
            { 
                BDebug.Log(LogCat.Vibration, $"Playing haptic feedback for Modal with setupParams: {setupParams} -- Vibrations available={_vibrations != null} -- Impacat Type = {ImpactPreset.MediumImpact} from BaseModalsController");
                _vibrations?.PlayHaptic(ImpactPreset.MediumImpact);
            }
            ModalsBuilder.Show(this, showMode: showMode, setupParams: setupParams);
        }

        public override void OnDiscard()
        {
            _discardCallback.SafeInvoke();
            _discardCallback = null;
        }

        public override void Hide()
        {
            HideModal();
        }

        protected virtual void HideModal()
        {
            base.Hide();
            ModalsBuilder.Hide(this);
            OnPostHide();
        }

        protected virtual void OnPostHide()
        {
        }

        protected void DoWhenReady(Action doWhenReady)
        {
            if (IsReady())
            {
                doWhenReady.SafeInvoke();
            }
            else
            {
                _cachedDoWhenReadyAction += doWhenReady;
            }
        }

        protected override void OnShow()
        {
            base.OnShow();

            PlayOpenningSound();
        }

        public override void OnPostShow()
        {
            base.OnPostShow();

            _easyTouchUiCompatibility = HedgehogTeam.EasyTouch.EasyTouch.GetUIComptability();
            HedgehogTeam.EasyTouch.EasyTouch.SetUICompatibily(UseEasyTouchUICompatibility);
        }

        protected virtual void PlayOpenningSound()
        {
            AudioProxy.PlaySound(GenericSoundIds.GenericPopupAppearing);
        }

        protected override void OnHide()
        {
            base.OnHide();
            if (IsReady())
            {
                View.OnCloseButtonEvent -= OnCloseButtonClicked;
            }

            HedgehogTeam.EasyTouch.EasyTouch.SetUICompatibily(_easyTouchUiCompatibility);
        }

        protected virtual void OnInitializeByContext(IContext context)
        {
            // For modals that appear before IVibrationsWrapper is initialized (like ATT consent)
            // we should not throw an exception when trying to get it from the context
            _vibrations = context.Resolve<IVibrationsWrapper>(null, true);
        }

        public virtual bool CanBypassTransition()
        {
            return false;
        }

        public virtual void Setup(ModalSetupParamsBase setupParams)
        {
        }

        public void TriggerDoWhenReady()
        {
            _cachedDoWhenReadyAction?.Invoke();
            _cachedDoWhenReadyAction = null;
        }

        public override void DisposeContext()
        {
            base.DisposeContext();
            _discardCallback = null;
            _cachedDoWhenReadyAction = null;
        }
    }
}