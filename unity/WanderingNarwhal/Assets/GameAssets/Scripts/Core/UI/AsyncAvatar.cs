using BBB.Core;
using BBB.Core.ResourcesManager;
using BBB.DI;
using BBB.UI.Core;
using BebopBee.Social;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts;
using UnityEngine;
using UnityEngine.UI;

namespace BBB
{
    public class AsyncAvatar : ContextedUiBehaviour
    {
        [SerializeField] private AsyncImage _avatarImage;
        [SerializeField] private string _defaultAvatar = "avatar_fennec";
        [Space]
        [SerializeField] private Image _flagImage;

        private FlagsLoader _flagsLoader;
        private IAssetsManager _assetsManager;

        protected override void Awake()
        {
            base.Awake();

            if (_avatarImage == null)
                Debug.LogError($"AsyncImage is not setup for AsyncAvatar: {gameObject.name}", gameObject);
        }

        public void Setup(AvatarInfo avatarInfo)
        {
            SetupAvatar(avatarInfo.AvatarUrl);
            SetupCountryAsync(avatarInfo.Country).Forget();
        }

        private void SetupAvatar(string avatarUrl)
        {
            LazyInit();

            if (_avatarImage != null)
            {
                _avatarImage.Load(avatarUrl.IsNullOrEmpty() ? _defaultAvatar : avatarUrl, SocialConstants.AvatarsTag, error =>
                {
                    if (_avatarImage)
                    {
                        _assetsManager
                            .LoadSpriteAsync(GenericResourceProvider.FallbackOfflineAvatar)
                            .ContinueWith(sprite => _avatarImage.ReplaceSprite(sprite));
                    }
                });
            }
            else
            {
                Debug.LogError($"AsyncImage is not setup for AsyncAvatar: {gameObject.name}", gameObject);
            }
        }

        private async UniTask SetupCountryAsync(string country)
        {
            LazyInit();

            if (_flagImage == null || country.IsNullOrEmpty())
                return;

            var countryCode = Util.ConvertCountryNameToCountryCode(country);
            var sprite = await _flagsLoader.GetSmallFlagByCountryNameAsync(countryCode);
            _flagImage.sprite = sprite;
        }

        public void ManualStop()
        {
            if (_avatarImage != null)
                _avatarImage.Stop();
        }

        protected override void InitWithContextInternal(IContext context)
        {
            _flagsLoader = context.Resolve<FlagsLoader>();
            _assetsManager = context.Resolve<IAssetsManager>();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();

            if (_avatarImage != null)
                _avatarImage.Stop();
            else
                Debug.LogError($"AsyncImage is not setup for AsyncAvatar: {gameObject.name}", gameObject);
        }
    }
}