using BBB.Controller;
using BBB.UI.Core;
using BebopBee.Core.UI;

namespace BBB
{
    public interface IModalsBuilder : IViewsBuilder
    {
        TController CreateModalView<TController>(ModalsType modalsType)
            where TController : class, IController, new();

        void Show<TController>(TController ctrl, string uid = null,
            ShowMode showMode = ShowMode.Immediate,
            ModalSetupParamsBase setupParams = null)
            where TController : IController;

        IController TryCreateModalView(ModalsType modalsType);
    }
}