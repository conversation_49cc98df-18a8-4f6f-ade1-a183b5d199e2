using System;
using BBB.Controller;
using BBB.UI.Core;
using BebopBee;

namespace BBB
{
    public interface IScreensBuilder : IViewsBuilder
    {
        ScreenType CurrentScreenType { get; }
        ScreenType PreviousScreenType { get; }
        bool ShowPreviousScreen();

        void ShowPreviousIfAnyOf(Action fallbackDelegate, params ScreenType[] setOfTypes);

        void ReloadScreen(Action<ScreenType> onReloadComplete = null);

        void ShowScreen(ScreenType type, Action<ScreenType> onScreenChange = null);

        void ShowScreen(ScreenType type, CommandBase transitionCommands, ScreenType fallbackScreen = ScreenType.None);

        void ShowScreen<TController>(ScreenType type, Action<ScreenType> onScreenChange = null)
            where TController : class, IController;

        void ShowScreen<TController, TParams>(ScreenType type, TParams @params)
            where TController : class, IController, IControllerParams<TParams>, new()
            where TParams : BaseScreenParam;

        void ShowScreen<TController, TParams>(ScreenType type, CommandBase transitionCommands, TParams @params)
            where TController : class, IController, IControllerParams<TParams>, new()
            where TParams : BaseScreenParam;
    }
}