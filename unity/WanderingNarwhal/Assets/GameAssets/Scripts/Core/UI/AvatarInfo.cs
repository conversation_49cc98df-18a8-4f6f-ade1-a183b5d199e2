using BBB.BrainCloud;
using BBB.RaceEvents.UI;
using BebopBee.Social;
using GameAssets.Scripts.SocialScreens.Teams.TeamInfo;
using RPC.Social;

namespace BBB
{
    public struct AvatarInfo
    {
        public readonly string AvatarUrl;
        public readonly string Country;

        // some extra customization parameters to be added later

        public AvatarInfo(string avatarUrl, string country = "")
        {
            AvatarUrl = avatarUrl;
            Country = country;
        }

        public AvatarInfo(LeaderboardManager.Score score)
        {
            AvatarUrl = score.Avatar;
            Country = score.Country;
        }

        public AvatarInfo(PublicUserInfo socialUserInfo)
        {
            AvatarUrl = socialUserInfo.Avatar;
            Country = socialUserInfo.Country;
        }
        
        public AvatarInfo(LeaderboardItem leaderboardItem)
        {
            AvatarUrl = leaderboardItem.Avatar;
            Country = string.Empty;
        }

        public AvatarInfo(PlayerEventLeaderboardItem playerEventLeaderboardItem)
        {
            AvatarUrl = playerEventLeaderboardItem.AvatarUrl;
            Country = playerEventLeaderboardItem.Country;
        }

        public AvatarInfo(PlayerRowViewData playerRowViewData)
        {
            AvatarUrl = playerRowViewData.Avatar;
            Country = playerRowViewData.Country;
        }

        public AvatarInfo(TeamMemberInfo teamMemberInfo)
        {
            AvatarUrl = teamMemberInfo.Avatar;
            Country = teamMemberInfo.Country;
        }
    }
}