using System;
using BBB.DI;
using BBB.Core.ResourcesManager;

namespace BBB.UI.Core
{
    public interface IViewPresenter 
    {
        event Action OnCloseClicked;
        event Action OnCloseButtonEvent;
        void Init(IContext previousContext);
        void CacheResources(IResourceCache cache);
        void DisposeResources();
        void Show();

        // This call should notify modal controller via event and if no other is showed during one frame - modal should hide automatically
        void Hide();
        void DisposeContext();
        bool IsVisible();
        void HideFromCloseButton();
        void SetGoActive(bool active);
        void SetTransparency(float transparency);
    }

    public interface IModalsViewPresenter : IViewPresenter
    {
        void ImmediateHide();
    }
    
    public interface IScreensViewPresenter : IViewPresenter
    {
        
    }
}
