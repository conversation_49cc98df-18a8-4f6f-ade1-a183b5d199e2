using System;
using BBB.Controller;
using BBB.DI;
using BBB.UI.Core;
using Core.Debug;
using System.Collections.Generic;
using BBB.Core;
using BBB.Screens;
using BBB.Wallet;
using BebopBee;
using BebopBee.Core.UI;
using Cysharp.Threading.Tasks;
using UnityEngine.Profiling;

namespace BBB
{
    public interface IBaseViewParams
    {
    }

    public class BaseModalParam : IBaseViewParams
    {
    }

    public class BaseScreenParam : BaseModalParam
    {
    }

    public interface IScreenRegisterer
    {
        IScreenRegisterer RegisterView<TController, TViewPresenter>(ScreenType type, Func<TViewPresenter> viewConstructor = null)
            where TController : class, IController<TViewPresenter>, IController, new()
            where TViewPresenter : class, IViewPresenter;

        UniTask PreloadView<TController, TViewPresenter>(ScreenType type)
            where TController : class, IController<TViewPresenter>, IController, new()
            where TViewPresenter : class, IViewPresenter;
    }

    public sealed class ScreensBuilder : ViewsBuilder<ScreenType>, IScre<PERSON>Builder, IScreenRegisterer
    {
        private ScreenType _previousScreenType = ScreenType.None;
        private ScreenType _currenScreenType = ScreenType.None;
        private CommandBase _transitionCommands;
        private CreateScreenCommand _createScreenCommand;
        private ScreenType _fallbackScreen = ScreenType.None;
        private Action<ScreenType> _onScreenChange;
        
        private readonly List<ScreenType> _screensWithWallets = new()
        {
            ScreenType.EpisodeScreen,
            ScreenType.SideMapScreen,
        };

        private IScreensManager _screenManager;

        public ScreenType CurrentScreenType => _currenScreenType;

        public ScreenType PreviousScreenType => _previousScreenType;

        public override void InitializeByContext(IContext context)
        {
            base.InitializeByContext(context);
            _screenManager = context.Resolve<IScreensManager>();
            _screenManager.OnFailedScreenTransition -= OnFailedScreenTransition;
            _screenManager.OnFailedScreenTransition += OnFailedScreenTransition;
            Manager = _screenManager;
        }

        public void Restart()
        {
            _screenManager.OnCurrentScreenTransitionComplete -= _onScreenChange;
            _screenManager.OnFailedScreenTransition -= OnFailedScreenTransition;
            _onScreenChange = null;
            _createScreenCommand?.Unsubscribe();
            _createScreenCommand = null;
        }

        private void OnFailedScreenTransition(ScreenType screenType)
        {
            if (_fallbackScreen != ScreenType.None)
            {
                LoadingProcessTracker.LogShowScreen(_fallbackScreen.ToString(), _screenManager.GetTrackingPreviousScreenType(), "OnFailedScreen");
                ShowScreen<IController>(_fallbackScreen);
            }
        }

        public void ShowPreviousIfAnyOf(Action fallbackDelegate, params ScreenType[] setOfTypes)
        {
            foreach (var screenType in setOfTypes)
            {
                if (screenType != _previousScreenType) continue;
                
                LoadingProcessTracker.LogShowScreen(_previousScreenType.ToString(), _screenManager.GetTrackingPreviousScreenType(), "ShowPreviousIfAnyOf");
                ShowPreviousScreen();
                return;
            }

            fallbackDelegate?.Invoke();
        }


        public bool ShowPreviousScreen()
        {
            if (_previousScreenType == ScreenType.None) return false;
            ShowScreen(_previousScreenType);
            return true;
        }
        
        public void ReloadScreen(Action<ScreenType> onReloadComplete = null)
        {
            ShowScreen(CurrentScreenType, null,
                screenType => screenType == CurrentScreenType
                    ? (IController)_screenManager.GetCurrentController()
                    : CreateView<IController>(screenType, null), onScreenChange: onReloadComplete);
        }

        public void ShowScreen(ScreenType type, Action<ScreenType> onScreenChange = null)
        {
            _fallbackScreen = ScreenType.None;
            _transitionCommands = null;
            ShowScreen<IController>(type, onScreenChange);
        }

        public void ShowScreen(ScreenType type, CommandBase transitionCommands, ScreenType fallbackScreen = ScreenType.None)
        {
            _transitionCommands = transitionCommands;
            ShowScreen<IController>(type, null, fallbackScreen);
        }

        public void ShowScreen<TController>(ScreenType type, Action<ScreenType> onScreenChange = null)
            where TController : class, IController
        {
            ShowScreen<TController>(type, null, onScreenChange: onScreenChange);
        }

        private void ShowScreen<TController>(ScreenType type, IContext context, ScreenType fallbackScreen = ScreenType.None, Action<ScreenType> onScreenChange = null)
            where TController : class, IController
        {
            ShowScreen(type, context, screenType => CreateView<TController>(screenType, context), fallbackScreen, onScreenChange);
        }
        
        private void ShowScreen<TController>(ScreenType type, IContext context, Func<ScreenType, TController> createView, ScreenType fallbackScreen = ScreenType.None, Action<ScreenType> onScreenChange = null)
            where TController : class, IController
        {
            _fallbackScreen = fallbackScreen;
            _previousScreenType = _currenScreenType;

            _createScreenCommand = new CreateScreenCommand(() =>
            {
                Profiler.BeginSample($"CreateView {_currenScreenType}");
                var ctrl = createView.Invoke(_currenScreenType);
                Profiler.EndSample();

                ctrl.SetWalletWidgetState(_screensWithWallets.Contains(_currenScreenType)
                    ? WalletWidgetState.HudDriven
                    : WalletWidgetState.AlwaysHidden);
                return ctrl;
            });

            type = Show(type, onScreenChange: onScreenChange);
            _currenScreenType = type;
        }

        public void ShowScreen<TController, TParams>(ScreenType type, TParams @params)
            where TController : class, IController, IControllerParams<TParams>, new()
            where TParams : BaseScreenParam
        {
            ShowScreen<TController, TParams>(type, @params, context: null);
        }

        public void ShowScreen<TController, TParams>(ScreenType type, CommandBase transitionCommands, TParams @params)
            where TController : class, IController, IControllerParams<TParams>, new()
            where TParams : BaseScreenParam
        {
            _transitionCommands = transitionCommands;
            ShowScreen<TController, TParams>(type, @params, context: null);
        }

        private void ShowScreen<TController, TParams>(ScreenType type, TParams @params, IContext context)
            where TController : class, IController, IControllerParams<TParams>, new()
            where TParams : BaseScreenParam
        {
            ShowScreen(type, context, screenType => CreateView<TController, TParams>(screenType, @params, context));
        }

        
        private ScreenType Show(ScreenType screenType, ShowMode showMode = ShowMode.Immediate, Action<ScreenType> onScreenChange = null)
        {
            if (onScreenChange != null)
            {
                _onScreenChange = onScreenChange;
                _screenManager.OnCurrentScreenTransitionComplete -= _onScreenChange;
                _screenManager.OnCurrentScreenTransitionComplete += _onScreenChange;   
            }
            return _screenManager.ShowScreen(screenType, _transitionCommands, _createScreenCommand);
        }

        public override void Hide<TController>(TController ctrl)
        {
            _screenManager.HideScreen(ctrl);
        }

        public IScreenRegisterer RegisterView<TController, TViewPresenter>(ScreenType type, Func<TViewPresenter> viewConstructor = null)
            where TController : class, IController<TViewPresenter>, IController, new()
            where TViewPresenter : class, IViewPresenter
        {
            _viewsFactory.Register<IContext>(type, context =>
            {
#if BBB_DEBUG
                var logStopWatch = new LogStopwatch();
                logStopWatch.Start();
#endif

                var ctrl = Manager.GetOrCreateController<TController, TViewPresenter, ScreenType>(type);
                ctrl.Init(context);

                Profiler.BeginSample($"viewConstructor() {typeof(TViewPresenter).FullName}");
                var view = viewConstructor.SafeInvoke();
                Profiler.EndSample();

                if (view != null)
                {
                    Profiler.BeginSample($"CacheScreenView {typeof(TViewPresenter).FullName}");
                    ((ScreensManager)Manager).CacheScreenView(type, view, ctrl);
                    Profiler.EndSample();
                    Profiler.BeginSample($"ctrl.SetView {typeof(TController).FullName}");
                    ctrl.SetView(view);
                    Profiler.EndSample();

                    return ctrl;
                }

                var cachedView = Manager.TryGetCachedView<TController, TViewPresenter, ScreenType>(type, ctrl);
                if (cachedView != null)
                {
                    OnViewPreloaded(cachedView);
                    return ctrl;
                }

                Manager.PreloadView<TController, TViewPresenter, ScreenType>(type, ctrl).ContinueWith(OnViewPreloaded);
                return ctrl;

                void OnViewPreloaded(IViewPresenter viewPresenter)
                {
                    if (viewPresenter == null)
                        BDebug.LogErrorFormat(LogCat.CoreViews, "Can't find view for type:{0} ctrl:{1} view:{2}", type, typeof(TController),
                            typeof(TViewPresenter));

                    ctrl.SetView((TViewPresenter)viewPresenter);
#if BBB_DEBUG
                    logStopWatch.StopLog($"View {type} created");
#endif
                }
            });
            return this;
        }

        public async UniTask PreloadView<TController, TViewPresenter>(ScreenType type)
            where TController : class, IController<TViewPresenter>, IController, new()
            where TViewPresenter : class, IViewPresenter
        {
            Profiler.BeginSample("GetOrCreateController");
            var ctrl = Manager.GetOrCreateController<TController, TViewPresenter, ScreenType>(type);
            Profiler.EndSample();

            Profiler.BeginSample("PreloadView");
            await Manager.PreloadView<TController, TViewPresenter, ScreenType>(type, ctrl);
            Profiler.EndSample();
        }

        public override void ReleaseByContext(IContext context)
        {
            base.ReleaseByContext(context);
            _screenManager.OnFailedScreenTransition -= OnFailedScreenTransition;
        }
    }
}