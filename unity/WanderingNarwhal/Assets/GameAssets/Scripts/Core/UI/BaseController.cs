using System;
using System.Collections.Generic;
using BBB.Audio;
using BBB.Core;
using BBB.DI;
using BBB.UI.Core;
using BBB.Wallet;
using BebopBee.Core.Audio;
using Bebopbee.Core.Systems.Ticksystem;
using UnityEngine.Profiling;
using Debug = System.Diagnostics.Debug;

namespace BBB.Controller
{
    public abstract class BaseController<TViewPresenter> : IController<TViewPresenter>, IControllerParams<IBaseViewParams>
        where TViewPresenter : IViewPresenter
    {
        public void Setup(IBaseViewParams @params)
        {
        }

        public IViewPresenter GetView => View;

        public bool ClosedIntentionally { get; private set; }
        public virtual bool ShouldHideHud => true;

        public event Action OnClose;
        public event Action Shown;
        protected TViewPresenter View;
        protected IModalsBuilder ModalsBuilder;
        protected IScreensBuilder ScreensBuilder;
        protected IScreensManager ScreensManager;

        private ITickSystem _tickSystem;
        private readonly List<ITickable> _tickables = new ();

        public virtual void Init(IContext previousContext)
        {
            ModalsBuilder = previousContext.Resolve<IModalsBuilder>();
            ScreensBuilder = previousContext.Resolve<IScreensBuilder>();
            ScreensManager = previousContext.Resolve<IScreensManager>();
            _tickSystem = previousContext.Resolve<ITickSystem>();
        }
        

        protected virtual void OnContextInitialized()
        {
        }

        public virtual void SetView(TViewPresenter view)
        {
            Debug.Assert(view != null, "Trying to set View to null.");
            Debug.Assert(View == null, "View already set");
            View = view;
        }

        public virtual void OnDiscard(){}
        public virtual void Destroy()
        {
            Unsubscribe();
            OnClose = null;
            Shown = null;
        }

        public bool CanBypassTransition()
        {
            return false;
        }

        public virtual bool IsReady()
        {
            return View != null;
        }

        public virtual void DisposeContext()
        {
        }

        public virtual void Show()
        {
            if (IsReady())
            {
                Subscribe();
                Profiler.BeginSample($"View[{View.GetType().Name}].Show");
                View.Show();
                Profiler.EndSample();
                Profiler.BeginSample($"[{this.GetType().Name}].OnShow");
                OnShow();
                Profiler.EndSample();
                Shown?.Invoke ();
            }
            else
            {
                BDebug.LogErrorFormat(LogCat.CoreViews, "Can't show not ready view {0}", typeof(TViewPresenter));
            }

            foreach (var tickable in _tickables)
                _tickSystem.Add(tickable);
        }

        private void Subscribe()
        {
            Unsubscribe();
            View.OnCloseClicked += Hide;
            View.OnCloseButtonEvent += OnCloseButtonClicked;
        }

        private void Unsubscribe()
        {
            View.OnCloseClicked -= Hide;
            View.OnCloseButtonEvent -= OnCloseButtonClicked;
        }

        public virtual void OnPostShow()
        {
            
        }

        public virtual void Hide()
        {
            OnHide();
            foreach (var tickable in _tickables)
                _tickSystem.Remove(tickable);
            OnClose?.Invoke();
            if (IsReady())
            {
                Unsubscribe();
                View.Hide();
            }
        }

        protected virtual void OnHide()
        {
        }

        protected virtual void OnShow()
        {
            ClosedIntentionally = false;
        }

        /// <summary>
        /// Intentionally close the modal with X button or tap on the screen
        /// </summary>
        protected virtual void OnCloseButtonClicked()
        {
            ClosedIntentionally = true;
            View.HideFromCloseButton();
        }
        
        protected virtual void OnCloseClicked()
        {
            Hide();
            PlayCloseFX();
        }
        
        private void PlayCloseFX()
        {
            AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
        }

        public void SetWalletWidgetState(WalletWidgetState state)
        {
        }
    }
}