using System.Collections.Generic;
using BBB.Controller;
using BBB.DI;
using BBB.Screens;
using BBB.UI.Core;
using Bebopbee.Core.Systems.Ticksystem;

namespace BBB.Core
{
    public interface IScreensController
    {
        bool ShouldHideHud { get; }
        
        void OnScreenCreated();
        
        void TransitionStarted();
        void TransitionCompleted();
        bool IsInSameGroup(ScreenType screenType);
    }

    public class ScreenContext : UnityContext
    {
        public ScreenContext() : base()
        {
            
        }
    }
    
    public class BaseScreensController<TViewPresenter> : BaseController<TViewPresenter>, IScreensController
        where TViewPresenter : IViewPresenter
    {
        protected IContext Context;
        
        public override void Init(IContext previousContext)
        {
            if (Context == null)
            {
                Context = InitializeContext(previousContext);
                base.Init(Context);
                OnContextInitialized();
            }
            else
            {
                base.Init(Context);
            }
        }

        public void TransitionCompleted()
        {
            OnTransitionCompleted();
            (View as ScreensViewPresenter).TransitionCompleted();
        }

        public virtual bool IsInSameGroup(ScreenType screenType)
        {
            return false;
        }

        public void TransitionStarted()
        {
            OnTransitionStarted();
            (View as ScreensViewPresenter).TransitionStarted();
        }

        protected virtual void OnTransitionCompleted()
        {
        }

        protected virtual void OnTransitionStarted()
        {
        }

        public override void SetView(TViewPresenter view)
        {
            view.Init (Context);
            View = view;
        }

        public override void DisposeContext()
        {
            if (Context != null)
            {
                BDebug.Log("<color=red>BaseScreenController disposing context</color>");
                Context.Releaser.ReleaseContext();
                Context = null;
            }
        }

        protected virtual IContext InitializeContext(IContext previousContext)
        {
            var context = new ScreenContext();
            OnInitializeByContext(context);
            context.RegisterContext(previousContext);
            return context;
        }

        protected virtual void OnInitializeByContext(UnityContext context)
        {
        }

        public override void Hide()
        {
            base.Hide();
            ScreensBuilder.Hide(this);
        }

        public virtual void OnScreenCreated()
        {
            
        }
    }
}