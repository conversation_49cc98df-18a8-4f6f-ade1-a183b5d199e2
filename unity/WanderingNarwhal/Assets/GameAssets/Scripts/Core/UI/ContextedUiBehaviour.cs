using System;
using BBB.Core;
using BBB.DI;

namespace BBB.UI.Core
{
    public abstract class ContextedUiBehaviour : BbbMonoBehaviour
    {
        protected static event Action ContextProvided;
        protected static IContext _context;
        private bool _isInitialized;

#if BBB_DEBUG
        protected static IContext DebugContext => _context;
#endif

        protected bool Initialized => _isInitialized;

        public static void SetupRootContext(IContext context)
        {
            BDebug.Log(LogCat.General, $"ContextUIBehaviour SetupRootContext {context}");
            _context = context;
            if (context != null)
                ContextProvided?.Invoke();
        }

        protected virtual void Awake()
        {
            if (_isInitialized)
                return;

            if (_context != null)
            {
                InitWithContextInternal(_context);
                _isInitialized = true;
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();

            ContextProvided -= LazyInit;
            UnInitWithContextInternal(_context);
        }

        protected virtual void LazyInit()
        {
            ContextProvided -= LazyInit;

            if (_isInitialized)
                return;

            if (_context != null)
            {
                InitWithContextInternal(_context);
                _isInitialized = true;
            }
            else
            {
                ContextProvided += LazyInit;
                BDebug.Log(LogCat.General, "Context is null on LazyInit. Subscribing for when context is injected");
            }
        }

        protected abstract void InitWithContextInternal(IContext context);

        protected virtual void UnInitWithContextInternal(IContext context)
        {
        }
    }
}