using System;
using BBB.Core.ResourcesManager;
using BBB.DI;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace BBB.UI.Core
{
    public class AsyncLoadableGameObject : ContextedUiBehaviour
    {
        [SerializeField] private string _nameToLoadOnAwake;
        
        private Transform _holderTransform;
        private GenericResourceProvider _genericResourceProvider;
        private string _loadedName;
        private GameObject _instantiatedGo;
        
        protected override void InitWithContextInternal(IContext context)
        {
            _holderTransform = transform;
            _genericResourceProvider = context.Resolve<GenericResourceProvider>();
            
            if(!string.IsNullOrWhiteSpace(_nameToLoadOnAwake))
                Load_Internal(_nameToLoadOnAwake, null);
                
        }

        protected override void UnInitWithContextInternal(IContext context)
        {
            if (!ReferenceEquals(_instantiatedGo, null))
            {
                DestroyImmediate(_instantiatedGo);
                _instantiatedGo = null;
            }
            
            if(_genericResourceProvider != null && !string.IsNullOrWhiteSpace(_loadedName))
                _genericResourceProvider.ReleaseCached(_loadedName);

            _genericResourceProvider = null;
            _loadedName = null;
        }

        public bool IsLoadedName(string value)
        {
            return _loadedName == value;
        }

        public void Load(string prefabToLoad, Action<GameObject> callback = null)
        {
            if (string.IsNullOrWhiteSpace(prefabToLoad))
                return;
            
            if(!string.IsNullOrWhiteSpace(_nameToLoadOnAwake))
                throw new Exception("Can not load gameobject because other loading name is specified");
            
            LazyInit();
                
            Load_Internal(prefabToLoad, callback).Forget(Debug.LogError);
        }

        private async UniTask Load_Internal(string prefabToLoad, Action<GameObject> callback)
        {
            var prefab = await _genericResourceProvider.CacheAndLoadAsync<GameObject>(this, prefabToLoad);
            UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[{prefab.name}]");
            _instantiatedGo = Instantiate(prefab, _holderTransform, false);
            UnityEngine.Profiling.Profiler.EndSample();
            _loadedName = prefabToLoad;
            callback.SafeInvoke(_instantiatedGo);
        }
    }
}