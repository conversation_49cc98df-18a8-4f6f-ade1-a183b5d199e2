using System;
using BBB;
using BBB.Core;
using UnityEngine;

public enum OffsetDirection
{
    Top = -1,
    Bottom = 1
}

public enum CorrectionType
{
    Translate,
    Expand,
    ExpandFromTopOnly
}

public class SafeAreaCorrection : BbbMonoBehaviour
{
    [SerializeField] private OffsetDirection _offsetDirection;
    [SerializeField] private float _offset;
    [SerializeField] private CorrectionType _type;

    private RectTransform _rect;
    private Canvas[] _canvas;
    private Canvas _mainCanvas;

    void Start()
    {
        _canvas = GetComponentsInParent<Canvas>();
        _mainCanvas = null;

        foreach (var canvas in _canvas)
        {
            if (!Predicate(canvas)) continue;
            
            _mainCanvas = canvas;
            break;
        }

        _rect = GetComponent<RectTransform>();
        if (_mainCanvas != null)
        {
            if (_type == CorrectionType.Translate)
            {
                ApplyTranslateCorrection();
            }
            else
            {
                ApplyExpandCorrection();
            }
        }
        else
        {
            BDebug.LogError(LogCat.General, $"SafeAreCorrection Canvas missing");
        }

        return;

        bool Predicate(Canvas canvas)
        {
            return canvas.gameObject != gameObject;
        }
    }

    private void ApplyExpandCorrection()
    {
        var area = SafeAreaProvider.GetSafeAreaRect();

        if (_type == CorrectionType.ExpandFromTopOnly)
        {
            area.height += area.y;
            area.y = 0;
        }

        var anchorMin = area.position;
        var anchorMax = area.position + area.size;
        anchorMin.x /= Screen.width;
        anchorMin.y /= Screen.height;
        anchorMax.x /= Screen.width;
        anchorMax.y /= Screen.height;
        _rect.anchorMin = anchorMin;
        _rect.anchorMax = anchorMax;
    }

    private void ApplyTranslateCorrection()
    {
        var area = SafeAreaProvider.GetSafeAreaRect();

        var offset = Vector3.zero;

        var viewportHeight = _mainCanvas.RectTransform().rect.height; // * (1f / _mainCanvas.scaleFactor);

        offset.y = _offsetDirection switch
        {
            OffsetDirection.Top => -(viewportHeight * ((Screen.height - (area.position.y + area.height)) / Screen.height)) + _offset,
            OffsetDirection.Bottom => viewportHeight * (area.position.y / Screen.height) + _offset,
            _ => throw new ArgumentOutOfRangeException()
        };

        transform.localPosition += offset;
    }
}