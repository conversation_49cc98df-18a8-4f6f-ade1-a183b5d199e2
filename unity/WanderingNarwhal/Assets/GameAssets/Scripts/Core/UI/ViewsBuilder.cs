using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading;
using BBB.Controller;
using BBB.Core;
using BBB.DI;
using BBB.UI.Core;
using Core.Debug;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace BBB
{
    public class ViewsContext : ContextBase
    {
        protected override void OnRegisterContext()
        {
        }
    }

    public class ViewsFactory<TViewType> : Factory<TViewType, IController>
    {
    }

    public class ViewsBuilder<TViewType> : IViewsBuilder, IContextInitializable, IContextReleasable
        where TViewType : struct, IConvertible
    {
        protected readonly ViewsContext Context;
        private IContext _defaultContext;
        protected readonly ViewsFactory<TViewType> _viewsFactory;
        private readonly Factory<KeyValuePair<TViewType, Type> , IControllerParams<IBaseViewParams>> _viewsFactoryParams;

        protected IViewsManager Manager;

        protected ViewsBuilder()
        {
            Context = new ViewsContext();
            _viewsFactory = new ViewsFactory<TViewType>();
            _viewsFactoryParams = new Factory<KeyValuePair<TViewType, Type>, IControllerParams<IBaseViewParams>>();
        }

        public void SetDefaultContext(IContext context)
        {
            _defaultContext = context;
        }

        public virtual void InitializeByContext(IContext context)
        {
            _defaultContext = context;
            Context.RegisterContext(context);
        }

        public virtual void ReleaseByContext(IContext context)
        {
            _defaultContext = null;
            Context.Releaser.ReleaseContext();
        }

        protected TController CreateView<TController>(TViewType type, IContext context)
            where TController : class, IController
        {
            context ??= _defaultContext;
            return _viewsFactory.Create(type, context) as TController;
        }

        protected IController CreateView(TViewType type, IContext context)
        {
            context ??= _defaultContext;
            return _viewsFactory.Create(type, context);
        }
        

        protected TController CreateView<TController, TParams>(TViewType type, TParams @params, IContext context)
            where TController : class, IController, IControllerParams<TParams>, new()
            where TParams : class, IBaseViewParams
        {
            var pair = new KeyValuePair<TViewType, Type>(type, typeof(TParams));
            if (!_viewsFactoryParams.IsRegistered(pair))
            {
                _viewsFactoryParams.Register<TParams, IContext>(pair, (param, c) =>
                {
                    var ctrl = CreateView<TController>(type, c);
            
                    WaitForControllerSetupAsync(ctrl, param).Forget();

                    return ctrl as IControllerParams<IBaseViewParams>;
                });
            }

            return _viewsFactoryParams.Create(pair, @params, context) as TController;
        }

        private async UniTask WaitForControllerSetupAsync<TParams>(IController ctrl, TParams param, CancellationToken cancellationToken = default) 
            where TParams : IBaseViewParams
        {
            await UniTask.WaitUntil(
                ctrl,
                c => c.IsReady(),
                PlayerLoopTiming.Update,
                cancellationToken
            );

            (ctrl as IControllerParams<TParams>)?.Setup(param);
        }

        public virtual void Hide<TController>(TController ctrl) where TController : IController
        {
        }
    }
}