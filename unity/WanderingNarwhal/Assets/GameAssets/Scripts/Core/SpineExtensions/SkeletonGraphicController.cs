using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using BebopBee.Core.Audio;
using Spine;
using Spine.Unity;
using UnityEngine;

[RequireComponent(typeof(SkeletonGraphic))]
public class SkeletonGraphicController : BbbMonoBehaviour
{
    [Serializable]
    public class AnimSoundPair
    {
        public string AnimName;
        public string SoundId;
    }

    [SerializeField] private string _currentIdleAnim = "Idle";
    [SerializeField] private List<AnimSoundPair> _animSoundMap;

    private SkeletonGraphic _skeletonGraphic;
    private bool _isLocked;
    private TrackEntry _currentTrackEntry;

    public void SetCurrentIdleAnimation(string currentIdleAnim)
    {
        _currentIdleAnim = currentIdleAnim;
    }

    private void LazyInitialize()
    {
        if (_skeletonGraphic == null)
            _skeletonGraphic = GetComponent<SkeletonGraphic>();
    }

    public bool HasAnimation(string animName)
    {
        LazyInitialize();
        
        return _skeletonGraphic.SkeletonData.FindAnimation(animName) != null;
    }

    public void FlipX(bool value = true)
    {
        LazyInitialize();
        
        _skeletonGraphic.initialFlipX = value;
        _skeletonGraphic.Initialize(true);
    }

    public void PlayAnimation(string animName, bool loop = false)
    {
        LazyInitialize();

        if (!_skeletonGraphic.enabled)
            return;

        if (_isLocked || !_skeletonGraphic.gameObject.activeInHierarchy)
            return;

        var anim = animName.IsNullOrEmpty() ? null : _skeletonGraphic.SkeletonData.FindAnimation(animName);
        if (anim != null)
        {
            if (_currentTrackEntry != null)
                _currentTrackEntry.Complete -= OnCurrentEntryComplete;

            _currentTrackEntry = _skeletonGraphic.AnimationState.SetAnimation(0, animName, loop);

            PlaySoundFor(animName);

            if (loop)
                _currentTrackEntry = null;
            else
                _currentTrackEntry.Complete += OnCurrentEntryComplete;
        }
        else
        {
            BDebug.LogWarning(LogCat.Spine, "Can not find animation " + animName + " in " + gameObject.name);
        }
    }

    private void PlaySoundFor(string animName)
    {
        if (_animSoundMap != null)
        {
            foreach (var pair in _animSoundMap)
                if (pair.AnimName == animName)
                    AudioProxy.PlaySound(pair.SoundId);
        }
    }

    private void OnCurrentEntryComplete(TrackEntry entry)
    {
        _currentTrackEntry.Complete -= OnCurrentEntryComplete;
        _currentTrackEntry = null;

        foreach (var skeletonDataAnimation in _skeletonGraphic.SkeletonData.Animations)
        {
            if (skeletonDataAnimation.Name != _currentIdleAnim) continue;
            
            _skeletonGraphic.AnimationState.SetAnimation(0, _currentIdleAnim, true);
            return;
        }

        Debug.LogErrorFormat("Can not find animation {0} in {1}", _currentIdleAnim, _skeletonGraphic.gameObject.name);
        _skeletonGraphic.AnimationState.SetAnimation(0, "Idle", true);
    }


    public void Lock(bool value)
    {
        _isLocked = value;
    }

    public void Freeze(bool value)
    {
        if (_skeletonGraphic != null)
            _skeletonGraphic.freeze = value;
    }

    public void Reset()
    {
        if (_skeletonGraphic != null) _skeletonGraphic.AnimationState?.ClearTracks();

        if (_currentTrackEntry != null)
        {
            _currentTrackEntry.Complete -= OnCurrentEntryComplete;
            _currentTrackEntry = null;
        }

        _isLocked = false;
    }
}