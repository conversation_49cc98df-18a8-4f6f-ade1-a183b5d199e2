using System.Collections.Generic;
using BBB;
using Spine;
using Spine.Unity;
using UnityEngine;

namespace GameAssets.Scripts.Core.SpineExtensions
{
    public class SkeletonGraphicRandomPlayer : BbbMonoBehaviour
    {
        [SerializeField] private SkeletonGraphic _skeletonGraphic;

        private TrackEntry _currentTrackEntry;

        private string[] _animNames;
        private Dictionary<string, float> _weights;
        private int _spineTrackToUse;

        public void StopMetaTrack()
        {
            if (_currentTrackEntry != null)
            {
                _currentTrackEntry.Complete -= OnCurrentEntryComplete;
            }

            _currentTrackEntry = null;

            _animNames = null;

            _weights?.Clear();

            _spineTrackToUse = 0;
        }

        public void ClearTracks()
        {
            _skeletonGraphic.AnimationState?.ClearTracks();
        }

        public void PlayRandomSequentialMix(string[] animNames, float[] weights = null, int track = 0)
        {
            if (_currentTrackEntry != null)
                _currentTrackEntry.Complete -= OnCurrentEntryComplete;

            _currentTrackEntry = null;

            _spineTrackToUse = track;

            _animNames = animNames;

            if (_weights == null)
                _weights = new Dictionary<string, float>();
            else
            {
                _weights.Clear();
            }

            if (weights != null)
            {
                for (int i = 0; i < animNames.Length; i++)
                {
                    _weights.Add(animNames[i], weights[i]);
                }
            }

            if (!_skeletonGraphic.enabled)
                return;

            Random.InitState((int) (Time.time));
            RunMetaClip(track);
        }

        private void RunMetaClip(int track = 0)
        {
            var animName = _weights.Count > 0
                ? _animNames.GetRandomItemByWeight(str => _weights.GetValueOrDefault(str, 0f))
                : _animNames.GetRandomItem();

            var anim = animName.IsNullOrEmpty() ? null : _skeletonGraphic.SkeletonData.FindAnimation(animName);
            if (anim != null)
            {
                if (_currentTrackEntry != null)
                    _currentTrackEntry.Complete -= OnCurrentEntryComplete;

                _skeletonGraphic.AnimationState.ClearTrack(0);
                _currentTrackEntry = _skeletonGraphic.AnimationState.SetAnimation(track, animName, false);
               
                _currentTrackEntry.Complete += OnCurrentEntryComplete;
            }
            else
            {
                Debug.LogError("Can not find animation " + animName + " in " + gameObject.name);
            }
        }

        private void OnCurrentEntryComplete(TrackEntry trackentry)
        {
            RunMetaClip(_spineTrackToUse);
        }
    }
}