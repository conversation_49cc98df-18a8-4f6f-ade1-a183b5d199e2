using System;
using System.Collections.Generic;

namespace BBB.Core.FSM
{
    public enum ProcessState
    {
        Denied,
        Done,
        Processed
    }

    public class StateMachine : IStateMachine
    {
        private readonly IState _origin;
        private IState _currentState;

        public StateMachine(IState origin)
        {
            _origin = origin;
            _currentState = origin;
        }

        public void Handle(IStateEvent @event)
        {
            switch (_currentState.Execute(@event))
            {
                case ProcessState.Denied:
                    break;
                case ProcessState.Done:
                    _currentState = FindTransition(_currentState, @event) ?? _origin;
                    break;
                case ProcessState.Processed:
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }

        public void Reset()
        {
            _currentState = _origin;
        }

        private static IState FindTransition(IState currentState, IStateEvent @event)
        {
            foreach (var transition in currentState.Transitions)
            {
                var state = transition.Execute(@event);
                if (state == ProcessState.Denied) continue;
                
                return transition;
            }

            return null;
        }
    }

    public abstract class BaseState : IState
    {
        protected BaseState()
        {
            Transitions = new List<IState>();
        }

        public event Action OnEnter = delegate { };
        public event Action OnExit = delegate { };
        public event Action OnExecute = delegate { };

        public List<IState> Transitions { get; private set; }

        public abstract ProcessState Execute(IStateEvent @event);

        public void AddTransition(IState nextState)
        {
            Transitions.Add(nextState);
        }

        public void AddTransition(params IState[] nextStates)
        {
            Transitions.AddRange(nextStates);
        }
    }

    public interface IStateEvent
    {
    }

    public interface IState
    {
        event Action OnEnter;
        event Action OnExit;
        event Action OnExecute;

        List<IState> Transitions { get; }
        ProcessState Execute(IStateEvent @event);
        void AddTransition(IState nextState);
        void AddTransition(params IState[] nextStates);
    }

    public interface IStateMachine
    {
        void Handle(IStateEvent @event);
    }
}