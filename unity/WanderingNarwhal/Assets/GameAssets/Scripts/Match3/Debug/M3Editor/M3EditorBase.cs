using System.Collections.Generic;
using UnityEngine;

#if UNITY_EDITOR


// ReSharper disable once CheckNamespace
namespace BBB.M3Editor
{
    // ReSharper disable once UnusedMember.Global
    public class M3EditorBase : IM3EditorSystem
    {
        protected List<GameObject> ObjectsDisabledDuringPlay = new List<GameObject>();
        protected List<GameObject> ObjectsEnableDuringPlay = new List<GameObject>();

        protected readonly M3Editor _m3Editor;
        protected GameController GameController => _m3Editor.GameController;
        protected Grid Grid => _m3Editor.GameController.Grid;

        public M3EditorBase(M3Editor m3Editor)
        {
            _m3Editor = m3Editor;
        }

        public virtual void SetUpUi()
        {
            ObjectsDisabledDuringPlay.Clear();
            ObjectsEnableDuringPlay.Clear();
        }

        public virtual void UpdateUi() {}
        public virtual void Clear()
        {

        }

        public virtual void OnGameRun(bool playState)
        {
            foreach(var go in ObjectsDisabledDuringPlay)
                go.SetActive(!playState);

            foreach (var go in ObjectsEnableDuringPlay)
                go.SetActive(playState);
        }
    }
}
#endif
