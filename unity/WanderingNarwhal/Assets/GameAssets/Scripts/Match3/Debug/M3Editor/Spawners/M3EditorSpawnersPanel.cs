#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB.DI;
using BBB.Match3.Systems;
using BBB.UI;
using GameAssets.Scripts.Match3.Debug.M3Editor.Tools;
using UnityEngine;
using UnityEngine.UI;
using UnityEditor;

namespace BBB.M3Editor
{
    public interface ISpawnerTileImageProvider
    {
        void GetTileSprite(TileSpawnSettings tile, out Sprite mainSprite, out Sprite overlaySprite, out float overlayAlpha, out Sprite secondOverlaySprite, out float secondOverlayAlpha);
    }

    public class M3EditorSpawnersPanel : BbbMonoBehaviour, ISpawnerTileImageProvider, IContextInitializable, IM3EditorSystem
    {
        [Serializable]
        public class SpawnerTilesAssetPreviewData
        {
            public TileAsset AssetType;
            public bool checkTileKind;
            public TileKinds TileKind = TileKinds.Green; 
            public Sprite Sprite;
        }

        public Button AddSpawnerButton;
        public Toggle ShowOnlyUsedToggle;
        public InputField SearchInput;
        public M3EditorSpawnerListItem ListItemTemplate;
        public Transform ListContentRoot;

        private SpawnerSettingsContainer _spawnerSettingsContainer;
        private List<M3EditorSpawnerListItem> _instances = new List<M3EditorSpawnerListItem>();
        private List<SpawnerSettings> _filteredSpawners = new List<SpawnerSettings>();
        private M3EditorSpawnerSettingsPopup _settingsPopup;
        private M3Editor _m3Editor;

        [SerializeField]
        private List<SpawnerTilesAssetPreviewData> _assetsPreviewSprites = new List<SpawnerTilesAssetPreviewData>();

        [SerializeField]
        private Sprite _icePreviewSprite;

        [SerializeField]
        private float _iceAlpha = 1f;

        [SerializeField]
        private Sprite _vasePreviewSprite;

        [SerializeField]
        private float _vaseAlpha = 0.5f;

        private void Awake()
        {
            ListItemTemplate.gameObject.SetActive(false);
            AddSpawnerButton.onClick.AddListener(AddListItem);
            CacheSettingsContainer();
            SearchInput.onValueChanged.AddListener((s) => UpdateUi());
            ShowOnlyUsedToggle.isOn = false;
            ShowOnlyUsedToggle.onValueChanged.AddListener((s) => UpdateUi());
        }

        public void InitializeByContext(IContext context)
        {
            _settingsPopup = context.Resolve<EditorLevelControllerLauncher>().SpawnerSettingsPopup;
            _settingsPopup.OnClosedEvent += OnSpawnSettngsPopupClosed;

            _m3Editor = context.Resolve<EditorLevelControllerLauncher>().M3Editor;
        }

        public void SetUpUi()
        {
        }

        private void OnSpawnSettngsPopupClosed()
        {
            UnityEditor.EditorUtility.SetDirty(_spawnerSettingsContainer);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            UpdateUi();
        }

        public void UpdateUi()
        {
            CacheSettingsContainer();

            if (_spawnerSettingsContainer == null)
            {
                return;
            }

            _filteredSpawners.Clear();

            int maxIndex = 0;
            for (int i = 0; i < _spawnerSettingsContainer.Spawners.Count; i++)
            {
                var spawner = _spawnerSettingsContainer.Spawners[i];
                if (IsSpawnerPassedExistenceOnLevelFilter(spawner.Uid)
                    && IsSpawnerPassedNameFilter(spawner.Name, SearchInput.text))
                {
                    _filteredSpawners.Add(spawner);
                }

                if (spawner.Uid > maxIndex)
                {
                    maxIndex = spawner.Uid;
                }
            }

            SetListItemsCount(_filteredSpawners.Count);

            for (int i = 0; i < _instances.Count; i++)
            {
                _instances[i].RefreshView(_filteredSpawners[i], this);

                // Active remove button only for last item.
                _instances[i].SetActiveRemoveButton(maxIndex > 0 && _filteredSpawners[i].Uid == maxIndex);
            }
        }

        private bool IsSpawnerPassedExistenceOnLevelFilter(int spawnerUid)
        {
            if (!this.ShowOnlyUsedToggle.isOn) return true;
            var grid = _m3Editor.Grid;
            if (grid == null) return false;
            foreach (var cell in grid.Cells)
            {
                if (cell.SpawnerUid == spawnerUid)
                {
                    return true;
                }
            }

            return false;
        }

        private bool IsSpawnerPassedNameFilter(string spawnerName, string enteredName)
        {
            return enteredName.IsNullOrEmpty() || spawnerName.ToLower().StartsWith(enteredName.ToLower());
        }

        public void OnGameRun(bool playState)
        {
        }

        private void SetListItemsCount(int count)
        {
            while (_instances.Count > count)
            {
                var last = _instances.Count - 1;
                var item = _instances[last];
                if (item != null)
                {
                    Destroy(item.gameObject);
                }

                _instances.RemoveAt(last);
            }

            while (_instances.Count < count)
            {
                _instances.Add(CreateNewItem());
            }
        }

        private void AddListItem()
        {
            CacheSettingsContainer();

            if (_spawnerSettingsContainer == null)
            {
                return;
            }

            var newSpawner = new SpawnerSettings()
            {
                TilesSettings = new[]
                {
                    new TileSpawnSettings()
                    {
                        Asset = TileAsset.Simple,
                        Kind = TileKinds.Undefined,
                        ProbabilityWeight = 1f,
                    }
                }
            };
            newSpawner.Uid = _spawnerSettingsContainer.Spawners.Count;
            _spawnerSettingsContainer.Spawners.Add(newSpawner);
            UnityEditor.EditorUtility.SetDirty(_spawnerSettingsContainer);
            SearchInput.text = "";
            UpdateUi();
        }

        private M3EditorSpawnerListItem CreateNewItem()
        {
            var item = Instantiate(ListItemTemplate, parent: ListContentRoot);

            item.OnSelectedClickEvent += OnItemSelectClicked;
            item.OnDeletedClickEvent += OnItemRemoveClicked;
            item.OnConfigureClickEvent += OnItemConfigureClicked;

            item.gameObject.SetActive(true);
            return item;
        }

        private void OnItemSelectClicked(int uid)
        {
            if (uid < 0) return;
            var m3SpawnerTool = new SpawnerCellEditorTool(_m3Editor._m3Systems);
            _m3Editor.SelectTool(m3SpawnerTool, m3SpawnerTool.GetType().Name, uid);
        }

        private void OnItemRemoveClicked(int uid)
        {
            if (uid == 0) return;
            CacheSettingsContainer();
            if (_spawnerSettingsContainer == null) return;
            if (_spawnerSettingsContainer.Spawners.Count == 0) return;

            SpawnerSettings item = _spawnerSettingsContainer.FindItemByUid(uid);
            if (item == null) return;
            if (item.Uid != uid) return;
            _spawnerSettingsContainer.Spawners.Remove(item);
            UpdateUi();
        }

        private void OnItemConfigureClicked(int uid)
        {
            CacheSettingsContainer();
            if (_spawnerSettingsContainer == null) return;

            for (int i = 0; i < _spawnerSettingsContainer.Spawners.Count; i++)
            {
                if (_spawnerSettingsContainer.Spawners[i].Uid == uid)
                {
                    _settingsPopup.InitForSpawner(_spawnerSettingsContainer.Spawners[i], imageProvider: this);
                    _settingsPopup.Show();
                    break;
                }
            }
        }

        private void CacheSettingsContainer()
        {
            if (_spawnerSettingsContainer == null)
            {
                var assets = AssetDatabase.FindAssets("t: " + typeof(SpawnerSettingsContainer).Name);
                if (assets.Length > 0)
                {
                    var path = AssetDatabase.GUIDToAssetPath(assets[0]);
                    _spawnerSettingsContainer = AssetDatabase.LoadAssetAtPath<SpawnerSettingsContainer>(path);
                }
            }
        }

        public void GetTileSprite(TileSpawnSettings tile, out Sprite mainSprite, out Sprite overlaySprite, out float overlayAlpha, out Sprite secondOverlaySprite, out float secondOverlayAlpha)
        {
            mainSprite = null;
            overlaySprite = null;
            secondOverlaySprite = null;
            overlayAlpha = 1f;
            secondOverlayAlpha = 1f;

            for (int i = 0; i < _assetsPreviewSprites.Count; i++)
            {
                if (_assetsPreviewSprites[i].AssetType == tile.Asset)
                {
                    if (!_assetsPreviewSprites[i].checkTileKind || _assetsPreviewSprites[i].TileKind == tile.Kind)
                    {
                        mainSprite = _assetsPreviewSprites[i].Sprite;
                        break;
                    }
                }
            }

            var assignedCount = 0;

            if ((tile.Mods & (long)TileState.IceCubeMod) != 0)
            {
                if (assignedCount == 0)
                {
                    overlaySprite = _icePreviewSprite;
                    overlayAlpha = _iceAlpha;
                    assignedCount++;
                }
                else if (assignedCount == 1)
                {
                    secondOverlaySprite = _icePreviewSprite;
                    secondOverlayAlpha = _iceAlpha;
                    assignedCount++;
                }
            }

            if ((tile.Mods & (long)TileState.VaseMod) != 0)
            {
                if (assignedCount == 0)
                {
                    overlaySprite = _vasePreviewSprite;
                    overlayAlpha = _vaseAlpha;
                    assignedCount++;
                }
                else if (assignedCount == 1)
                {
                    secondOverlaySprite = _vasePreviewSprite;
                    secondOverlayAlpha = _vaseAlpha;
                    assignedCount++;
                }
            }
        }
    }
}
#endif