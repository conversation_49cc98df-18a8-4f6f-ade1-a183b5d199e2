#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;
using BBB.Match3;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class WatermelonTileEditorTool : TileTool
    {
        public WatermelonTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
            MultiTileElement = new MultiTileTntElement(TntTargetType.Watermelon, M3EditorTile);
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            if (TryUpdateMultiTile(coords)) return;
            var cell = M3EditorTile.GetGridCell(coords);
            if (cell == null) return;
            var count = 1;
            if (!cell.Tile.IsNull())
            {
                count = cell.Tile.GetParam(TileParamEnum.AdjacentHp);
                count++;
                if (count is < 0 or > 3) count = 1;
            }

            cell.HardRemoveTile(0);
            var tileParams = new List<TileParam>(1)
            {
                new(TileParamEnum.AdjacentHp, count)
            };
            var newTile = TileFactory.CreateTile(grid.TilesSpawnedCount++, TileAsset.Watermelon,
                new TileOrigin(Creator.LevelEditor, cell));
            newTile.SetTileParams(tileParams);
            cell.AddTile(newTile);
            base.Apply(grid, coords, cardinalDirections, prm);
        }
    }
}
#endif
