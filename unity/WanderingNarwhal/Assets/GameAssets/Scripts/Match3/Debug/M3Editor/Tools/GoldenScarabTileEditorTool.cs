#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;
using BBB.Match3;
using UnityEngine;
using Grid = BBB.Grid;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class GoldenScarabTileEditorTool : TileTool
    {
        private const int Hp = 2;
        public GoldenScarabTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
            MultiTileElement = new MultiTileTntElement(TntTargetType.GoldenScarab, M3EditorTile);
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            if (TryUpdateMultiTile(coords)) return;

            var cell = M3EditorTile.GetGridCell(coords);
            if (cell == null) return;
            cell.HardRemoveTile(0);
            prm = Mathf.Clamp(prm, 0, 1);
            
            var tileParams = new List<TileParam>
            {
                new(TileParamEnum.AdjacentHp, Hp),
                new(TileParamEnum.GoldenScarabCount, prm)
            };
            var newTile = TileFactory.CreateTile(grid.TilesSpawnedCount++, TileAsset.GoldenScarab,
                new TileOrigin(Creator.LevelEditor, cell));
            newTile.SetTileParams(tileParams);
            cell.AddTile(newTile);
            base.Apply(grid, coords, cardinalDirections, prm);
        }
    }
}
#endif
