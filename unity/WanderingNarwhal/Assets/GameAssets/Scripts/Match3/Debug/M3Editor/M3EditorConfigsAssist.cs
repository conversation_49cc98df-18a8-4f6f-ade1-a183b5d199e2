using System;
using System.Globalization;
using System.Linq;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems;
using GameAssets.Scripts.Match3.Systems.AssistSystem;
using UnityEngine;
using UnityEngine.UI;
using AssistSystem = BBB.GameAssets.UI.Level.Scripts.AssistSystem.AssistSystem;

#if UNITY_EDITOR

namespace BBB.M3Editor
{
    public class M3EditorConfigsAssist : M3EditorConfigsItemBase, IAssistParamsProvider
    {
        //variable params

        private InputField _assistEfficiencyInputField;
        private Dropdown _assistStageDropDown;
        private Toggle _useAssistControllerToggle;

        private IEventDispatcher _eventDispatcher;
        private readonly AssistSystem _assistSystem;

        private float _assistValue;
        private AssistMode _assitMode;
        /// <summary>
        /// -1 is default Assist, which follows target win rate
        /// 0 is <PERSON><PERSON>, editor override
        /// 1 is <PERSON><PERSON>, editor override
        /// </summary>
        private int assistAim = -1;

        public M3EditorConfigsAssist(M3Editor m3Editor, M3EditorConfigs m3EditorConfigs) : base(m3Editor, m3EditorConfigs)
        {
            _eventDispatcher = M3Editor.Context.Resolve<IEventDispatcher>();

            _assistSystem = new AssistSystem();
            _assistSystem.InitializeByContext(M3Editor.Context);

            _eventDispatcher.RemoveListener<BalanceReportStarted>(OnBalanceReportStarted);
            _eventDispatcher.AddListener<BalanceReportStarted>(OnBalanceReportStarted);
        }

        private void OnBalanceReportStarted(BalanceReportStarted obj)
        {
            _useAssistControllerToggle.isOn = true;
        }

        public override void SetUpUi()
        {
            foreach (var childInputField in M3Editor.GetComponentsInChildren<InputField>(true))
            {
                if (childInputField.name == "AssistEfficiencyInputField")
                {
                    _assistEfficiencyInputField = childInputField;
                    _assistEfficiencyInputField.text = 0f.ToString();
                    _assistEfficiencyInputField.onValueChange.RemoveAllListeners();
                    _assistEfficiencyInputField.onValueChanged.AddListener(OnVariableValueChanged);
                }
            }

            foreach (var childDropDown in M3Editor.GetComponentsInChildren<Dropdown>(true))
            {
                if (childDropDown.name == "AssistStageDropdown")
                {
                    _assistStageDropDown = childDropDown;
                    var enumValues = Enum.GetNames(typeof(AssistMode));
                    _assistStageDropDown.options = enumValues.Select(str => new Dropdown.OptionData(str)).ToList();
                    _assistStageDropDown.onValueChanged.RemoveAllListeners();
                    _assistStageDropDown.onValueChanged.AddListener(OnAssistStageValueChanged);
                    _assistStageDropDown.value = 1;
                }
            }

            foreach (var childToggle in M3Editor.GetComponentsInChildren<Toggle>(false))
            {
                if (childToggle.name == "UseAssistControllerToggle")
                {
                    _useAssistControllerToggle = childToggle;
                    childToggle.onValueChanged.RemoveAllListeners();
                    childToggle.onValueChanged.AddListener(OnUseAssistControllerChanged);
                    childToggle.isOn = false;
                }
                
                if (childToggle.name == "TargetWinRateToggle")
                {
                    childToggle.onValueChanged.RemoveAllListeners();
                    childToggle.onValueChanged.AddListener(state =>
                    {
                        if (state)
                        {
                            assistAim = -1;
                        }
                    });
                    childToggle.isOn = true;
                }
                
                if (childToggle.name == "AimWinToggle")
                {
                    childToggle.onValueChanged.RemoveAllListeners();
                    childToggle.onValueChanged.AddListener(state =>
                    {
                        if (state)
                        {
                            assistAim = 1;
                        }
                    });
                    childToggle.isOn = false;
                }
                
                if (childToggle.name == "AimLoseToggle")
                {
                    childToggle.onValueChanged.RemoveAllListeners();
                    childToggle.onValueChanged.AddListener(state =>
                    {
                        if (state)
                        {
                            assistAim = 0;
                        }
                    });
                    childToggle.isOn = false;
                }
            }

            if (_assistEfficiencyInputField == null)
                Debug.LogError("Assist efficiency input field not found");

            if (_useAssistControllerToggle == null)
                Debug.LogError("Use assist controller toggle not found");
        }

        private void OnAssistStageValueChanged(int number)
        {
            var option = _assistStageDropDown.options[number];
            _assitMode = (AssistMode)Enum.Parse(typeof(AssistMode), option.text);
        }

        private void OnUseAssistControllerChanged(bool value)
        {
            _assistEfficiencyInputField.gameObject.SetActive(!value);
            _assistStageDropDown.gameObject.SetActive(!value);
        }

        private void OnVariableValueChanged(string value)
        {
            bool success = true;
            success &= float.TryParse(_assistEfficiencyInputField.text, NumberStyles.Any, CultureInfo.InvariantCulture, out var assistValue);

            if (success)
            {
                _assistValue = assistValue;
            }
        }

        public override void LoadConfigsFrom(ILevel level)
        {
            string overrideAssistUid = default;

            if (M3Editor.ReplayPopup.IsPlaying && !string.IsNullOrEmpty(M3Editor.ReplayPopup.AssistSystemUid))
            {
                overrideAssistUid = M3Editor.ReplayPopup.AssistSystemUid;
            }

            _assistSystem.RefreshForLevel(level, assistAim, overrideAssistUid);

            if (!_useAssistControllerToggle.isOn)
                _useAssistControllerToggle.isOn = true;
        }

        public AssistParams GetAssistParams(int remainingMoves, int totalMoves, AssistState progressAchieved, AssistState originalGoals)
        {
            _assistSystem.IsReplaySystem = M3Editor.ReplayPopup.IsPlaying;
            var assistParams = _assistSystem.GetAssistParams(remainingMoves, totalMoves, progressAchieved, originalGoals);

            if (!_useAssistControllerToggle.isOn && !_assistSystem.IsReplaySystem)
            {
                assistParams.ResetAssistValue(_assistValue);
            }

            return assistParams;
        }
    }
}

#endif