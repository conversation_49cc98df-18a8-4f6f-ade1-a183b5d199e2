#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.Linq;
using BBB.GameAssets.Scripts.Player;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;
using UnityEngine.UI;

// ReSharper disable once CheckNamespace
namespace BBB.M3Editor
{
    // ReSharper disable once UnusedMember.Global
    public sealed class M3EditorConfigs : M3EditorBase, IAssistParamsProvider
    {
        private Dictionary<string, Toggle> _usedKindToggles;

        private readonly Dictionary<Type, M3EditorConfigsItemBase> _uiItems;

        private readonly M3EditorLevelFileToConfigMap _fileToConfigMap;

        private bool _isAlreadyTryingToApply;
        private LevelRemoteData _remoteDataOverride;

        private SpawnerSettingsManager _spawnerSettingsManager;

        private T GetConfigItem<T>() where T : M3EditorConfigsItemBase
        {
            return _uiItems[typeof(T)] as T;
        }

        public M3EditorConfigs(M3Editor m3Editor) : base(m3Editor)
        {
            _fileToConfigMap = m3Editor.Context.Resolve<M3EditorLevelFileToConfigMap>();
            _spawnerSettingsManager = m3Editor.Context.Resolve<SpawnerSettingsManager>();
            _uiItems = new Dictionary<Type, M3EditorConfigsItemBase>
            {
                { typeof(M3EditorConfigsWeights), new M3EditorConfigsWeights(m3Editor, this) },
                { typeof(M3EditorConfigsAssist), new M3EditorConfigsAssist(m3Editor, this) },
                { typeof(M3EditorConfigsLitterSkin), new M3EditorConfigsLitterSkin(m3Editor, this) },
                { typeof(M3EditorConfigsStickerSkin), new M3EditorConfigsStickerSkin(m3Editor, this) },
                { typeof(M3EditorConfigsAnimalSkin), new M3EditorConfigsAnimalSkin(m3Editor, this) },
                { typeof(M3EditorConfigsOverride), new M3EditorConfigsOverride(m3Editor, this) }
            };
        }

        public override void SetUpUi()
        {
            foreach (var child in _m3Editor.GetComponentsInChildren<Button>(true))
            {
                if (child.name == "CopyConfigButton")
                    child.ReplaceOnClick(OnCopyConfigPressed);
            }

            foreach (var child in _m3Editor.GetComponentsInChildren<Transform>(true))
            {
                if (child.name == "UsedKindsGroup")
                {
                    _usedKindToggles = new Dictionary<string, Toggle>();
                    foreach (Transform childKind in child)
                    {
                        var toggle = childKind.GetComponent<Toggle>();
                        toggle.onValueChanged.RemoveAllListeners();
                        toggle.onValueChanged.AddListener(SetUsedKind);
                        _usedKindToggles.Add(childKind.name, toggle);
                    }
                }
            }

            foreach (var m3EditorConfigsItemBase in _uiItems.Values)
            {
                m3EditorConfigsItemBase.SetUpUi();
            }
        }

        private void OnCopyConfigPressed()
        {
        }

        public override void UpdateUi()
        {
            LoadConfigsFrom(GameController.Level);
        }

        public bool IsUsedKind(string kind)
        {
            return !_usedKindToggles.ContainsKey(kind) || _usedKindToggles[kind].isOn;
        }

        public void SetupRemoteDataOverride(GoalState goalState, ILevel level)
        {
            if (goalState == null)
            {
                _remoteDataOverride = null;
                return;
            }

            _remoteDataOverride = new LevelRemoteData(goalState, level.TurnsLimit, level.Stage, level.Config.Uid);
        }

        public void ApplyConfigs(int prm = 0)
        {
            if (_isAlreadyTryingToApply) return;
            var usedKinds = new List<TileKinds>();
            RefreshToggleColors();

            foreach (var kvp in _usedKindToggles)
            {
                if (!kvp.Value.isOn)
                {
                    continue;
                }
                
                var kind = (TileKinds)Enum.Parse(typeof(TileKinds), kvp.Key);
                usedKinds.Add(kind);
            }

            var level = GameController.Level;
            level.UsedKinds = usedKinds;
            level.Goals = new GoalState();
            var currentFile = M3SaveLoadUtility.CurrentLevelUid;
            var filePath = _fileToConfigMap.GetFilePathWithLevelUid(currentFile);
            var remoteData = _remoteDataOverride ?? _fileToConfigMap.GetRemoteDataForFile(filePath);
            level.SetupRemoteData(remoteData, _spawnerSettingsManager.SpawnerSettings);
            level.Goals.InitializeTargetGoalCountsIfNotManuallySpecified(Grid, level.Config.Uid, remoteData.OptionalGoals);
            _m3Editor.ApplyGrid(Grid);
        }

        private void SetUsedKind(bool val)
        {
            ApplyConfigs();
        }

        private void LoadConfigsFrom(ILevel level)
        {
            _isAlreadyTryingToApply = true;

            foreach (var toggle in _usedKindToggles.Values)
                toggle.isOn = false;

            if (level.UsedKinds.Count > 0)
            {
                foreach (var usedKind in level.UsedKinds)
                {
                    var key = usedKind.ToString();
                    if (_usedKindToggles.TryGetValue(key, out var toggle))
                    {
                        toggle.isOn = true;
                    }
                    else
                    {
                        Debug.LogError($"Toggle {key} is not found in dict");
                    }
                }
            }

            RefreshToggleColors();

            foreach (var m3EditorConfigsItemBase in _uiItems.Values)
            {
                m3EditorConfigsItemBase.LoadConfigsFrom(level);
            }

            _isAlreadyTryingToApply = false;
        }

        private void RefreshToggleColors()
        {
            foreach (var usedKind in _usedKindToggles)
            {
                var cb = usedKind.Value.colors;
                cb.normalColor = usedKind.Value.isOn ? Color.black : Color.white;
                cb.highlightedColor = usedKind.Value.isOn ? Color.black : Color.white;
                usedKind.Value.colors = cb;
            }
        }

        public AssistParams GetAssistParams(int remainingMoves, int totalMoves, AssistState progressAchieved, AssistState originalGoals)
        {
            var provider = (IAssistParamsProvider)_uiItems[typeof(M3EditorConfigsAssist)];
            return provider.GetAssistParams(remainingMoves, totalMoves, progressAchieved, originalGoals);
        }

        public void RefreshForLevel(ILevel level)
        {
            var configsAssist = (M3EditorConfigsAssist)_uiItems[typeof(M3EditorConfigsAssist)];
            configsAssist.LoadConfigsFrom(level);
        }
    }
}
#endif