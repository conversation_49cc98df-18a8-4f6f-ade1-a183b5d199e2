#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;
using BBB.Match3;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class StickerTileEditorTool : TileTool
    {
        public StickerTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
            MultiTileElement = new MultiTileTntElement(TntTargetType.Sticker, M3EditorTile);
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            if (TryUpdateMultiTile(coords)) return;

            var cell = M3EditorTile.GetGridCell(coords);
            if (cell == null) return;

            var targetHp = 1;
            if (cell.Tile != null)
            {
                var hp = cell.Tile.GetParam(TileParamEnum.AdjacentHp);
                targetHp = ++hp;
                cell.HardRemoveTile(0);
            }

            targetHp = targetHp < 1 ? 1 : targetHp > 3 ? 1 : targetHp;
            var tileParams = new List<TileParam>(1)
            {
                new(TileParamEnum.AdjacentHp, targetHp)
            };
            var newTile = TileFactory.CreateTile(grid.TilesSpawnedCount++, TileAsset.Sticker, new TileOrigin(Creator.LevelEditor, cell));
            newTile.SetTileParams(tileParams);
            cell.AddTile(newTile);
            base.Apply(grid, coords, cardinalDirections, prm);
        }
    }
}
#endif
