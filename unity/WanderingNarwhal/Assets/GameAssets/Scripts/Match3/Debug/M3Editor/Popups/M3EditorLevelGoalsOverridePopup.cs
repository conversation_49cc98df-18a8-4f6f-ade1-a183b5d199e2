#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;
using UnityEngine.UI;
using System.Linq;

namespace BBB.M3Editor
{
    public class M3EditorLevelGoalsOverridePopup : BbbMonoBehaviour
    {
        [SerializeField] private Button _closeButton;
        [SerializeField] private GameObject _subItemPrefab;
        [SerializeField] private Transform _holder;

        private readonly Dictionary<string, int> _goalStringDictionary = new();

        [NonSerialized]
        private readonly Dictionary<GoalType, M3EditorGoalsOverrideSubItem> _goalSubItems = new();

        [NonSerialized]
        private readonly List<GoalType> _excludedGoalTypes = new()
        {
            GoalType.None,
            GoalType.Score
        };
        
        [NonSerialized]
        private readonly List<GoalType> _nonGridBasedGoalTypes = new()
        {
            GoalType.Toad,
            GoalType.MagicHat,
            GoalType.Bee
        };

        private Action<string> _goalsUpdate;

        public void Setup(Action<string> goalsUpdate)
        {
            _closeButton.ReplaceOnClick(Hide);
            
            foreach (var goalType in Enum.GetValues(typeof(GoalType)).Cast<GoalType>().OrderBy(s => s.ToNameString()))
            {
                if(_excludedGoalTypes.Contains(goalType)) continue;
                {
                    var subItem =  Instantiate(_subItemPrefab, _holder, false);
                    _goalSubItems.Add(goalType, subItem.GetComponent<M3EditorGoalsOverrideSubItem>());
                }
            }

            _goalsUpdate = goalsUpdate;
            
            Reset();
        }

        public void Show()
        {
            gameObject.SetActive(true);
        }

        public void Refresh(Dictionary<GoalType, int> goalState)
        {
            foreach (var (goalType, subItem) in _goalSubItems)
            {
                bool toggleState;
                int currentValue;
                
                if (goalState.TryGetValue(goalType, out var value))
                {
                    toggleState = true;
                    currentValue = value;
                }
                else
                {
                    toggleState = subItem.CachedToggleValue;
                    currentValue = toggleState? 1 : 0;
                }
                
                var gridBased = !_nonGridBasedGoalTypes.Contains(goalType);
                subItem.Setup(goalType, currentValue, toggleState, gridBased, GoalsUpdate);
            }
        }

        private void GoalsUpdate()
        {
            _goalStringDictionary.Clear();

            foreach (var keyPairValues in _goalSubItems)
            {
                if (keyPairValues.Value.ToggleState && keyPairValues.Value.CurrentValue >= 0)
                {
                    _goalStringDictionary.Add(keyPairValues.Key.ToString(), keyPairValues.Value.CurrentValue);
                }
            }

            _goalsUpdate?.Invoke(Newtonsoft.Json.JsonConvert.SerializeObject(_goalStringDictionary));
        }

        public void Reset()
        {
            foreach (var (key, subItem) in _goalSubItems)
            {
                subItem.Setup(key, 0, false, false, null);
            }
        }

        private void Hide()
        {
            gameObject.SetActive(false);
        }
    }
}
#endif
