#if UNITY_EDITOR
using BBB.Match3.Systems;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    public class M3EditorSpawnerTileImage : BbbMonoBehaviour
    {
        public Image MainImage;
        public Image Overlay0;
        public Image Overlay1;

        public void SetupImage(TileSpawnSettings tile, ISpawnerTileImageProvider imageProvider, float fade = 1f)
        {
            imageProvider.GetTileSprite(tile, out var mainSprite, out var overlay0Sprite, out var alpha0, out var overlay1Sprite, out var alpha1);
            MainImage.sprite = mainSprite;
            ApplyColorFade(MainImage, fade);

            // Tile preview can have main tile sprite and optional overlays sprites (ice).
            Overlay0.sprite = overlay0Sprite;
            Overlay0.preserveAspect = true;
            if (overlay0Sprite != null)
            {
                Overlay0.gameObject.SetActive(true);
                ApplyColorFade(Overlay0, fade, alpha0);
            }
            else
            {
                Overlay0.gameObject.SetActive(false);
            }

            Overlay1.sprite = overlay1Sprite;
            Overlay1.preserveAspect = true;
            if (overlay1Sprite != null)
            {
                Overlay1.gameObject.SetActive(true);
                ApplyColorFade(Overlay1, fade, alpha1);
            }
            else
            {
                Overlay1.gameObject.SetActive(false);
            }
        }

        private static void ApplyColorFade(Image image, float fade, float alpha = 1f)
        {
            image.color = new Color(fade, fade, fade, alpha);
        }
    }
}
#endif