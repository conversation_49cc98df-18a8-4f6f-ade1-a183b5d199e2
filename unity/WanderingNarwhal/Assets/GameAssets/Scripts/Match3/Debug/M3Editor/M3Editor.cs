#if UNITY_EDITOR
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using BBB.CellTypes;
using BBB.Core;
using BBB.DI;
using BBB.Match3.Renderer;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.UI;
using BBB.UI.Level;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Match3.Debug.M3Editor.Tools;
using TMPro;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;

// ReSharper disable once CheckNamespace
namespace BBB.M3Editor
{
    // ReSharper disable once UnusedMember.Global
    public sealed class M3Editor : BbbMonoBehaviour, IContextInitializable, IContextReleasable, IAssistParamsProvider
    {
        private Button _currentToolButton;

        private object _currentToolInstance;
        private string _currentToolName;
        private int _currentToolParam;

        private Text _currentToolText;
        private bool _isInited;

        private Coords _lastInputCoords;
        private EditorLevelControllerLauncher _launcher;

        private M3EditorLevelItemController _levelItemController;
        private M3EditorLevelBoostersController _m3EditorLevelBoostersController;
        [SerializeField] private TextMeshProUGUI _levelTitle;
        [SerializeField] private int _levelTileOffset = 50;
        [SerializeField] private Transform _m3CellToolsParent;
        [SerializeField] private Transform _m3TileToolsParent;

        private readonly List<M3EditorAddTileButtonActiveState> _refreshableButtons = new(7);

        [SerializeField] private M3EditorSpawnersPanel _spawnersPanel;

        private readonly Dictionary<string, string> _toolAndOpposite = new();

        public GameController GameController { get; private set; }
        
        public Match3SimulationPlayer Match3SimulationPlayer { get; private set; }

        public TileTickPlayer TileTickPlayer { get; private set; }

        public Grid Grid => GameController.Grid;
        public M3EditorHistogramPopup HistPopup { get; private set; }
        public M3EditorExceptionPopup ExcepPopup { get; private set; }
        public M3EditorIntegrationTestsPopup IntegrationTestsPopup { get; private set; }
        public M3EditorReplayPopup ReplayPopup { get; private set; }
        public M3EditorLevelGoalsOverridePopup GoalsOverridePopup { get; private set; }

        public DebugLevelController LevelController { get; private set; }

        public M3EditorDebugView DebugView { get; private set; }

        public bool PlayState { get; set; }
        public bool IsInSpawnerMode { get; set; }
        public IContext Context { get; private set; }

        public Grid BackedUpGrid { get; private set; }

        internal Dictionary<Type, IM3EditorSystem> _m3Systems { get; set; }
        public  ILevelAnalyticsReporter LevelAnalyticsReporter { get; private set; }


        public void InitializeByContext(IContext context)
        {
            _launcher = context.Resolve<EditorLevelControllerLauncher>();
            LevelController = FindObjectOfType<DebugLevelController>();
            GameController = context.Resolve<GameController>();
            Match3SimulationPlayer = context.Resolve<Match3SimulationPlayer>();
            
            TileTickPlayer = context.Resolve<TileTickPlayer>();
            _levelItemController = context.Resolve<M3EditorLevelItemController>();
            _m3EditorLevelBoostersController = context.Resolve<M3EditorLevelBoostersController>();
            LevelAnalyticsReporter = context.Resolve<ILevelAnalyticsReporter>();
            var gridController = context.Resolve<IGridController>() as GridController;

            var resourceProvider = context.Resolve<Match3EditorResourceProvider>();
            var debugViewPrefab = resourceProvider.GetEditorPrefab(EditorPrefabNames.M3EditorDebugView);
            var go = Instantiate(debugViewPrefab, gridController.transform, false);
            DebugView = go.GetComponent<M3EditorDebugView>();
            DebugView.InitializeByContext(context);

            HistPopup = context.Resolve<M3EditorHistogramPopup>();
            ExcepPopup = context.Resolve<M3EditorExceptionPopup>();
            IntegrationTestsPopup = context.Resolve<M3EditorIntegrationTestsPopup>();
            ReplayPopup = context.Resolve<M3EditorReplayPopup>();
            GoalsOverridePopup = context.Resolve<M3EditorLevelGoalsOverridePopup>();
            Context = context;

            InitEditor(context);
        }

        public void ReleaseByContext(IContext context)
        {
            Context = null;
        }


        private void InitEditor(IContext context)
        {
            if (_isInited) return;

            _m3Systems = new Dictionary<Type, IM3EditorSystem>
            {
                {typeof(M3EditorCell), new M3EditorCell(this)},
                {typeof(M3EditorConfigs), new M3EditorConfigs(this)},
                {typeof(M3EditorGridShape), new M3EditorGridShape(this)},
                {typeof(M3EditorSaveLoad), new M3EditorSaveLoad(this)},
                {typeof(M3EditorTest), new M3EditorTest(this)},
                {typeof(M3EditorPlayTools), new M3EditorPlayTools(this)},
                {typeof(M3EditorTile), new M3EditorTile(this)},
                {typeof(M3EditorSpawnersPanel), _spawnersPanel},
                {typeof(M3EditorLevelItemController), _levelItemController},
                {typeof(M3EditorLevelBoostersController), _m3EditorLevelBoostersController},
                
            };

            var tileInterfaceType = typeof(ITileTool);
            var tileImplementations = Assembly.GetExecutingAssembly().GetTypes()
                .Where(t => tileInterfaceType.IsAssignableFrom(t));

            _refreshableButtons.Clear();

            foreach (var type in tileImplementations)
            {
                if (!type.IsClass || type.IsAbstract)
                    continue;
                var registerMethod = type.GetMethod("RegisterToEditor");
                var toolInstance = Activator.CreateInstance(type, _m3Systems);

                registerMethod?.Invoke(toolInstance, new object[] {_m3TileToolsParent});

                var actionButton = type.GetMethod("GetActionButton");

                if (actionButton?.Invoke(toolInstance, null) is M3EditorUIActionButton overrideAction)
                    if (overrideAction != null)
                        overrideAction.GetButton().onClick.AddListener(() =>
                            SelectTool(toolInstance, type.Name, overrideAction.ParameterNumber));

                var activeStateMethod = type.GetMethod("GetActiveStateButton");

                if (activeStateMethod?.Invoke(toolInstance, null) is not M3EditorAddTileButtonActiveState
                    activeStateButton) continue;
                if (activeStateButton != null)
                    _refreshableButtons.Add(activeStateButton);
            }

            var cellInterfaceType = typeof(ICellTool);
            var cellImplementations = Assembly.GetExecutingAssembly().GetTypes()
                .Where(t => cellInterfaceType.IsAssignableFrom(t));

            foreach (var type in cellImplementations)
            {
                if (!type.IsClass || type.IsAbstract)
                    continue;
                var registerMethod = type.GetMethod("RegisterToEditor");
                var toolInstance = Activator.CreateInstance(type, _m3Systems);
                registerMethod?.Invoke(toolInstance, new object[] {_m3CellToolsParent});

                var actionButton = type.GetMethod("GetActionButton");

                if (actionButton?.Invoke(toolInstance, null) is not M3EditorUIActionButton overrideAction) continue;
                if (overrideAction != null)
                    overrideAction.GetButton().onClick.AddListener(() =>
                        SelectTool(toolInstance, type.Name, overrideAction.ParameterNumber));
            }

            var levelTitle = M3SaveLoadUtility.GetShortPath(M3SaveLoadUtility.LastEditedLevelPath);
            _levelTitle.text = levelTitle;
            var levelTitleTransform = _levelTitle.gameObject.transform;
            levelTitleTransform.localPosition = new Vector3(levelTitleTransform.localPosition.x, 
                levelTitleTransform.localPosition.y + _levelTileOffset, levelTitleTransform.localPosition.z);


            foreach (var m3System in _m3Systems)
            {
                var contextInitializable = m3System.Value as IContextInitializable;
                contextInitializable?.InitializeByContext(context);
                m3System.Value.SetUpUi();
            }

            BindCurrentTools();

            _isInited = true;

            UpdateUis();
        }

        private Cell InternalRemoveSpawner()
        {
            if (!GetCoordsAndDirection(out var coords, out _)) return null;
            if (_lastInputCoords == coords) return null;
            _lastInputCoords = coords;
            if (!Grid.TryGetCell(coords, out var cell)) return null;
            if (!cell.IsAnyOf(CellState.Spawner)) return null;
            cell.HardRemoveTile(0);
            return cell;
        }

        private void PlaceSpawner()
        {
            var cell = InternalRemoveSpawner();
            var newTile =
                TileFactory.CreateTile(Grid.TilesSpawnedCount++, TileAsset.DropItem, new TileOrigin(Creator.LevelEditor, cell));
            cell.AddTile(newTile);
            ApplyGrid(Grid);
        }

        private void RemoveSpawner()
        {
            InternalRemoveSpawner();
            ApplyGrid(Grid);
        }

        private void InternalProcessTile(bool remove)
        {
            if (!GetCoordsAndDirection(out var coords, out var direction)) return;
            if(_lastInputCoords == coords) return;
            _lastInputCoords = coords;
            RunTool(coords, direction, remove);
        }

        private void PlaceTile()
        {
            InternalProcessTile(false);
        }

        private void RemoveTile()
        {
            InternalProcessTile(true);
        }


        public void Update()
        {
            if (IsInSpawnerMode)
            {
                if (Input.GetMouseButton(0))
                {
                    if (Input.GetKey(KeyCode.LeftControl))
                        RemoveSpawner();
                    else
                        PlaceSpawner();
                }
            }
            else
            {
                if (Input.GetMouseButton(0))
                {
                    PlaceTile();
                }
                else if (Input.GetMouseButton(1))
                {
                    RemoveTile();
                }
            }

            if (Input.GetMouseButtonUp(0)) _lastInputCoords = Coords.OutOfGrid;
        }

        public void HardRestartWith(string name, Grid grid)
        {
            Context.Releaser.ReleaseContext();
            var level = GameController.Level;
            level.Grid = grid;
            _launcher.HardRestart(level);
            UpdateUis();
            _levelTitle.text = name;
        }

        public void ApplyGrid(Grid grid)
        {
            grid.RefrehsAllCellsMultisizeCaches();
            LevelController.RefreshForGrid(grid).Forget();
            GameController.Refresh();
            DebugView.Refresh(grid);
            RefreshButtonStates();
        }

        private void UpdateUis()
        {
            foreach (var m3System in _m3Systems) m3System.Value.UpdateUi();

            RefreshButtonStates();
        }

        private void RefreshButtonStates()
        {
            foreach (var b in _refreshableButtons) b.RefreshState(GameController.Level);
        }

        public static void LockUis(Selectable exeptSelectable)
        {
            var go = GameObject.Find("M3Editor");
            var selectables = new HashSet<Selectable>();
            selectables.UnionWith(go.GetComponentsInChildren<Button>());
            selectables.UnionWith(go.GetComponentsInChildren<Dropdown>());
            selectables.UnionWith(go.GetComponentsInChildren<InputField>());

            foreach (var selectable in selectables)
            {
                if (selectable == exeptSelectable) continue;
                selectable.interactable = false;
            }
        }

        public static void UnLockUis()
        {
            var go = GameObject.Find("M3Editor");
            var selectables = new HashSet<Selectable>();
            selectables.UnionWith(go.GetComponentsInChildren<Button>());
            selectables.UnionWith(go.GetComponentsInChildren<Dropdown>());
            selectables.UnionWith(go.GetComponentsInChildren<InputField>());

            foreach (var selectable in selectables) selectable.interactable = true;
        }

        public void ClearTool()
        {
            SelectTool(null, null, 0);
        }

        public void BackUpGrid()
        {
            BackedUpGrid = GameController.Grid.Clone();
        }

        public void RestoreGridFromBackUp()
        {
            ApplyGrid(BackedUpGrid);
        }

        public T GetSystem<T>() where T : M3EditorBase
        {
            return (T) _m3Systems[typeof(T)];
        }

        #region Init

        private void BindCurrentTools()
        {
            // Right panel:
            var rightPanel = transform.Find("RightPanel");

            _currentToolButton = rightPanel.Find("CurrentTool").GetComponentInChildren<Button>(true);
            _currentToolButton.onClick.AddListener(() => SelectTool(null, null, 0));
            _currentToolText = _currentToolButton.GetComponentInChildren<Text>(true);
        }

        #endregion

        public void SelectTool(object toolInstance, string toolName, int param)
        {
            if (string.IsNullOrEmpty(toolName))
            {
                _currentToolInstance = toolInstance;
                _currentToolName = null;
                _currentToolParam = 0;
                _currentToolText.transform.parent.gameObject.SetActive(false);
            }
            else
            {
                if (ValidateTool(toolName))
                {
                    _currentToolInstance = toolInstance;
                    _currentToolName = toolName;
                    _currentToolText.text = toolName;
                    _currentToolText.transform.parent.gameObject.SetActive(true);
                    _currentToolParam = param;

                    // In order to prevent adding Ivy in invisible state
                    // we should have "ArmoredTiles" debug toggle being turned on
                    if (_currentToolName == "AddIvy") _levelItemController.SetArmoredTilesToggleOn();
                }
                else
                {
                    Debug.LogErrorFormat("Tool {0} can not be used now, check the config", toolName);
                }
            }
        }

        private bool ValidateTool(string toolName)
        {
            if (toolName.StartsWith("Add") && toolName.EndsWith("Tile"))
            {
                var tileName = toolName.Replace("Add", string.Empty).Replace("Tile", string.Empty);
                var configSystem = GetSystem<M3EditorConfigs>();
                return configSystem.IsUsedKind(tileName);
            }

            return true;
        }

        private void RunTool(Coords coords, CardinalDirections directions, bool runOpposite = false)
        {
            if (string.IsNullOrEmpty(_currentToolName)) return;
            object[] parameters = {Grid, coords, directions, _currentToolParam};
            var type = _currentToolInstance.GetType();

            if (!runOpposite)
            {
                var applyMethod = type.GetMethod("Apply");
                applyMethod?.Invoke(_currentToolInstance, parameters);
            }
            else
            {
                var unApplyMethod = type.GetMethod("UnApply");
                unApplyMethod?.Invoke(_currentToolInstance, parameters);
            }
        }

        private bool GetCoordsAndDirection(out Coords coords, out CardinalDirections direction)
        {
            coords = default;
            direction = default;

            var gridGo = Context.Resolve<IGridController>();
            var gridGoRect = gridGo.Transform;
            var corners = new Vector3[4];
            gridGoRect.GetWorldCorners(corners);
            var delta = corners[2] - corners[0];
            var width = delta.x;
            var height = delta.y;

            var gridWidth = GameController.Grid.Width;
            var gridHeight = GameController.Grid.Height;

            var camera = Context.Resolve<Camera>(Match3Constants.LevelCameraTag);

            var mouseWorldPos = camera.ScreenToWorldPoint(Input.mousePosition);
            var cursorPosition = mouseWorldPos - corners[0];
            var cursorX = cursorPosition.x;
            var cursorY = cursorPosition.y;

            var gridWorldRect = new Rect(corners[0], corners[2] - corners[0]);
            if (!gridWorldRect.Contains(mouseWorldPos)) return false;

            var xOne = width / gridWidth;
            var yOne = height / gridHeight;

            var coordXWithDecimal = cursorX / xOne;
            var coordYWithDecimal = cursorY / yOne;

            var xDelta = coordXWithDecimal - Math.Floor(coordXWithDecimal);
            var yDelta = coordYWithDecimal - Math.Floor(coordYWithDecimal);
            if (xDelta > yDelta)
                direction = xDelta > 1 - yDelta ? CardinalDirections.E : CardinalDirections.S;
            else
                direction = xDelta > 1 - yDelta ? CardinalDirections.N : CardinalDirections.W;

            coords = new Coords(Mathf.FloorToInt(coordXWithDecimal), Mathf.FloorToInt(coordYWithDecimal));

            return true;
        }

        public GameObject InstantiateGo(GameObject go)
        {
            return Instantiate(go);
        }

        public void DestroyGo(GameObject go)
        {
            Destroy(go);
        }

        public void StartCoroutineMethod(IEnumerator coroutineMethod)
        {
            StartCoroutine(coroutineMethod);
        }

        public void OnGameRun(bool playState, bool instantReveal = false)
        {
            if (!playState)
            {
                DebugView.HideText();
            }
            else
            {
                var configs = (M3EditorConfigs) _m3Systems[typeof(M3EditorConfigs)];
                configs.RefreshForLevel(GameController.Level);
                GameController.Refresh(instantReveal);
            }


            foreach (var system in _m3Systems.Values) system.OnGameRun(playState);

            IntegrationTestsPopup?.ChangePlayState(playState);
        }

        public AssistParams GetAssistParams(int remainingMoves, int totalMoves, AssistState progressAchieved,
            AssistState originalGoals)
        {
            var provider = (IAssistParamsProvider) _m3Systems[typeof(M3EditorConfigs)];
            return provider.GetAssistParams(remainingMoves, totalMoves, progressAchieved, originalGoals);
        }

        public void OnShow()
        {
            DebugView.Refresh(GameController.Grid);
        }

        public static bool IsCurrentSceneLevelEditorScene()
        {
            return SceneManager.GetActiveScene().name == "Match3LevelEditor";
        }
    }
}
#endif