#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;
using BBB.Match3;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class FrameTileEditorTool : TileTool
    {
        public FrameTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            if (TryUpdateMultiTile(coords)) return;

            var cell = M3EditorTile.GetGridCell(coords);
            if (cell == null) return;

            if (cell.Tile is {Speciality: TileSpeciality.Frame})
            {
                cell.Tile.SetParam(TileParamEnum.AdjacentHp, cell.Tile.GetParam(TileParamEnum.AdjacentHp) == 1 ? 2 : 3);
            }
            else
            {
                cell.HardRemoveTile(0);
                
                var tileParams = new List<TileParam>(1)
                {
                    new(TileParamEnum.AdjacentHp, 1)
                };

                var newTile = TileFactory.CreateTile(grid.TilesSpawnedCount++, TileAsset.Frame,
                    new TileOrigin(Creator.LevelEditor, cell));
                newTile.SetTileParams(tileParams);
                cell.AddTile(newTile);
            }

            base.Apply(grid, coords, cardinalDirections, prm);
        }

        public override void UnApply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            var cell = M3EditorTile.GetGridCell(coords, false);
            if (cell == null) return;

            if (cell.Tile is {Speciality: TileSpeciality.Frame})
                cell.Tile.SetParam(TileParamEnum.AdjacentHp, cell.Tile.GetParam(TileParamEnum.AdjacentHp) == 3 ? 2 : 1);

            M3EditorTile.RefreshGoals();
            M3EditorTile.RefreshGrid();
        }
    }
}
#endif
