using System;
using BebopBee.Core;
using BebopBee.UnityEngineExtensions;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;
using UnityEngine.UI;

#if UNITY_EDITOR

namespace BBB.M3Editor
{
    public class M3EditorConfigsOverride : M3EditorConfigsItemBase
    {
        private ILevel _level;
        private string _levelUid;
        private int _defaultTurnsLimit = -1;
        private GoalState _defaultGoalState;

        private Toggle _overrideMovesToggle;
        private Toggle _overrideGoalsToggle;

        private InputField _movesInputField;
        private InputField _goalsInputField;
        private Button _goalsCopyButton;
        private Button _goalsPopupButton;

        private const string OVERRIDE_GOAL_PREFIX_KEY = "goal_override_level_";
        private const string OVERRIDE_MOVE_PREFIX_KEY = "move_override_level_";

        private bool _isLoadingNewLevel;

        public M3EditorConfigsOverride(M3Editor m3Editor, M3EditorConfigs m3EditorConfigs)
            : base(m3Editor, m3EditorConfigs)
        {
        }

        public override void SetUpUi()
        {
            foreach (Transform child in M3Editor.transform.GetChildrenRecursevely())
            {
                if (child.gameObject.name == "OverridePanel")
                {
                    _overrideMovesToggle = child.Find("OverrideMovesToggle").GetComponent<Toggle>();
                    _overrideGoalsToggle = child.Find("OverrideGoalsToggle").GetComponent<Toggle>();
                    _movesInputField = child.Find("MovesInputField").GetComponent<InputField>();
                    _goalsInputField = child.Find("GoalsInputField").GetComponent<InputField>();
                    _goalsCopyButton = child.Find("CopyToClipboardButton").GetComponent<Button>();
                    _goalsPopupButton = child.Find("OpenGoalsPopup").GetComponent<Button>();
                    break;
                }
            }
            
            _goalsInputField.interactable = false;

            _overrideMovesToggle.onValueChanged.RemoveAllListeners();
            _overrideMovesToggle.onValueChanged.AddListener(OnMovesToggleChanged);

            _movesInputField.onEndEdit.RemoveAllListeners();
            _movesInputField.onEndEdit.AddListener(OnMovesInputValueChanged);

            _overrideGoalsToggle.onValueChanged.RemoveAllListeners();
            _overrideGoalsToggle.onValueChanged.AddListener(OnGoalsToggleChanged);

            _goalsInputField.onEndEdit.RemoveAllListeners();
            _goalsInputField.onEndEdit.AddListener(OnGoalsInputValueChanged);

            _goalsCopyButton.ReplaceOnClick(OnCopyButton);
            _goalsPopupButton.ReplaceOnClick(OpenGoalsOverridePopup);
            
            M3Editor.GoalsOverridePopup.Setup(UpdateGoalsFromPopup);
        }

        private void OnCopyButton()
        {
            var goalsString = _goalsInputField.text;
            var te = new TextEditor();
            te.text = goalsString;
            te.SelectAll();
            te.Copy();
        }

        private void OpenGoalsOverridePopup()
        {
            M3Editor.GoalsOverridePopup.Show();
        }

        public override void LoadConfigsFrom(ILevel level)
        {
            Rx.Invoke(0.01f, LoadConfigsAction);

            void LoadConfigsAction(long _)
            {
                _isLoadingNewLevel = true;

                _level = level;
                _levelUid = level.Config.Uid;
                _defaultTurnsLimit = -1;
                _defaultGoalState = null;
                M3EditorConfigs.SetupRemoteDataOverride(null, _level);
                LoadSavedOverrideMovesState();
                LoadSavedOverrideGoalsState();

                _isLoadingNewLevel = false;
            }
        }

        private void LoadSavedOverrideMovesState()
        {
            if (!_levelUid.IsNullOrEmpty() && PlayerPrefs.HasKey(OVERRIDE_MOVE_PREFIX_KEY + _levelUid))
            {
                SetToggleAndForceUpdate(_overrideMovesToggle, true, OnMovesToggleChanged);
            }
            else
            {
                SetToggleAndForceUpdate(_overrideMovesToggle, false, OnMovesToggleChanged);
            }
        }

        private void LoadSavedOverrideGoalsState()
        {
            if (!_levelUid.IsNullOrEmpty() && PlayerPrefs.HasKey(OVERRIDE_GOAL_PREFIX_KEY + _levelUid))
            {
                SetToggleAndForceUpdate(_overrideGoalsToggle, true, OnGoalsToggleChanged);
            }
            else
            {
                SetToggleAndForceUpdate(_overrideGoalsToggle, false, OnGoalsToggleChanged);
            }
        }

        private void OnMovesToggleChanged(bool val)
        {
            _movesInputField.gameObject.SetActive(val);

            if (_isLoadingNewLevel)
            {
                _defaultTurnsLimit = _level.TurnsLimit;
            }

            if (val)
            {
                int moves;
                if (PlayerPrefs.HasKey(OVERRIDE_MOVE_PREFIX_KEY + _levelUid))
                {
                    moves = PlayerPrefs.GetInt(OVERRIDE_MOVE_PREFIX_KEY + _levelUid);
                }
                else
                {
                    moves = _defaultTurnsLimit;
                }

                SetUITextAndForceUpdate(_movesInputField, moves.ToString(), OnMovesInputValueChanged);
            }
            else
            {
                _level.TurnsLimit = _defaultTurnsLimit;
                M3Editor.ApplyGrid(_level.Grid);

                if (!_isLoadingNewLevel)
                {
                    PlayerPrefs.DeleteKey(OVERRIDE_MOVE_PREFIX_KEY + _levelUid);
                }
            }
        }

        private void OnGoalsToggleChanged(bool val)
        {
            _goalsInputField.gameObject.SetActive(val);
            _goalsCopyButton.gameObject.SetActive(val);
            _goalsPopupButton.gameObject.SetActive(val);

            if (_isLoadingNewLevel)
            {
                _defaultGoalState = _level.Goals.Clone();
            }

            if (val)
            {
                string goalStr;
                if (PlayerPrefs.HasKey(OVERRIDE_GOAL_PREFIX_KEY + _levelUid))
                {
                    goalStr = PlayerPrefs.GetString(OVERRIDE_GOAL_PREFIX_KEY + _levelUid);
                }
                else
                {
                    goalStr = GoalState.Serlializer.ToJson(_defaultGoalState);
                }

                SetUITextAndForceUpdate(_goalsInputField, goalStr, OnGoalsInputValueChanged);
            }
            else
            {
                if (_defaultGoalState != null)
                {
                    _level.Goals = _defaultGoalState.Clone();
                    M3Editor.ApplyGrid(_level.Grid);
                }

                if (!_isLoadingNewLevel)
                {
                    PlayerPrefs.DeleteKey(OVERRIDE_GOAL_PREFIX_KEY + _levelUid);
                }
            }
        }

        private void OnGoalsInputValueChanged(string val)
        {
            if (val.IsNullOrEmpty())
            {
                _level.Goals = _defaultGoalState.Clone();
                M3Editor.GoalsOverridePopup.Refresh(_level.Goals.ToDictionary());
                PlayerPrefs.DeleteKey(OVERRIDE_GOAL_PREFIX_KEY + _levelUid);
                return;
            }

            if (JsonValidation.IsValidJson(val))
            {
                var goals = GoalState.Serlializer.FromJson(val);
                M3EditorConfigs.SetupRemoteDataOverride(goals, _level);
                M3EditorConfigs.ApplyConfigs();
                if (!_isLoadingNewLevel)
                {
                    PlayerPrefs.SetString(OVERRIDE_GOAL_PREFIX_KEY + _levelUid, val);
                }
                M3Editor.GoalsOverridePopup.Refresh(_level.Goals.ToDictionary());
            }
            else
            {
                _goalsInputField.text = null;
                M3Editor.GoalsOverridePopup.Reset();
            }
        }

        private void UpdateGoalsFromPopup(string str)
        {
            _goalsInputField.text = str;
            OnGoalsInputValueChanged(_goalsInputField.text);
        }

        private void OnMovesInputValueChanged(string val)
        {
            int.TryParse(val, out var moves);

            if (moves <= 0)
            {
                _level.TurnsLimit = _defaultTurnsLimit;
                M3Editor.ApplyGrid(_level.Grid);
                PlayerPrefs.DeleteKey(OVERRIDE_MOVE_PREFIX_KEY + _levelUid);
                return;
            }

            _level.TurnsLimit = moves;
            M3Editor.ApplyGrid(_level.Grid);
            if (!_isLoadingNewLevel)
            {
                PlayerPrefs.SetInt(OVERRIDE_MOVE_PREFIX_KEY + _levelUid, moves);
            }
        }



        private static void SetToggleAndForceUpdate(Toggle toggle, bool isOn, Action<bool> onChangeAction)
        {
            if (toggle.isOn == isOn)
            {
                onChangeAction(isOn);
            }
            else
            {
                toggle.isOn = isOn;
            }
        }

        private static void SetUITextAndForceUpdate(InputField input, string str, Action<string> onChangeAction)
        {
            input.text = str;
            onChangeAction(str);
        }
    }
}

#endif