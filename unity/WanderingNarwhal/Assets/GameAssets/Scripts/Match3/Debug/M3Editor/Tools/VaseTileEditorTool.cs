#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class VaseTileEditorTool : TileTool
    {
        public VaseTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            if (TryUpdateMultiTile(coords)) return;

            var cell = M3EditorTile.GetGridCell(coords);
            if (ReferenceEquals(cell?.Tile, null)) return;

            var vaseCount = cell.Tile.GetParam(TileParamEnum.VaseLayerCount);
            vaseCount++;
            if (vaseCount >= 3)
                vaseCount = 3;
            cell.Tile.SetParam(TileParamEnum.VaseLayerCount, vaseCount);
            cell.Tile.Add(TileState.VaseMod);

            base.Apply(grid, coords, cardinalDirections, prm);
        }

        public override void UnApply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            var cell = M3EditorTile.GetGridCell(coords);
            if (ReferenceEquals(cell?.Tile, null)) return;

            if (cell.Tile.IsAnyOf(TileState.VaseMod))
            {
                var iceCount = cell.Tile.GetParam(TileParamEnum.VaseLayerCount);

                iceCount--;

                if (iceCount <= 0)
                    iceCount = 0;

                cell.Tile.SetParam(TileParamEnum.VaseLayerCount, iceCount);
                cell.Tile.CleanParams();
                if (iceCount <= 0) cell.Tile.Remove(TileState.VaseMod);
                M3EditorTile.RefreshGoals();
                M3EditorTile.RefreshGrid();
            }
        }
    }
}
#endif
