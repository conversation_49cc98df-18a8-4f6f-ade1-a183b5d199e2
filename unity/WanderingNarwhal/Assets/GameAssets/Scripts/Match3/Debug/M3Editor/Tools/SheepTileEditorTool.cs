#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;
using BBB.Match3;
 using UnityEngine;
using Grid = BBB.Grid;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class SheepTileEditorTool : TileTool
    {
        private const int Hp = 3;
        public SheepTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
            MultiTileElement = new MultiTileTntElement(TntTargetType.Sheep, M3EditorTile);
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            if (TryUpdateMultiTile(coords)) return;

            var cell = M3EditorTile.GetGridCell(coords);
            if (cell == null) return;
            cell.HardRemoveTile(0);
            List<TileParam> tileParams;
            if (Hp > 0)
            {
                tileParams = new List<TileParam>();
                var hp = Mathf.Clamp(prm, 0, Hp);
                tileParams.Add(new TileParam { Param = TileParamEnum.AdjacentHp, Value = hp });
            }
            var newTile = TileFactory.CreateTile(grid.TilesSpawnedCount++, TileAsset.Sheep, new TileOrigin(Creator.LevelEditor, cell));
            newTile.SetTileParams(tileParams);
            cell.AddTile(newTile);
            base.Apply(grid, coords, cardinalDirections, prm);
        }
    }
}
#endif
