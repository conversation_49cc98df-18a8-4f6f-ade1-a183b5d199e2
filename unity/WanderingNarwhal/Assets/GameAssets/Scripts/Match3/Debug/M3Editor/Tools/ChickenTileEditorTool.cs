#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;
using BBB.Match3;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class ChickenTileEditorTool : TileTool
    {
        public ChickenTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
            MultiTileElement = new MultiTileTntElement(TntTargetType.Chicken, M3EditorTile);
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            if (TryUpdateMultiTile(coords)) return;

            var cell = M3EditorTile.GetGridCell(coords);
            if (cell == null) return;
            cell.HardRemoveTile(0);

            //default chicken skin for add chicken
            const int skin = 0;
            var tileParams = new List<TileParam> { new() { Param = TileParamEnum.Skin, Value = skin } };
            var newTile = TileFactory.CreateTile(grid.TilesSpawnedCount++,
                TileAsset.Chicken, new TileOrigin(Creator.LevelEditor, cell));
            newTile.SetTileParams(tileParams);
            cell.AddTile(newTile);

            base.Apply(grid, coords, cardinalDirections, prm);
        }
    }
}
#endif