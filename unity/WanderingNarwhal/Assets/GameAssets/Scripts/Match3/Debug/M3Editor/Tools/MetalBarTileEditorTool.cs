#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.CellTypes;
using BBB.M3Editor;
using BBB.Match3;
using UnityEngine;
using Grid = BBB.Grid;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class MetalBarTileEditorTool : TileTool
    {
        public MetalBarTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
            MultiTileElement = new MultiTileTntElement(TntTargetType.MetalBar, M3EditorTile);
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            if (TryUpdateMultiTile(coords)) return;

            var originCell = M3EditorTile.GetGridCell(coords, false);
            if (originCell == null) return;

            var orientation = -90;

            CustomInputField.DeserializeParamsFromInt
                (prm, out var sizeX, out var sizeY, out var count, out var mirror);

            sizeX = Mathf.Max(1, sizeX);
            sizeY = Mathf.Max(1, sizeY);

            if (sizeX > 1)
            {
                sizeY = 1;
                count = sizeX - 1;
                orientation = mirror ? 0 : 180;
            }

            if (sizeY > 1)
            {
                sizeX = 1;
                count = sizeY - 1;
                orientation = mirror ? -90 : 90;
            }

            if (sizeX == 1 && sizeY == 1)
            {
                sizeX = 2;
                count = 1;
                orientation = mirror ? 0 : 180;
            }

            if (mirror)
                for (var x = coords.X; x < coords.X + sizeX; x++)
                for (var y = coords.Y; y > coords.Y - sizeY; y--)
                {
                    var pos = new Coords(x, y);
                    var cell = M3EditorTile.GetGridCell(pos, false);
                    if (cell == null || cell.HasMultiSizeCellReferenceWithMultiSizeTile()) return;
                }
            else
                for (var x = coords.X; x > coords.X - sizeX; x--)
                for (var y = coords.Y; y < coords.Y + sizeY; y++)
                {
                    var pos = new Coords(x, y);
                    var cell = M3EditorTile.GetGridCell(pos, false);
                    if (cell == null || cell.HasMultiSizeCellReferenceWithMultiSizeTile()) return;
                }

            if (mirror)
                for (var x = coords.X; x < coords.X + sizeX; x++)
                for (var y = coords.Y; y > coords.Y - sizeY; y--)
                {
                    grid.TryGetCell(new Coords(x, y), out var cell);
                    cell.HardRemoveTile(0);
                    cell.ClearState(true);
                    cell.UpdateCellBackgroundState();
                    cell.Add(CellState.MetalBar);
                    cell.MetalBarStatus = true;
                }
            else
                for (var x = coords.X; x > coords.X - sizeX; x--)
                for (var y = coords.Y; y < coords.Y + sizeY; y++)
                {
                    grid.TryGetCell(new Coords(x, y), out var cell);
                    cell.HardRemoveTile(0);
                    cell.ClearState(true);
                    cell.UpdateCellBackgroundState();
                    cell.Add(CellState.MetalBar);
                    cell.MetalBarStatus = true;
                }

            var tileParams = new List<TileParam>
            {
                new(TileParamEnum.SizeX, sizeX),
                new(TileParamEnum.SizeY, sizeY),
                new(TileParamEnum.AdjacentHp, count),
                new(TileParamEnum.MetalBarOrientation, orientation)
            };
            
            
            var newTile = TileFactory.CreateTile(
                grid.TilesSpawnedCount++,TileAsset.MetalBar,
                new TileOrigin(Creator.LevelEditor, originCell));
            newTile.SetTileParams(tileParams);

            originCell.AddTile(newTile);
            base.Apply(grid, coords, cardinalDirections, prm);
        }
    }
}
#endif
