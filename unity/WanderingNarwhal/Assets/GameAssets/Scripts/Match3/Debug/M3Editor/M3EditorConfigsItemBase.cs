#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    public abstract class M3EditorConfigsItemBase : IM3EditorConfigsItem
    {
        protected readonly M3Editor M3Editor;
        protected readonly M3EditorConfigs M3EditorConfigs;
        protected List<ItemObject> ItemObjects;

        protected M3EditorConfigsItemBase(M3Editor m3Editor, M3EditorConfigs m3EditorConfigs)
        {
            M3Editor = m3Editor;
            M3EditorConfigs = m3EditorConfigs;
        }

        public virtual void SetUpUi()
        {
            ItemObjects = new List<ItemObject>();

            foreach (var child in M3Editor.GetComponentsInChildren<Button>(true))
            {
                if (child.name == "AddItem")
                {
                    child.onClick.RemoveAllListeners();
                    child.onClick.AddListener(AddItem);
                }
            }
        }

        public abstract void LoadConfigsFrom(ILevel level);

        protected virtual void AddItem()
        {
            throw new NotImplementedException();
        }

        public virtual void Clear()
        {
            if(ItemObjects != null)
                for (int i = ItemObjects.Count - 1; i >= 0; i--)
                {
                    RemoveItem(ItemObjects[i].GameObject);
                }
        }

        protected virtual void RemoveItem(GameObject go)
        {
            var tileKindGoal = ItemObjects.First(t => t.GameObject == go);
            ItemObjects.Remove(tileKindGoal);
            M3Editor.DestroyGo(tileKindGoal.GameObject);

            M3EditorConfigs.ApplyConfigs();
        }
    }
    
    
    public class ItemObject
    {
        public readonly GameObject GameObject;
        public ItemObject(GameObject gameObject)
        {
            GameObject = gameObject;
        }
    }
}
#endif
