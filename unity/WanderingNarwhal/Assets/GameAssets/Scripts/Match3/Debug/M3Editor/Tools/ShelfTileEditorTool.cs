#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;
using BBB.Match3;
using Grid = BBB.Grid;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class ShelfTileEditorTool : TileTool
    {
        private const int Hp = 2;
        
        public ShelfTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
            MultiTileElement = new MultiTileTntElement(TntTargetType.Shelf, M3EditorTile);
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            if (TryUpdateMultiTile(coords)) return;

            var originCell = M3EditorTile.GetGridCell(coords, false);
            if (originCell == null) return;

            originCell.HardRemoveTile(0);

            var tileParams = new List<TileParam>
            {
                new(TileParamEnum.ShelfGroupIdentifier, prm),
                new(TileParamEnum.AdjacentHp, Hp)
            };

            var newTile = TileFactory.CreateTile(grid.TilesSpawnedCount++,
                TileAsset.Shelf, new TileOrigin(Creator.LevelEditor, originCell));
            newTile.SetTileParams(tileParams);

            originCell.AddTile(newTile);
            base.Apply(grid, coords, cardinalDirections, prm);
        }
    }
}
#endif