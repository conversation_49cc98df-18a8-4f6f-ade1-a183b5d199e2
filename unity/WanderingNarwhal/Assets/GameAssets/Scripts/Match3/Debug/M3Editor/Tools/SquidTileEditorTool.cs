#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;
using BBB.Match3;
using BBB.Match3.Renderer;
using UnityEngine;
using Grid = BBB.Grid;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class SquidTileEditorTool : TileTool
    {
        public SquidTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
            MultiTileElement = new MultiTileTntElement(TntTargetType.Squid, M3EditorTile);
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            if (TryUpdateMultiTile(coords)) return;

            var cell = M3EditorTile.GetGridCell(coords, false);
            if (cell == null) return;

            CustomInputField.DeserializeParamsFromInt(prm, out var sizeX, out var sizeY, out var count,
                out var isSingle);

            if (sizeX < 1) sizeX = 1;
            if (sizeY < 1) sizeY = 1;

            while (sizeX * sizeY > SquidTileLayer.MAX_SUBITEMS_COUNT)
                // incorrect size (like 2x6) will be reduced to 2x5 (if count limit is 10).
                // or 5x5 will be reduced to 3x3
                if (sizeX <= 1)
                    sizeY--;
                else if (sizeY <= 1)
                    sizeX--;
                else if (sizeY > sizeX)
                    sizeY--;
                else
                    sizeX--;

            var isFitGrid = true;
            for (var x = coords.X; x < coords.X + sizeX && isFitGrid; x++)
            for (var y = coords.Y; y < coords.Y + sizeY; y++)
            {
                var pos = new Coords(x, y);
                var c = M3EditorTile.GetGridCell(pos, false);
                if (c == null)
                {
                    isFitGrid = false;
                    break;
                }

                if (c.HasMultiSizeCellReference())
                {
                    foreach (var cellRef in c.CachedMultiSizeCellsReferences)
                    {
                        if (cellRef.TntCount > 0) continue;

                        if (cellRef.Coords != coords)
                        {
                            isFitGrid = false;
                            break;
                        }
                    }

                    if (!isFitGrid) break;
                }
            }

            if (!isFitGrid) return;

            for (var x = coords.X; x < coords.X + sizeX; x++)
            for (var y = coords.Y; y < coords.Y + sizeY; y++)
            {
                grid.TryGetCell(new Coords(x, y), out var c);
                c.HardRemoveTile(0);
                c.ClearState(true);
                c.UpdateCellBackgroundState();
            }

            count = isSingle ? 1 : Mathf.Min(Mathf.Max(1, count), sizeX * sizeY);
            var stateCount = isSingle ? sizeX * sizeY : count;
            var state = 0;
            for (var i = 0; i < stateCount; i++) state = SquidTileLayer.SetColorNumInState(state, 2, i);

            var tileParams = new List<TileParam>
            {
                new(TileParamEnum.SizeX, sizeX),
                new(TileParamEnum.SizeY, sizeY),
                new(TileParamEnum.SquidsCount, count),
                new(TileParamEnum.SquidsState, state)
            };
            
            if (isSingle)
            {
                tileParams.Add(new TileParam(TileParamEnum.AdjacentHp, sizeX * sizeY));
            }
            
            var newTile = TileFactory.CreateTile(
                grid.TilesSpawnedCount++, TileAsset.Squid,
                new TileOrigin(Creator.LevelEditor, cell));
            newTile.SetTileParams(tileParams);
            cell.AddTile(newTile);
            base.Apply(grid, coords, cardinalDirections, prm);
        }
    }
}
#endif
