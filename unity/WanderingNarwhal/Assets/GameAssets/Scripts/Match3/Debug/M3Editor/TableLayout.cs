using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    public class TableLayout : BbbMonoBehaviour
    {
        [SerializeField] private List<string> _columnNames;
        [SerializeField] private List<string> _defaultValues;
        [SerializeField] private RectTransform _headerHolder;
        [SerializeField] private GameObject _headerTextTemplate;
        [SerializeField] private RectTransform _rowsHolder;
        [SerializeField] private GameObject _rowTemplate;
        [SerializeField] private Button _addButton;
        [SerializeField] private Button _removeButton;

        private RectTransform _ownTransform;

        private readonly List<TableLayoutElement> _tableElements = new ();

        private void Start()
        {
            if (_columnNames.Count == 0)
                return;
            
            _ownTransform = GetComponent<RectTransform>();
            var headerTemplateTf = _headerTextTemplate.GetComponent<RectTransform>();
            
            headerTemplateTf.sizeDelta = new Vector2(_ownTransform.sizeDelta.x/_columnNames.Count,
                headerTemplateTf.sizeDelta.y);

            var rowTemplateTransform = _rowTemplate.GetComponent<RectTransform>();
            rowTemplateTransform.sizeDelta = new Vector2(_ownTransform.sizeDelta.x, rowTemplateTransform.sizeDelta.y);
            
            foreach (var columnName in _columnNames)
            {
                var go = GameObject.Instantiate(_headerTextTemplate, _headerHolder);
                var textMeshPro = go.GetComponent<TextMeshProUGUI>();
                textMeshPro.text = columnName;
                go.SetActive(true);
            }
            
            _addButton.ReplaceOnClick(AddButtonHandler);
            _removeButton.ReplaceOnClick(RemoveButtonHandler);
        }

        public void SetData(List<Dictionary<string, string>> dataList)
        {
            foreach(var tableElement in _tableElements)
                tableElement.gameObject.SetActive(false);
            
            for(int i = 0; i < dataList.Count; i++)
            {
                TableLayoutElement tableElement = null;
                if (i < _tableElements.Count)
                {
                    tableElement = _tableElements[i];
                }
                else
                {
                    var go = Instantiate(_rowTemplate, _rowsHolder);
                    tableElement = go.GetComponent<TableLayoutElement>();
                    _tableElements.Add(tableElement);
                }

                var rowData = dataList[i];
                tableElement.Apply(rowData, _columnNames);
                tableElement.gameObject.SetActive(true);
            }
        }

        public List<Dictionary<string, string>> GetData()
        {
            var result = new List<Dictionary<string, string>>();
            foreach (var row in _tableElements)
            {
                if (row.gameObject.activeSelf)
                {
                    result.Add(row.GetRowData(_columnNames));
                }
            }

            return result;
        }

        private void AddButtonHandler()
        {
            var go = Instantiate(_rowTemplate, _rowsHolder);
            var tableElement = go.GetComponent<TableLayoutElement>();
            var dict = new Dictionary<string, string>();
            for (int i = 0; i < _columnNames.Count; i++)
            {
                var columnName = _columnNames[i];
                var defValue = i < _defaultValues.Count ? _defaultValues[i] : "";
                dict.Add(columnName, defValue);
            }
            
            tableElement.Apply(dict, _columnNames);
            tableElement.gameObject.SetActive(true);
            
            _tableElements.Add(tableElement);
        }

        private void RemoveButtonHandler()
        {
            var activeElements = new List<TableLayoutElement>();
            foreach(var element in _tableElements)
                if (element.gameObject.activeSelf)
                {
                    activeElements.Add(element);
                }

            if (activeElements.Count == 0)
                return;
            
            var last = activeElements[^1];
            last.gameObject.SetActive(false);
        }
    }
}