#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;
using BBB.Match3;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class SodaTileEditorTool : TileTool
    {
        private const int Hp = 4;
        
        public SodaTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
            MultiTileElement = new MultiTileTntElement(TntTargetType.Soda, M3EditorTile);
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            if (TryUpdateMultiTile(coords)) return;
            var originCell = M3EditorTile.GetGridCell(coords, false);
            if (originCell == null) return;

            const int size = 2;

            var fitsToGrid = true;
            for (var x = coords.X; x < coords.X + size && fitsToGrid; x++)
            for (var y = coords.Y; y < coords.Y + size; y++)
            {
                var pos = new Coords(x, y);
                var cell = M3EditorTile.GetGridCell(pos, false);
                if (cell == null || cell.HasMultiSizeCellReferenceWithMultiSizeTile())
                {
                    fitsToGrid = false;
                    break;
                }
            }

            if (!fitsToGrid) return;

            for (var x = coords.X; x < coords.X + size; x++)
            for (var y = coords.Y; y < coords.Y + size; y++)
            {
                grid.TryGetCell(new Coords(x, y), out var cell);
                cell.HardRemoveTile(0);
                cell.ClearState(true);
                cell.UpdateCellBackgroundState();
            }
            
            var tileParams = new List<TileParam>
            {
                new(TileParamEnum.SizeX, size),
                new(TileParamEnum.SizeY, size),
                new(TileParamEnum.AdjacentHp, Hp),
                new(TileParamEnum.SodaColors, prm),
                new(TileParamEnum.SodaBottlesCount, Hp),
            };

            var newTile = TileFactory.CreateTile(
                grid.TilesSpawnedCount++, TileAsset.Soda,
                new TileOrigin(Creator.LevelEditor, originCell));
            newTile.SetTileParams(tileParams);

            originCell.AddTile(newTile);
            base.Apply(grid, coords, cardinalDirections, prm);
        }
    }
}
#endif
