#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using BBB.Match3.Systems;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.M3Editor
{
    public class BalanceReportStarted : IEvent
    {
        
    }
    
    public class BalanceReporter
    {
        public static string ConvertStatsToReportString(BalanceReportConfigObject reportConfig, Match3Statistics stats, IDictionary<string, int> goalDict, AutoBruteErrorCode errorCode)
        {
            var stringBuilder = new StringBuilder();

            var tileKindResults = stats.ResultTypes.Where(resultType =>
            {
                if (Enum.TryParse(resultType, true, out GoalType goal))
                {
                    return goal.IsTileKind();
                }
                return false;
            }).ToList();
            
            AppendTileKinds(tileKindResults, stringBuilder, reportConfig.EasyThreshold, stats);
            AppendTileKinds(tileKindResults, stringBuilder, reportConfig.MediumThreshold, stats);
            AppendTileKinds(tileKindResults, stringBuilder, reportConfig.HardThreshold, stats);
            
            var notGridBasedResults = stats.ResultTypes.Where(resultType =>
            {
                if (Enum.TryParse(resultType, true, out GoalType goal))
                {
                    return !goal.IsGridBased();
                }
                return false;
            }).ToList();
            
            var gridBasedResults = stats.ResultTypes.Where(resultType =>
            {
                if (Enum.TryParse(resultType, true, out GoalType goal))
                {
                    return goal.IsGridBased();
                }
                return false;
            }).ToList();

            AppendResultsReachedComment(notGridBasedResults, stringBuilder, stats, goalDict);
            AppendResultsReachedComment(gridBasedResults, stringBuilder, stats, goalDict);
            
            stringBuilder.Append($"{stats.CountAllEqualOrGreaterThenCeiling()}" +
                                 $"/" +
                                 $"{stats.MaxCount()} \t");
            
            stringBuilder.Append(stats.GetLossReasonString() + "\t");

            var remainingMoves = (float)(stats.GetResults("RemainingMoves")?.Average() ?? 0f);
            stringBuilder.Append(remainingMoves.ToString("F2") + "\t");
            var sbUsed = (float)(stats.GetResults("SuperBoostUsed")?.Average() ?? 0f);
            stringBuilder.Append(sbUsed.ToString("F2") + "\t");
            var lbCreated = (float)(stats.GetResults("LineBreaker")?.Average() ?? 0f);
            stringBuilder.Append(lbCreated.ToString("F2") + "\t");
            var bombCreated = (float)(stats.GetResults("Bomb")?.Average() ?? 0f);
            stringBuilder.Append(bombCreated.ToString("F2") + "\t");
            var boltCreated = (float)(stats.GetResults("Bolt")?.Average() ?? 0f);
            stringBuilder.Append(boltCreated.ToString("F2") + "\t");
            var propellerCreated = (float)(stats.GetResults("Propeller")?.Average() ?? 0f);
            stringBuilder.Append(propellerCreated.ToString("F2") + "\t");
            var reshuffles = (float)(stats.GetResults("Shuffles")?.Average() ?? 0f);
            stringBuilder.Append(reshuffles.ToString("F2") + "\t");
            var emptyMoves = (float)(stats.GetResults("EmptyMoves")?.Average() ?? 0f);
            stringBuilder.Append(emptyMoves.ToString("F2") + "\t");
            var minTurns = (float)(stats.GetResults("Turns")?.Min() ?? 0f);
            stringBuilder.Append(minTurns.ToString("F2") + "\t");
            var avgTurns = (float)(stats.GetResults("Turns")?.Average() ?? 0f);
            stringBuilder.Append(avgTurns.ToString("F2") + "\t");
            var medTurns = stats.GetThresholdForRatio("Turns", 0.5f);
            stringBuilder.Append(medTurns.ToString("F2") + "\t");
            return stringBuilder.ToString();
        }
        
        private static void AppendTileKinds(IList<string> tileKindResults, StringBuilder stringBuilder, float ratio, Match3Statistics stats)
        {
            var tileKindString = string.Empty;
            if (tileKindResults.Count > 0)
            {
                tileKindString += "{";
                foreach (var tileKindResult in tileKindResults)
                {
                    var count = stats.GetThresholdForRatio(tileKindResult, ratio).ToString();
                    tileKindString += $"\"{tileKindResult}\":{count}, ";
                }

                tileKindString = tileKindString.TrimEnd(' ').TrimEnd(',');
                tileKindString += "}";
            }

            stringBuilder.Append(tileKindString + "\t");

        }
        
        private static void AppendResultsReachedComment(IList<string> results, StringBuilder stringBuilder, Match3Statistics stats, IDictionary<string,int> goalDict)
        {
            var goalString = string.Empty;
            if (results.Count > 0)
            {
                foreach (var result in results)
                {
                    var reachCount = stats.CountEqualOrGreaterThenCeiling(result);
                    var count = stats.Count(result);
                    var goalCount = goalDict[result];
                    goalString += $"Config {result} goal equals {goalCount} and reached in {reachCount}/{count} times; ";
                }

                goalString = goalString.TrimEnd(' ').TrimEnd(';');
            }

            goalString += "\t";

            stringBuilder.Append(goalString);
        }
        
    }
}
#endif