using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Match3.Renderer;
using UnityEngine;
using UnityEngine.EventSystems;

#if UNITY_EDITOR
// ReSharper disable once CheckNamespace
namespace BBB.M3Editor
{
    // ReSharper disable once UnusedMember.Global
    public sealed class M3EditorTile : M3EditorBase
    {
        public M3EditorTile(M3Editor m3Editor) : base(m3Editor)
        {
        }

        public override void SetUpUi()
        {
        }

        public bool TryUpdateMultiTileElementWithTargetType(Coords coords, TntTargetType target)
        {
            var cell = GetGridCell(coords, false);

            //Prevent Ivy On Top Of TukTuk
            var tuktukCell = cell.GetMainCellReference(out _);
            if (tuktukCell.HasTile() && tuktukCell.Tile.Speciality == TileSpeciality.TukTuk &&
                target == TntTargetType.Ivy) return true;

            if (!cell.HasMultiSizeCellReference()) return false;

            cell = cell.GetMainCellReference(out _, true);
            if (!cell.IsAnyOf(CellState.Tnt)) return false;

            cell.TntTarget = target;
            cell.TntKind = TileKinds.None;
            RefreshGrid();
            return true;
        }

        public bool TryUpdateMultiTileElementWithColor(Coords coords, TileKinds color)
        {
            var cell = GetGridCell(coords, false);
            if (color != TileKinds.Undefined && !GameController.Level.UsedKinds.Contains(color)) return false;
            
            var itemIndex = -1;
            var hits = new List<RaycastResult>();
            var pointer = new PointerEventData(EventSystem.current) { position = Input.mousePosition };
            EventSystem.current.RaycastAll(pointer, hits);
            
            foreach (var hit in hits)
            {
                var multiSizeItem = hit.gameObject.GetComponent<IMultiSizeTileItemElement>();
                if (multiSizeItem == null) continue;
                itemIndex = multiSizeItem.ItemIndex;
                break;
            }
            
            var tempCell = cell.GetMainCellReference(out _);
            if (itemIndex < 0)
            {
                if (tempCell.HasTile() && tempCell.Tile.Speciality == TileSpeciality.TukTuk)
                {
                    if(color.IsColored())
                        tempCell.Tile.SetParam(TileParamEnum.TukTukColor, (int) color);
                    RefreshGrid();
                    return true;
                }

                if (!cell.HasMultiSizeCellReferenceWithCellOverlay()) return false;
                cell = cell.GetMainCellReference(out _, true);
                if (!cell.IsAnyOf(CellState.Tnt)) return false;
                cell.TntKind = color;
                cell.TntTarget = TntTargetType.Simple;
            }
            else
            {
                if (tempCell.HasTile() && tempCell.Tile.Speciality == TileSpeciality.Squid)
                { 
                    var tile = tempCell.Tile;
                    var state = tile.GetParam(TileParamEnum.SquidsState);
                    var isSingle = tile.GetParam(TileParamEnum.AdjacentHp) > 0;
                    var colorNum = SquidTileLayer.ColorToEncodedNum(color) & 0b111;
                    if (isSingle)
                    {
                        var sizeX = tile.GetParam(TileParamEnum.SizeX);
                        var sizeY = tile.GetParam(TileParamEnum.SizeY);
                        state = 0;

                        for (var x = 0; x < sizeX; x++)
                        for (var y = 0; y < sizeY; y++)
                        {
                            var i = sizeX * y + x;
                            state = SquidTileLayer.SetColorNumInState(state, colorNum, i);
                        }
                    }
                    else
                    {
                        state = SquidTileLayer.SetColorNumInState(state, colorNum, itemIndex);
                    }

                    tile.SetParam(TileParamEnum.SquidsState, state);
                }
            }

            RefreshGrid();
            return true;
        }

        public Cell GetGridCell(Coords coords, bool excludeMultiSizeTileOccupied = true)
        {
            if (Grid.TryGetCell(coords, out var result))
                if (!excludeMultiSizeTileOccupied || !result.HasMultiSizeCellReference())
                    return result;

            return null;
        }

        public void RefreshGrid()
        {
            if (!_m3Editor.PlayState)
                _m3Editor.GetSystem<M3EditorConfigs>().ApplyConfigs();
            else
                _m3Editor.ApplyGrid(Grid);
        }

        public void RefreshGoals()
        {
            if (!_m3Editor.PlayState)
            {
                var goals = GameController.Level.Goals;
                var levelUid = GameController.Level.Config.Uid;
                var optionalGoalTypes = GameController.Level.OptionalGoalsEnabled;
                goals.InitializeTargetGoalCountsIfNotManuallySpecified(Grid, levelUid, optionalGoalTypes);
            }
        }
    }
}
#endif