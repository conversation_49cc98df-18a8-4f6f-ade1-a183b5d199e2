#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;
using BBB.Match3;
using UnityEngine;
using Grid = BBB.Grid;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class ToadTileEditorTool : TileTool
    {
        public ToadTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            var cell = M3EditorTile.GetGridCell(coords, false);
            if (cell == null) return;

            CustomInputField.DeserializeParamsFromInt(prm, out var sizeX, out var sizeY, out _, out _);

            sizeX = Mathf.Max(1, Mathf.Min(sizeX, sizeY));
            sizeY = sizeX; // Keep always square size.

            var isFitGrid = true;
            for (var x = coords.X; x < coords.X + sizeX && isFitGrid; x++)
            for (var y = coords.Y; y < coords.Y + sizeY; y++)
            {
                var pos = new Coords(x, y);
                var c = M3EditorTile.GetGridCell(pos, false);
                if (c == null || c.HasMultiSizeCellReferenceWithMultiSizeTile())
                {
                    isFitGrid = false;
                    break;
                }
            }

            if (!isFitGrid) return;

            for (var x = coords.X; x < coords.X + sizeX; x++)
            for (var y = coords.Y; y < coords.Y + sizeY; y++)
            {
                grid.TryGetCell(new Coords(x, y), out var c);
                c.HardRemoveTile(0);
                c.ClearState(true);
                c.UpdateCellBackgroundState();
            }
            
            var tileParams = new List<TileParam>
            {
                new(TileParamEnum.SizeX, sizeX),
                new(TileParamEnum.SizeY, sizeY)
            };

            var newTile = TileFactory.CreateTile(
                grid.TilesSpawnedCount++,TileAsset.Toad,
                new TileOrigin(Creator.LevelEditor, cell));
            newTile.SetTileParams(tileParams);
            cell.AddTile(newTile);
            base.Apply(grid, coords, cardinalDirections, prm);
        }
    }
}
#endif
