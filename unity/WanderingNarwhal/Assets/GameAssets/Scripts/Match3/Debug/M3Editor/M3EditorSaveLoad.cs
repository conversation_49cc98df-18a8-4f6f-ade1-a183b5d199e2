#if UNITY_EDITOR
using System.Diagnostics.CodeAnalysis;
using BBB.CellTypes;
using BBB.Core;
using BBB.GameAssets.Scripts.Player;
using BBB.Match3.Systems;
using BBB.Navigation;
using BBB.UI;
using BBB.UI.Level;
using GameAssets.Scripts.Match3.Logic;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;

// ReSharper disable once CheckNamespace
namespace BBB.M3Editor
{
    // ReSharper disable once UnusedMember.Global
    [SuppressMessage("ReSharper", "UnusedMember.Global")]
    public sealed class M3EditorSaveLoad : M3EditorBase
    {
        private GameObject _playOn;
        private GameObject _playOff;

        private Button _refreshConfigButton;
        
        private readonly M3EditorLevelFileToConfigMap _fileToConfigMap;
        private readonly ILevelsOrderingManager _levelsOrderingManager;

        private readonly SpawnerSettingsManager _spawnerSettingsManager;

        public M3EditorSaveLoad(M3Editor m3Editor) : base(m3Editor)
        {
            _fileToConfigMap = m3Editor.Context.Resolve<M3EditorLevelFileToConfigMap>();
            _spawnerSettingsManager = m3Editor.Context.Resolve<SpawnerSettingsManager>();
            _levelsOrderingManager = m3Editor.Context.Resolve<ILevelsOrderingManager>();
            M3SaveLoadUtility.CurrentLevelUid = _fileToConfigMap.GetRemoteDataForFile(M3SaveLoadUtility.LastEditedLevelPath).Config.Uid;
        }

        public override void SetUpUi()
        {
            base.SetUpUi();

            var children = _m3Editor.GetComponentsInChildren<Button>(true);
            foreach (var child in children)
            {
                if (child.name == "PreviousLevel")
                {
                    child.onClick.RemoveAllListeners();
                    child.onClick.AddListener(LoadPrevLevel);
                    ObjectsDisabledDuringPlay.Add(child.gameObject);
                }

                if (child.name == "NextLevel")
                {
                    child.onClick.RemoveAllListeners();
                    child.onClick.AddListener(LoadNextLevel);
                    ObjectsDisabledDuringPlay.Add(child.gameObject);
                }

                if (child.name == "Save")
                {
                    child.onClick.RemoveAllListeners();
                    child.onClick.AddListener(SaveLevel);
                    ObjectsDisabledDuringPlay.Add(child.gameObject);
                }

                if (child.name == "SaveToLastOpened")
                {
                    child.onClick.RemoveAllListeners();
                    child.onClick.AddListener(SaveLevelToLastOpened);
                    ObjectsDisabledDuringPlay.Add(child.gameObject);
                }

                if (child.name == "Load")
                {
                    child.onClick.RemoveAllListeners();
                    child.onClick.AddListener(SelectAndLoadLevel);
                    ObjectsDisabledDuringPlay.Add(child.gameObject);
                }

                if (child.name == "Play")
                {
                    child.onClick.RemoveAllListeners();
                    child.onClick.AddListener(TogglePlaying);
                    _playOn  = child.transform.Find("On").gameObject;
                    _playOff = child.transform.Find("Off").gameObject;
                }

                if (child.name == "RefreshConfig")
                {
                    _refreshConfigButton = child;
                    child.onClick.RemoveAllListeners();
                    child.onClick.AddListener(RefreshConfig);
                    ObjectsDisabledDuringPlay.Add(child.gameObject);
                }

                if (child.name == "IntegrationTests")
                {
                    child.onClick.RemoveAllListeners();
                    child.onClick.AddListener(() => { _m3Editor.IntegrationTestsPopup.gameObject.SetActive(true); });
                }

                if (child.name == "Replay")
                {
                    child.onClick.RemoveAllListeners();
                    child.onClick.AddListener(() => { _m3Editor.ReplayPopup.gameObject.SetActive(true); });
                }
            }
        }

        private void RefreshConfig()
        {
            var context         = _m3Editor.Context;
            var configRefresher = context.Resolve<IConfigRefresher>();
            _refreshConfigButton.interactable = false;
            configRefresher.RefreshConfig(GameController.Level, () =>
            {
                _m3Editor.ApplyGrid(GameController.Grid);
                _refreshConfigButton.interactable = true;
            });
        }

        private void SaveLevel()
        {
            if (TestForSaving(GameController.Level, out var errorMsg))
            {
                _m3Editor.ExcepPopup.Show(errorMsg);
                return;
            }

            var pathAndFileName = EditorUtility.SaveFilePanel(
                title: "Save level as M3L",
                directory: System.IO.Path.GetDirectoryName(M3SaveLoadUtility.LastEditedLevelPath),
                defaultName: "LevelName",
                extension: LevelHelper.LEVELS_DOT_EXT.Remove(0,1));

            M3SaveLoadUtility.SaveLevelIntoFile(GameController.Level, pathAndFileName, updateCurrentLevelPath: true);
            var name = M3SaveLoadUtility.GetShortPath(pathAndFileName);
            _m3Editor.HardRestartWith(name, GameController.Level.Grid);
        }

        private static bool TestForSaving(ILevel level, out string errorMsg)
        {
            var grid = level.Grid;

            var hasSpawners = false;
            foreach (var cell in grid.Cells)
            {
                hasSpawners |= cell.IsAnyOf(CellState.Spawner);
            }

            if (!hasSpawners)
            {
                errorMsg = "This level has no tile spawners";
                return true;
            }

            foreach (var kind in level.UsedKinds)
            {
                if (!kind.IsColored())
                {
                    errorMsg = $"Invalid tile kind {kind} in used kinds for this level";
                    return true;
                }

                if (kind is TileKinds.Orange or TileKinds.White or TileKinds.Purple)
                {
                    errorMsg = $"Tile kind {kind} is not allowed in the level";
                    return true;
                }
            }

            errorMsg = null;
            return false;
        }

        private void SaveLevelToLastOpened()
        {
            if (TestForSaving(GameController.Level, out var errorMsg))
            {
                _m3Editor.ExcepPopup.Show(errorMsg);
                return;
            }

            var pathAndFileName = M3SaveLoadUtility.SaveLevelToLastLoadedFile(GameController.Level);
            var name = M3SaveLoadUtility.GetShortPath(pathAndFileName);
            _m3Editor.HardRestartWith(name, GameController.Level.Grid);
        }

        private void SelectAndLoadLevel()
        {
            var folder = System.IO.Path.GetDirectoryName(M3SaveLoadUtility.LastEditedLevelPath);
            if (folder.IsNullOrEmpty())
            {
                folder = Application.dataPath + "/Levels/";
            }

#if UNITY_EDITOR_OSX
            // on osx extensions with dots do not work
            var ext = System.IO.Path.GetExtension(LevelHelper.LEVELS_DOT_EXT).Remove(0, 1);
#else
            var ext = LevelHelper.LEVELS_DOT_EXT.Remove(0, 1);
#endif
            var path = EditorUtility.OpenFilePanel("Load level file", folder, ext);

            if (string.IsNullOrEmpty(path))
                return;

            var filePath = M3SaveLoadUtility.LoadLevelFromFile(path, out var level);
            var remoteDataForFile = _fileToConfigMap.GetRemoteDataForFile(filePath);
            M3SaveLoadUtility.CurrentLevelUid = remoteDataForFile.Config.Uid;
            level.SetupRemoteData(remoteDataForFile, _spawnerSettingsManager.SpawnerSettings);
            if (string.IsNullOrEmpty(filePath))
                return;

            GameController.SetLevel(level);
            var fileName = M3SaveLoadUtility.GetShortPath(filePath);

            _m3Editor.HardRestartWith(fileName, level.Grid);
        }

        private void LoadPrevLevel()
        {
            var previousLevelUid = _levelsOrderingManager.GetPreviousLevelUid(M3SaveLoadUtility.CurrentLevelUid);
            LoadLevelFromFilePath(previousLevelUid);
        }

        private void LoadNextLevel()
        {
            var nextLevelUid = _levelsOrderingManager.GetNextLevelUid(M3SaveLoadUtility.CurrentLevelUid);
            LoadLevelFromFilePath(nextLevelUid);
        }

        private void LoadLevelFromFilePath(string levelUid)
        {
            if (levelUid.IsNullOrEmpty())
            {
                BDebug.LogError(LogCat.Match3, "Level not found");
                return;
            }
            
            M3SaveLoadUtility.CurrentLevelUid = levelUid;
            var filePath = _fileToConfigMap.GetFilePathWithLevelUid(levelUid);
            
            if (filePath == null)
                return;

            M3SaveLoadUtility.LoadLevelFromFile(filePath, out var level);
            level.SetupRemoteData(_fileToConfigMap.GetRemoteDataForFile(filePath), _spawnerSettingsManager.SpawnerSettings);

            GameController.SetLevel(level);
            var fileName = M3SaveLoadUtility.GetShortPath(filePath);
            _m3Editor.HardRestartWith(fileName, level.Grid);
        }

        public void TogglePlaying()
        {
            _m3Editor.PlayState = !_m3Editor.PlayState;

            if (_m3Editor.PlayState)
            {
                _m3Editor.ClearTool();
                _m3Editor.BackUpGrid();
                _playOn.SetActive(false);
                _playOff.SetActive(true);
                _m3Editor.GameController.SimulateLoopException = true;
                
                var config = _m3Editor.Context.Resolve<IConfig>();
                _m3Editor.LevelAnalyticsReporter.LevelStarted(GameController.Level, config, string.Empty);
                _m3Editor.GameController.Run(true);
                _m3Editor.OnGameRun(true);
                _m3Editor.TileTickPlayer.Launch();
                RandomSystem.Reset();
            }
            else
            {
                _m3Editor.ClearTool();
                _m3Editor.RestoreGridFromBackUp();
                _playOn.SetActive(true);
                _playOff.SetActive(false);
                _m3Editor.GameController.SimulateLoopException = false;
                _m3Editor.Match3SimulationPlayer.Hide();
                _m3Editor.OnGameRun(false);
            }

            _m3Editor.GameController.LockInput(!_m3Editor.PlayState);
            if (!_m3Editor.PlayState)
            {
                _m3Editor.GameController.StopAllCoroutines();
            }
        }
    }
}
#endif
