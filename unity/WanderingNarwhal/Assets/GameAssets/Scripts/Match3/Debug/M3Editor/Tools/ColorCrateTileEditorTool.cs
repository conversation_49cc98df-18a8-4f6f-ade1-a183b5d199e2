#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;
using BBB.Match3;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class ColorCrateTileEditorTool : TileTool
    {
        public ColorCrateTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
            MultiTileElement = new MultiTileTntElement(TntTargetType.ColorCrate, M3EditorTile);
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            if (TryUpdateMultiTile(coords)) return;

            var cell = M3EditorTile.GetGridCell(coords);
            if (cell == null) return;

            if (!cell.Tile.IsNull())
            {
                var tileAsset = cell.Tile.Asset switch
                {
                    TileAsset.Green => TileAsset.ColorCrateGreen,
                    TileAsset.Yellow => TileAsset.ColorCrateYellow,
                    TileAsset.Blue => TileAsset.ColorCrateBlue,
                    TileAsset.Red => TileAsset.ColorCrateRed,
                    TileAsset.Purple => TileAsset.ColorCratePurple,
                    TileAsset.White => TileAsset.ColorCrateWhite,
                    _ => TileAsset.ColorCrateUndefined,
                };

                var hp = cell.Tile.GetParam(TileParamEnum.AdjacentHp);
                var tileParams = new List<TileParam>(1)
                {
                    new(TileParamEnum.AdjacentHp, 1)
                };
                var newTile = TileFactory.CreateTile(grid.TilesSpawnedCount++, tileAsset,
                    new TileOrigin(Creator.LevelEditor, cell));
                newTile.SetTileParams(tileParams);
                if (hp > 0)
                {
                    hp++;
                    if (hp > 3) hp = 1;

                    newTile.SetParam(TileParamEnum.AdjacentHp, hp);
                }

                cell.HardRemoveTile(0);
                cell.AddTile(newTile);
            }
            else
            { 
                var tileParams = new List<TileParam>(1)
                {
                    new(TileParamEnum.AdjacentHp, 1)
                };
                var created = TileFactory.CreateTile(grid.TilesSpawnedCount++, TileAsset.ColorCrateUndefined,
                    new TileOrigin(Creator.LevelEditor, cell));
                created.SetTileParams(tileParams);
                cell.AddTile(created);
            }

            base.Apply(grid, coords, cardinalDirections, prm);
        }
    }
}
#endif
