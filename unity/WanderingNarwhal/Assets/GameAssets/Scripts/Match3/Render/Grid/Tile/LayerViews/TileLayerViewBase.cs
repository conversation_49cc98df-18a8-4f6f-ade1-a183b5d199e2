using System;
using System.Collections;
using System.Collections.Generic;
using BBB.Core;
using BBB.DI;
using BebopBee.UnityEngineExtensions;
using DG.Tweening;
using GameAssets.Scripts.Match3.Settings;
using Spine.Unity;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public abstract class TileLayerViewBase : ITileLayerView, IContextInitializable
    {
        protected const string TargetLayerTag = "TargetSprite";

        /// <summary>
        /// Is currently renderer disabled for tile.
        /// </summary>
        /// <remarks>
        /// Used only for special tiles that spawn from other tiles (like <PERSON><PERSON>, <PERSON>, <PERSON>). -VK
        /// </remarks>
        protected bool _isHiddenState;

        private IEnumerable<ITileLayerView> _allLayerViews;
        private RectTransform _transform;
        private RendererContainers _rendererContainers;
        private readonly ITileLayer _layer;
        private GameObject _instance;
        protected IGridController GridController;
        protected TilesResources TilesResources;
        protected TileResourceSelector tileResourceSelector;
        protected M3Settings M3Settings;
        protected FxRenderer FxRenderer;
        protected Animator Animator;
        private SpriteRenderer _spriteRenderer;
        private bool _glow;
        private Tweener _glowTweener;
        private ICoroutineExecutor _coroutineExecutor;

        private bool _enabled;
        private Sequence _shakeTween;
        private bool _isShaking;

        public virtual float Alpha
        {
            get
            {
                if (_spriteRenderer != null)
                    return _spriteRenderer.color.a;
                else
                {
                    return 0f;
                }
            }
            set
            {
                if (_spriteRenderer != null)
                {
                    var color = _spriteRenderer.color;
                    color.a = value;
                    _spriteRenderer.color = color;
                }
            }
        }

        public bool Glow
        {
            get => _glow;
            set
            {
                if (_glow == value)
                    return;

                _glow = value;
                if (_spriteRenderer == null)
                    return;

                if (_glowTweener != null)
                    _glowTweener.Kill();

                _glowTweener = SetGlow(value);
            }
        }

        public virtual int SortingLayer
        {
            get => _spriteRenderer != null ? _spriteRenderer.sortingLayerID : 0;
            set
            {
                if (_spriteRenderer != null)
                    _spriteRenderer.sortingLayerID = value;
            }
        }

        protected virtual Tweener SetGlow(bool value)
        {
            return _spriteRenderer.material.DOFloat(_glow ? M3Settings.GlowMaxValue : M3Settings.GlowMinValue,
                M3Settings.GlowParam, M3Settings.GlowInOutDuration);
        }

        public virtual void AddToSortOrder(int increment)
        {
            if (_spriteRenderer != null)
                _spriteRenderer.sortingOrder += increment;
        }

        public virtual void RemoveFromSortOrder(int decrement)
        {
            if (_spriteRenderer != null)
                _spriteRenderer.sortingOrder -= decrement;
        }

        /// <summary>
        /// Flag that allows to delay release of tile view when corresponding grid tile is destroyed.
        /// </summary>
        /// <remarks>
        /// When tile destroy is triggered, tile is destroyed almost immediately,
        /// but some tiles have long destroy animation which requires delay of object destruction (release to pool).
        /// This property can be set to true for the period of custom destruction animation of the tile.
        /// </remarks>
        public bool IsPlayingLayerViewAnimation { get; protected set; }

        public TileLayerState State { get; }

        public bool Applied { get; private set; }

        protected TileLayerViewBase(ITileLayer layer)
        {
            State = layer.State;
            _layer = layer;
        }

        public virtual void InitializeByContext(IContext context)
        {
            if (tileResourceSelector == null)
            {
                TilesResources = context.Resolve<TilesResources>();
                tileResourceSelector = context.Resolve<TileResourceSelector>();
                M3Settings = context.Resolve<M3Settings>();
                FxRenderer = context.Resolve<FxRenderer>();
                GridController = context.Resolve<IGridController>();
            }

            _rendererContainers = context.Resolve<RendererContainers>();
            _coroutineExecutor = context.Resolve<ICoroutineExecutor>();
        }

        private static bool AnyScalableComponentFound(GameObject instance)
        {
            return instance.GetComponentInChildren<SkeletonMecanim>(true)
                   || instance.GetComponentInChildren<SkeletonRenderer>(true);
        }

        public virtual void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList,
            bool isVisible)
        {
            if (!Applied)
            {
                _instance = _instance ? _instance : InstantiateView(tile, _layer);
                _transform = _instance.GetComponent<RectTransform>();
                _allLayerViews = viewsList;
                _transform.SetParent(container, false);
                _transform.anchoredPosition = Vector2.zero;
                _transform.sizeDelta = TilesResources.TileSize;
                _spriteRenderer = _instance.GetComponentInChildren<SpriteRenderer>(true);

                if (_spriteRenderer != null || AnyScalableComponentFound(_instance))
                {
                    _transform.localScale = TilesResources.TileLayerSpriteRescale;
                    if (isVisible) Alpha = 1.0f;
                }

                Applied = true;
            }
        }

        public void Update(Tile tile, ITileLayer layer, int siblingIndex)
        {
            _instance.transform.SetSiblingIndex(siblingIndex);

            layer.ConfigurateRenderer(tile, _transform);

            if (!_enabled)
            {
                _instance.SetActive(true);
                _enabled = true;
            }
        }

        public virtual void Animate(Coords coords, TileLayerViewAnims anim, TileLayerViewAnimParams animParams = TileLayerViewAnimParams.None)
        {
        }

        public virtual void OnCoordsChanged(Vector2 coords)
        {
        }

        public virtual void AfterApply()
        {
        }

        public virtual void UnApply()
        {
            if (!Applied || _coroutineExecutor == null || (_coroutineExecutor is MonoBehaviour executor && executor == null) || AppController.IsDestroyed()) 
                return;
            
            _coroutineExecutor.StartCoroutine(UnApplyRoutine());
        }

        private IEnumerator UnApplyRoutine()
        {
            if (_instance != null && _shakeTween != null)
            {
                yield return _shakeTween.WaitForCompletion();
            }

            if (_instance == null) yield break;

            Applied = false;
            _enabled = false;
            _transform.localPosition = Vector3.zero;
            _transform.localRotation = Quaternion.identity;
            _transform = null;
            _instance.Release();
            _instance = null;
            _allLayerViews = null;

            if (Animator == null) yield break;

            Animator.ResetAllParameters();
            Animator.PlayMain();
            Animator.speed = 1f;
            Animator = null;
        }

        public void ShakeWithSiblings(Action onDone = null)
        {
            if (_allLayerViews == null)
                return;

            var transformsList = new List<TileLayerViewBase>();
            foreach (var view in _allLayerViews)
            {
                // Animal Layer should be excluded because it is trying to hide/move to next frame when the frame takes the damage
                if (view is not TileLayerViewBase layerView || layerView._transform == null || layerView._isShaking || layerView is AnimalLayerView) continue;
                transformsList.Add(layerView);
                layerView._isShaking = true;
            }

            if (transformsList.Count <= 0) return;

            _shakeTween?.Complete(true);
            _shakeTween ??= DOTween.Sequence();

            var posShakeSettings = M3Settings.TileShakeSettings.Item1;
            var rotShakeSettings = M3Settings.TileShakeSettings.Item2;

            foreach (var layer in transformsList)
            {
                _shakeTween.Join(DOTween.Shake(() => layer._transform.localPosition,
                        x => layer._transform.localPosition = x,
                        posShakeSettings.Duration, posShakeSettings.Amplitude,
                        posShakeSettings.Frequency, posShakeSettings.VectorAngleSpread, false, M3Settings.FadeOut)
                    .SetOptions(M3Settings.Snapping));

                _shakeTween.Join(DOTween.Shake(() => layer._transform.localEulerAngles,
                        x => layer._transform.localRotation = Quaternion.Euler(x),
                        rotShakeSettings.Duration, rotShakeSettings.Amplitude,
                        rotShakeSettings.Frequency, rotShakeSettings.VectorAngleSpread, false, M3Settings.FadeOut)
                    .SetOptions(M3Settings.Snapping));
            }

            _shakeTween.OnComplete(() =>
            {
                foreach (var layerView in transformsList)
                {
                    layerView._isShaking = false;
                }

                onDone.SafeInvoke();
                _shakeTween = null;
            });
        }

        private GameObject InstantiateView(Tile tile, ITileLayer layer)
        {
            var instance = _rendererContainers.GetLayersPool(_layer.State).Spawn();
            OnInstantiateView(instance, layer, TilesResources.CellSize);
            
            //For Updated tiles using new system
            OnInstantiateViewUpdated(instance, tile, TilesResources.CellSize);

            return instance;
        }

        protected virtual void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            
        }

        protected virtual void OnInstantiateViewUpdated(GameObject instance, Tile tile, Vector2 cellSize)
        {
            
        }

        /// <summary>
        /// Control disabled state of special tiles that spawn from other tiles.
        /// </summary>
        /// <remarks>
        /// This should be called for special tiles (Banana, Bee, Bird) from OnInstantiateView.
        /// </remarks>
        protected void ProcessRendererVisibilityOnInstantiateView()
        {
            if (_isHiddenState)
            {
                HideRenderer();
            }
            else
            {
                ShowRenderer();
            }
        }

        /// <summary>
        /// Control disabled state of special tiles that spawn from other tiles.
        /// Requires ShowRenderer and HideRenderer to be overriden for specific renderer.
        /// </summary>
        /// <remarks>
        /// Such tiles like Banana, Bee, Chicken or Bird have special spawn logic (Spawn tile from another tile logic),
        /// which requires that tile remains invisible in cell until Spawn animation is not finished.
        /// The process of disabling and enabling renderer is interconnected with m3 logical actions.
        /// </remarks>
        protected void ProcessRendererVisibilityOnApplyStarted(bool isVisible)
        {
            if (Applied)
            {
                // Apply is forced to be called after spawn animation ends.
                if (_isHiddenState)
                {
                    if (isVisible)
                    {
                        _isHiddenState = false;
                        ShowRenderer();
                    }
                }
            }
            else
            {
                if (!isVisible)
                {
                    // When bird is spawned from another tile (egg),
                    // then it should be hidden until flying item not reached target cell.
                    _isHiddenState = true;
                    HideRenderer();
                }
            }
        }

        /// <summary>
        /// Instant show renderer.
        /// </summary>
        /// <remarks>
        /// Used only for special tiles that spawn from other tiles,
        /// which should be disabled in cell during spawn animation. (Banana, Bee, Bird, Chicken).
        /// </remarks>
        protected virtual void ShowRenderer()
        {
        }

        protected virtual void HideRenderer()
        {
        }

        public virtual TileLayerRendererBase GetRenderer()
        {
            BDebug.LogError(LogCat.General, $"Not implemented for {GetType().Name}");
            return null;
        }
    }
}