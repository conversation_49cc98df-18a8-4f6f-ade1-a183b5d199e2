using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class HiveLayerView : SheetTileLayerViewBase
    {
        private HiveLayerRenderer _renderer;
        private int _prevLevel = -1;

        public HiveLayerView(ITileLayer layer) : base(layer)
        {
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<HiveLayerRenderer>();
            _renderer.InitialSetup();
        }

        protected override int SelectSheetsCountValue(Tile tile)
        {
            return tile.GetParam(TileParamEnum.BeeHiveOutOfBeesFlag);
        }

        public override void UnApply()
        {
            _prevLevel = -1;
            base.UnApply();
        }

        protected override void ShowRenderer()
        {
            if (_renderer != null) _renderer.OnSpawn();
        }

        protected override void HideRenderer()
        {
            if (_renderer != null) _renderer.Hide();
        }

        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            // Hive will always have 0 newLevel.
            _renderer.IsOutOfBees = newLevel > 0;
            if (_prevLevel >= 0)
            {
                _renderer.PlayHit();
            }
            else
            {
                _renderer.PlayIdle();
            }

            _prevLevel = newLevel;
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim);
            switch (anim)
            {
                case TileLayerViewAnims.Destroy:
                    IsPlayingLayerViewAnimation = true;
                    _renderer.PlayDestroy(onDone: () => { IsPlayingLayerViewAnimation = false; });
                    break;
                case TileLayerViewAnims.CustomAppear:
                    _renderer.PlayHiveAppear();
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;
            }
        }
    }
}