using BBB.Audio;
using BebopBee.Core;
using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class FireWorksLayerView : SheetTileLayerViewBase
    {
        private FireWorksLayerRenderer _renderer;
        private int _prevLevel = -1;

        public FireWorksLayerView(ITileLayer layer) : base(layer)
        {
            
        }

        protected override int SelectSheetsCountValue(Tile tile)
        {
            return tile.GetParam(TileParamEnum.AdjacentHp);
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<FireWorksLayerRenderer>();
            if (_renderer == null)
            {
                UnityEngine.Debug.LogError($"Missing component {nameof(FireWorksLayerRenderer)} on tile prefab: {instance.name}", instance);
            }
            else
            {
                _renderer.ResetView();
            }
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim, TileLayerViewAnimParams animParams = TileLayerViewAnimParams.None)
        {
            base.Animate(coords, anim);

            switch (anim)
            {
                case TileLayerViewAnims.Destroy:
                    AudioProxy.PlaySound(Match3SoundIds.FireworksDestroy);
                    FxRenderer.SpawnSingleAnimationEffect(coords, FxType.FireWorksTileDestroy, 6f);
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;
            }
        }

        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            if (newLevel < _prevLevel)
            {
                _renderer.PlayFx();
                ShakeWithSiblings();
                var settings = M3Settings.GetSettingsForEffect<FireWorksEffectSettings>(FxType.FireWorksFlight);
                var delay = settings.FireWorksDelayForLayerOne;

                if (coords.HasValue && newLevel != 0)
                {
                    FxRenderer.SpawnSingleAnimatorEffect(coords.Value, FxType.FireWorksRemoveLayer, 6f);
                    delay = settings.FireWorksDelayForLayerTwo;
                }

                Rx.Invoke(delay, _ =>
                {
                    _renderer.UpdateView(newLevel);
                });
            }
            else
            {
                _renderer.UpdateView(newLevel);
            }

            _prevLevel = newLevel;
        }
    }
}
