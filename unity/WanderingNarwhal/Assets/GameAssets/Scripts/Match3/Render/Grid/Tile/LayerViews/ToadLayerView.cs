using System.Collections.Generic;
using BBB.MMVibrations.Plugins;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class ToadLayerView : SheetTileLayerViewBase
    {
        private ToadLayerRenderer _renderer;

        private int _size;
        private Vector2 _cellSize;

        public ToadLayerView(ITileLayer layer) : base(layer)
        {
        }

        protected override void OnInstantiateViewUpdated(GameObject instance, Tile tile, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<ToadLayerRenderer>();
            _renderer.Init(tile);
            _cellSize = cellSize;
        }

        protected override int SelectSheetsCountValue(Tile tile)
        {
            return -1;
        }

        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            SetupRendererSize(tile);
            _renderer.PlayHit();
        }

        public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList,
            bool isVisible)
        {
            base.Apply(tile, coords, container, viewsList, isVisible);
            SetupRendererSize(tile);
            _renderer.OnRefresh();
        }

        private void SetupRendererSize(Tile tile)
        {
            if (_renderer == null) return;
            if (tile is null) return;

            _renderer.CellSize = _cellSize;

            _size = Mathf.Max(1, tile.GetParam(TileParamEnum.SizeX));
            _renderer.Setup(_size);
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim);
            _renderer.Setup(_size);
            switch (anim)
            {
                case TileLayerViewAnims.Destroy:
                    IsPlayingLayerViewAnimation = true;
                    _renderer.PlayDestroy(OnBaseDestroyed, OnDone);

                    void OnBaseDestroyed()
                    {
                        FxRenderer.PlayHapticFeedback(ImpactPreset.HeavyImpact);
                    }

                    void OnDone()
                    {
                        IsPlayingLayerViewAnimation = false;
                    }
                    break;
                case TileLayerViewAnims.CustomAppear:
                    _renderer.PlayAppear();
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;
            }
        }
    }
}