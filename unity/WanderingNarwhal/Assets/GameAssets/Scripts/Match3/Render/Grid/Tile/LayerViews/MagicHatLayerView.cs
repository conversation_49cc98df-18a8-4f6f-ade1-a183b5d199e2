using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class MagicHatLayerView : SheetTileLayerViewBase
    {
        private MagicHatLayerRenderer _renderer;
        private int _prevLevel = -1;
        
        public MagicHatLayerView(ITileLayer layer) : base(layer)
        {
        }

        protected override void OnInstantiateViewUpdated(GameObject instance, Tile tile, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<MagicHatLayerRenderer>();
            _renderer.Init(tile);
        }

        protected override int SelectSheetsCountValue(Tile tile)
        {
            return tile.GetParam(TileParamEnum.MagicHatOutOfRabbitsFlag);
        }

        public override void UnApply()
        {
            _prevLevel = -1;
            base.UnApply();
        }

        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            _renderer.IsOutOfRabbits = newLevel > 0;
            if (_prevLevel >= 0)
            {
                _renderer.PlayHit();
                _prevLevel = newLevel;
            }
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim);
            switch (anim)
            {
                case TileLayerViewAnims.Destroy:
                    IsPlayingLayerViewAnimation = true;
                    _renderer.PlayDestroy(onDone: () => { IsPlayingLayerViewAnimation = false; });
                    break;
                case TileLayerViewAnims.CustomAppear:
                    _renderer.PlayAppear();
                    _prevLevel = 0; 
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;
            }
        }
    }
}