using System.Collections.Generic;
using BBB.Audio;
using BebopBee.Core.Audio;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class DropItemLayerView : TileLayerViewBase
    {
        private DropItemLayerRenderer _renderer;
        private SpriteRenderer _targetSpriteRenderer;
        
        public DropItemLayerView(ITileLayer layer) : base(layer)
        {
        }
        
        public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList,
            bool isVisible)
        {
            base.Apply(tile, coords, container, viewsList, isVisible);

            if(_targetSpriteRenderer != null)
                _targetSpriteRenderer.sprite = tileResourceSelector.GetGoalSprite(GoalType.DropItems);
        }
        
        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim);

            switch (anim)
            {
                case TileLayerViewAnims.Destroy:
                    AudioProxy.PlaySound(Match3SoundIds.DropItemFall);
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;
            }
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<DropItemLayerRenderer>();
            Animator = instance.GetComponent<Animator>();
    
            Transform targetSpriteGo = null;
            for (var i = 0; i < instance.transform.childCount; i++)
            {
                var child = instance.transform.GetChild(i);
                if (!child.CompareTag(TargetLayerTag)) continue;
                
                targetSpriteGo = child;
                break;
            }

            if (targetSpriteGo != null)
            {
                _targetSpriteRenderer = targetSpriteGo.GetComponent<SpriteRenderer>();
            }
            else
            {
                UnityEngine.Debug.LogError($"Object with tag {TargetLayerTag} not found in DropItem prefab");
            }
        }

    }
}