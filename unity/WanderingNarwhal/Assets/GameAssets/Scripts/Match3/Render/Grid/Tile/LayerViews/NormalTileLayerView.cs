using System.Collections.Generic;
using BBB.Core;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class NormalTileLayerView : TileLayerViewBase
    {
        private NormalTileRenderer _renderer;
        private TileKinds _kind;

        public override float Alpha
        {
            get { return base.Alpha; }
            set
            {
                _renderer.Alpha = value;
                base.Alpha = value;
            }
        }

        public override int SortingLayer
        {
            get { return base.SortingLayer; }
            set
            {
                _renderer.SortLayer = value;
                base.SortingLayer = value;
            }
        }

        public NormalTileLayerView(ITileLayer layer) : base(layer)
        {
        }

        public override void AddToSortOrder(int increment)
        {
            base.AddToSortOrder(increment);
            _renderer.AddToSortOrder(increment);
        }

        public override void RemoveFromSortOrder(int decrement)
        {
            base.RemoveFromSortOrder(decrement);
            _renderer.RemoveFromSortOrder(decrement);
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponentInChildren<NormalTileRenderer>();
        }

        public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList, bool isVisible)
        {
            _kind = tile.Kind;

            base.Apply(tile, coords, container, viewsList, isVisible);
            var kind = tile.Kind;
            TilesResources
                .GetAsync(kind)
                .ContinueWith(tileData => _renderer.SetData(tileData))
                .Forget();
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim, animParams);
            switch (anim)
            {
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;
            }

            if (anim == TileLayerViewAnims.Destroy)
            {
                switch (animParams)
                {
                    case TileLayerViewAnimParams.ByWhirlpool:
                    {
                        FxRenderer.SpawnRemoveTileByBombBombEffect(coords); 
                        break;
                    }
                    case TileLayerViewAnimParams.ByRemoveColorTiles:
                    { 
                        //YA: This effect has a white explosion that causes flickering when used with bolt
                        // FxRenderer.SpawnRemoveTileByBolt(coords, _kind); 
                        FxRenderer.SpawnRemoveTile(coords, _kind); 
                        break;
                    }
                    default:
                    {
                        FxRenderer.SpawnRemoveTile(coords, _kind);
                        break;
                    }
                }
            }
        }
    }
}