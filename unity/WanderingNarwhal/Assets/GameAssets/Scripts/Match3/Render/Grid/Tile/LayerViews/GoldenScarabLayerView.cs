using BBB.Audio;
using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class GoldenScarabLayerView : SheetTileLayerViewBase
    {
        private GoldenScarabLayerRenderer _renderer;
        private int _prevLevel = -1;
        private int _size;
        
        public GoldenScarabLayerView(ITileLayer layer) : base(layer)
        {
            
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<GoldenScarabLayerRenderer>();
        }

        protected override int SelectSheetsCountValue(Tile tile)
        {
            var count = tile.GetParam(TileParamEnum.AdjacentHp);
            return count;
        }

        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            if (newLevel < _prevLevel)
            {
                var fx = FxType.GoldenScarabEmpty;
                if (_prevLevel == 2 && tile.HasParam(TileParamEnum.GoldenScarabCount))
                {
                    var count = tile.GetParam(TileParamEnum.GoldenScarabCount);
                    if (count == 0)
                    {
                        _prevLevel++;
                       fx = FxType.GoldenScarabFull;
                    }
                }
                _renderer.PlayHit(_prevLevel);
                
                var pos = coords ?? Coords.Zero;
                if (newLevel > 0)
                {
                    AudioProxy.PlaySound(Match3SoundIds.BushLayerRemoved);
                }
                FxRenderer.SpawnSingleAnimatorEffect(pos, fx, _renderer.DestroyDuration);
            }
            _prevLevel = newLevel;
        }
        
        public override void Animate(Coords coords, TileLayerViewAnims anim, TileLayerViewAnimParams animParams = TileLayerViewAnimParams.None)
        {
            base.Animate(coords, anim, animParams);
            switch (anim)
            {
                case TileLayerViewAnims.CustomAppear:
                    _renderer.PlayAppear();
                    break;

                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;

                case TileLayerViewAnims.Destroy:
                    IsPlayingLayerViewAnimation = true;
                    AudioProxy.PlaySound(Match3SoundIds.BushExpand);
                    _renderer.PlayDestroy(OnDone);
                    FxRenderer.SpawnSingleAnimatorEffect(coords, FxType.GoldenScarabDestroy, _renderer.DestroyDuration);
                    void OnDone()
                    {
                        IsPlayingLayerViewAnimation = false;
                    }
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
            }
        }
    }
}
