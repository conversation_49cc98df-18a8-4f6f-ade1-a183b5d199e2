using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class ColorBombLayerView : TileLayerViewBase
    {
        private ISpecialTileRenderer _renderer;

        public override float Alpha
        {
            get { return base.Alpha; }
            set
            {
                _renderer.SetAlpha(value);
                base.Alpha = value;
            }
        }

        public override int SortingLayer
        {
            get => base.SortingLayer;
            set
            {
                base.SortingLayer = value;
                _renderer.SortingLayer = value;
            }
        }

        public ColorBombLayerView(ITileLayer layer) : base(layer)
        {
        }
        
        
        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            Animator = instance.GetComponent<Animator>();
            _renderer = instance.GetComponent<ISpecialTileRenderer>();
            _renderer.Setup(M3Settings, TileSpeciality.ColorBomb);
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim, animParams);
            
            switch (anim)
            {
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;
            }
        }
    }
}