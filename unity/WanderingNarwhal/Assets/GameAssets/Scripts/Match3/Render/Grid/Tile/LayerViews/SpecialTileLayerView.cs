using System;
using System.Collections.Generic;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class SpecialTileLayerView : TileLayerViewBase
    {
        private ISpecialTileRenderer _renderer;
        public Vector3 SpecialRendererGlobalScale => _renderer.GetGlobalScale();

        public override float Alpha
        {
            get => base.Alpha;
            set
            {
                _renderer.SetAlpha(value);
                base.Alpha = value;
            }
        }

        public override int SortingLayer
        {
            get => base.SortingLayer;
            set
            {
                _renderer.SortingLayer = value;
                base.SortingLayer = value;
            }
        }

        public SpecialTileLayerView(ITileLayer layer) : base(layer)
        {
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<ISpecialTileRenderer>();
        }

        public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList,
            bool isVisible)
        {
            base.Apply(tile, coords, container, viewsList, isVisible);
            _renderer.Setup(M3Settings, tile.Speciality);
        }

        public override void UnApply()
        {
            base.UnApply();
            if(_renderer is PropellerLayerRenderer propeller)
                propeller.ResetSpine();
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim);
            switch (anim)
            {
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;
                case TileLayerViewAnims.Preactivated:
                    _renderer.PlayPreactivate();
                    break;
                case TileLayerViewAnims.BoltCombo:
                    if (_renderer is IFormableTileRenderer fRenderer3)
                    {
                        fRenderer3.PlayBoltPreactivate();
                    }
                    break;
                case TileLayerViewAnims.FormationByBoltIntro:
                    if (_renderer is IFormableTileRenderer fRenderer1)
                    {
                        fRenderer1.PlayFormationByBolt();
                    }
                    break;
                case TileLayerViewAnims.FormationIntro:
                    if (_renderer is IFormableTileRenderer fRenderer2)
                    {
                        fRenderer2.PlayStandardFormation();
                    }
                    break;
                case TileLayerViewAnims.Swap:
                {
                    _renderer.PlaySwap();
                    break;
                }
            }
        }

        public void SetIntroCallback(Action callback)
        {
            _renderer.SetupCallbackOnIntro(callback);   
        }
    }
}