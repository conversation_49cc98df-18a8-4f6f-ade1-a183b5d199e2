using System.Collections.Generic;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public abstract class SheetTileLayerViewBase : TileLayerViewBase, ICounteredTile
    {
        public const string SheetLayerTag = "SheetLayer";

        protected SheetTileLayerViewBase(ITileLayer layer) : base(layer)
        {
        }

        protected abstract int SelectSheetsCountValue(Tile tile);

        protected abstract void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null);

        public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList,
            bool isVisible)
        {
            base.Apply(tile, coords, container, viewsList, isVisible);
            var count = SelectSheetsCountValue(tile);
            ChangeSheetLevel(count, tile);
        }

        public void ChangeCount(Cell cell)
        {
            ChangeSheetLevel(SelectSheetsCountValue(cell.Tile), cell.Tile, cell.Coords);
        }
    }
}