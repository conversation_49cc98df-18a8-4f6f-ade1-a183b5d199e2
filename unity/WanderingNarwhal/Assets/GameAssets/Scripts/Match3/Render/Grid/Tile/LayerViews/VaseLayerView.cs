using BBB.Audio;
using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class VaseLayerView : SheetTileLayerViewBase
    {
        private VaseLayerRenderer _renderer;
        private int _prevLevel = -1;

        public VaseLayerView(ITileLayer layer) : base(layer)
        {
        }

        protected override int SelectSheetsCountValue(Tile tile)
        {
            return tile.GetParam(TileParamEnum.VaseLayerCount);
        }
        
        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<VaseLayerRenderer>();
            _renderer.ResetView();
        }

        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            _renderer.ResetView(newLevel);

            if (_prevLevel != newLevel)
            {
                if (newLevel < _prevLevel)
                {
                    AudioProxy.PlaySound(Match3SoundIds.VaseDestroy);

                    if (coords.HasValue)
                    {
                        if (newLevel == 0)
                        {
                            FxRenderer.SpawnSingleAnimatorEffect(coords.Value, FxType.VaseDestroy);
                        }
                        else if (newLevel == 1)
                        {
                            FxRenderer.SpawnSingleAnimatorEffect(coords.Value, FxType.VaseSecondLayerRemove);
                        }
                        else if (newLevel == 2)
                        {
                            FxRenderer.SpawnSingleAnimatorEffect(coords.Value, FxType.VaseThirdLayerRemove);
                        }
                    }

                    if (newLevel > 0 && _renderer.allowShakeOnHit)
                        ShakeWithSiblings();
                }

                _prevLevel = newLevel;
            }
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim);

            switch (anim)
            {
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;
            }
        }

        public override void UnApply()
        {
            _prevLevel = -1;

            if (_renderer != null)
            {
                _renderer.ResetView();
            }

            base.UnApply();
        }
    }
}