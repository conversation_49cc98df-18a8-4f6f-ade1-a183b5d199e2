using BBB.Audio;
using BBB.MMVibrations.Plugins;
using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class SandLayerView : TileLayerViewBase
    {
        private SandLayerRenderer _renderer;
        private bool _appeared = false;

        public SandLayerView(ITileLayer layer) : base(layer)
        {
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<SandLayerRenderer>();
            if (!Applied)
            {
                _renderer.gameObject.SetActive(true);
                _renderer.OnInstantiated(FxRenderer);
                _appeared = true;
            }
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim);
            //_renderer.PlayDisappear();
            switch (anim)
            {
                case TileLayerViewAnims.Destroy:
                    if (_appeared && Applied)
                    {
                        _appeared = false; 
                        //FxRenderer.SpawnSingleAnimatorEffect(coords, FxType.SandRemove);
                        IsPlayingLayerViewAnimation = true;
                        _renderer.PlayDisappear(OnHideFinished);
                    }
                    break;
                case TileLayerViewAnims.Unapply:
                    if (_appeared)
                    {
                        _appeared = false;
                        AudioProxy.PlaySound(Match3SoundIds.SandExit);
                        FxRenderer.SpawnSingleAnimationEffect(coords, FxType.SandRemove);
                        //IsPlayingLayerViewAnimation = true;
                        //_renderer.PlayDisappear(OnHideFinished);
                    }
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;
            }
        }

        private void OnHideFinished()
        {
            IsPlayingLayerViewAnimation = false;
        }
    }
}