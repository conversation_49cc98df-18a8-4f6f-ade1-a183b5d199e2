using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class LitterItemTileLayer : TileLayerBase
    {
        public override TileLayerState State { get { return TileLayerState.Litter; } }
        

        protected override bool IsCondition(TileLayerState state)
        {
            return (state & TileLayerState.Litter) != 0;
        }

        public override void ConfigurateRenderer(Tile tile, RectTransform rendererTransform)
        {
            base.ConfigurateRenderer(tile, rendererTransform);
            rendererTransform.sizeDelta = TilesResources.TileSize * 2f;
        }
    }
}