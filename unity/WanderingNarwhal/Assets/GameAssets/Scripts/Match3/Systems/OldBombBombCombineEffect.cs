using System.Collections.Generic;
using BBB;
using BBB.Match3.Renderer;
using BebopBee.Core;
using DG.Tweening;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    public class OldBombBombCombineEffect : BbbMonoBehaviour, IPoolItem
    {
        [SerializeField] private int _cyclesNumber = 4;
        [SerializeField] private float _verticalOffset = 0.1f;
        [SerializeField] private Transform _firstTileHolder;
        [SerializeField] private Transform _secondTileHolder;

        private Animator _animator;

        private Transform _initialFirstTileParent;
        private Transform _initialSecondTileParent;

        private Transform _firstTileViewTf;
        private Transform _secondTileViewTf;

        private TileView _firstTileView;
        private TileView _secondTileView;

        private Vector3 _initialLocalPositionFirst;
        private Vector3 _initialLocalPositionSecond;

        private readonly List<Tween> _tweens = new List<Tween>();

        public void Setup(TileView firstTileView, TileView secondTileView, float time)
        {
            _firstTileView = firstTileView;
            _secondTileView = secondTileView;

            _firstTileViewTf = firstTileView.transform;
            _secondTileViewTf = secondTileView.transform;

            _initialLocalPositionFirst = _firstTileViewTf.localPosition;
            _initialLocalPositionSecond = _secondTileViewTf.localPosition;

            _initialFirstTileParent = _firstTileViewTf.parent;
            _initialSecondTileParent = _secondTileViewTf.parent;

            firstTileView.Animator.SetAnimatorEnabled(false);
            secondTileView.Animator.SetAnimatorEnabled(false);

            _firstTileViewTf.SetParent(_firstTileHolder, true);
            _secondTileViewTf.SetParent(_secondTileHolder, true);

            var firstPos = _firstTileViewTf.localPosition;
            var secondPos = _secondTileViewTf.localPosition;

            var midPoint = (firstPos + secondPos) * 0.5f;
            var firstIntermediatePoint = midPoint;
            var secondIntermediatePoint = midPoint;

            var absoluteOffset = Mathf.Abs(firstPos.x - secondPos.x) * _verticalOffset;
            firstIntermediatePoint.y += absoluteOffset;
            secondIntermediatePoint.y -= absoluteOffset;

            var semiCycleTime = 0.5f * time / _cyclesNumber;

            var firstSequence = DOTween.Sequence();
            firstSequence.Append(_firstTileViewTf.DOLocalPath(new[] {firstIntermediatePoint,secondPos}, semiCycleTime, PathType.CatmullRom)
                .SetEase(Ease.InOutSine));
            firstSequence.Append(_firstTileViewTf.DOLocalPath(new[] {secondIntermediatePoint,firstPos}, semiCycleTime, PathType.CatmullRom)
                .SetEase(Ease.InOutSine));
            firstSequence.SetLoops(_cyclesNumber);
            firstSequence.OnComplete(ReleaseViews);

            var secondSequence = DOTween.Sequence();
            secondSequence.Append(_secondTileViewTf.DOLocalPath(new[] {secondIntermediatePoint,firstPos}, semiCycleTime, PathType.CatmullRom)
                .SetEase(Ease.InOutSine));
            secondSequence.Append(_secondTileViewTf.DOLocalPath(new[] {firstIntermediatePoint,secondPos}, semiCycleTime, PathType.CatmullRom)
                .SetEase(Ease.InOutSine));
            secondSequence.SetLoops(_cyclesNumber);

            var a = _firstTileView;
            var b = _secondTileView;

            var seriesTween = Rx.InvokeSeries(semiCycleTime, _cyclesNumber * 2, () =>
                {
                    a.MoveLayersOnTop();
                    b.MoveLayersToBottom();

                    var temp = a;
                    a = b;
                    b = temp;
                }).OnKill(() =>
                {
                    _firstTileView.MoveLayersToBottom();
                    _secondTileView.MoveLayersToBottom();
                })
                .OnComplete(() =>
                {
                    _firstTileView.MoveLayersToBottom();
                    _secondTileView.MoveLayersToBottom();
                });

            _tweens.Add(firstSequence);
            _tweens.Add(secondSequence);
            _tweens.Add(seriesTween);
        }

        private void ReleaseViews()
        {
            if (_firstTileViewTf.gameObject.activeSelf)
            {
                _firstTileViewTf.SetParent(_initialFirstTileParent);
                _firstTileViewTf.localPosition = _initialLocalPositionFirst;
                _firstTileViewTf.gameObject.SetActive(false);
                _firstTileViewTf = null;
            }

            if (_secondTileViewTf.gameObject.activeSelf)
            {
                _secondTileViewTf.SetParent(_initialSecondTileParent);
                _secondTileViewTf.localPosition = _initialLocalPositionSecond;  
                _secondTileViewTf.gameObject.SetActive(false);
                _secondTileViewTf = null;
            }

            _initialFirstTileParent = null;
            _initialSecondTileParent = null;

            if (_firstTileView.gameObject.activeSelf)
            {
                _firstTileView.Animator.SetAnimatorEnabled(true);
                _firstTileView = null;
            }

            if (_secondTileViewTf.gameObject.activeSelf)
            {
                _secondTileView.Animator.SetAnimatorEnabled(true);
                _secondTileView = null;
            }

            foreach(var tween in _tweens)
                tween.Kill();

            _tweens.Clear();
        }

        void IPoolItem.OnInstantiate()
        {
            _animator = GetComponent<Animator>();
            gameObject.SetActive(false);
        }

        void IPoolItem.OnSpawn()
        {
            if (this == null) return;
            gameObject.SetActive(true);
        }

        void IPoolItem.OnRelease()
        {
            if (this == null) return;
            
            ReleaseViews();

            gameObject.SetActive(false);

            if (_animator.gameObject.activeSelf)
            {
                _animator.Rebind();
            }
        }
    }
}