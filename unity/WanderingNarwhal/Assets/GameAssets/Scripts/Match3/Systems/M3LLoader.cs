using System;
using System.Threading;
using BBB.Core.ResourcesManager;
using BBB.DI;
using Core.Debug;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;

namespace BBB.Match3.Systems
{
    public class M3LLoader : ILevelLoader, IContextInitializable, IContextReleasable
    {
        private readonly LogStopwatch _logStopwatch = new("red");
        private IAssetsManager              _assetsManager;
        private SpawnerSettingsManager      _spawnerSettingsManager;
        private string                      _fullNameToLoad;
        private ILevel                      _loadedLevel;

        public void InitializeByContext(IContext context)
        {
            _assetsManager = context.Resolve<IAssetsManager>();
            _spawnerSettingsManager = context.Resolve<SpawnerSettingsManager>();
        }

        public async UniTask LoadLevelAsync(
            string            fullName,
            CancellationToken cancellationToken = default
        )
        {
            _logStopwatch.Start();
            _fullNameToLoad = fullName;

            var filename = "Levels/" + fullName;

            var levelTask    = _assetsManager.LoadAsync<TextAsset>(filename, externalToken: cancellationToken);
            var settingsTask = _assetsManager.LoadAsync<SpawnerSettingsContainer>(
                "paris/SpawnerSettingsAsset",
                externalToken: cancellationToken
            );

            var (levelWrapper, settingsWrapper) = await UniTask.WhenAll(levelTask, settingsTask);

            var textAsset    = levelWrapper?.Get();
            if (textAsset == null)
                throw new Exception($"Cannot find Level: {_fullNameToLoad}");

            _logStopwatch.StopLogRestart($"Level {_fullNameToLoad} loading done");
            _loadedLevel = LevelBinarySerializer.Deserialize(textAsset.bytes);
            _logStopwatch.StopLog($"Level {_fullNameToLoad} parsing done");

            var spawnerSettings = settingsWrapper?.Get();
            if (spawnerSettings == null)
                throw new Exception("Failed to load spawner settings");

            _spawnerSettingsManager.SetGlobalSpawners(spawnerSettings);
        }

        public ILevel GetLoadedLevel()
        {
            if (_loadedLevel == null)
                throw new InvalidOperationException("Level has not been loaded yet!");
            
            return _loadedLevel;
        }

        public void ReleaseByContext(IContext context)
        {

        }
    }
}
