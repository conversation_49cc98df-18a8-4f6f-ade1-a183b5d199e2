using System;
using System.Collections.Generic;
using BBB.Match3.Debug;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes.AdditionalHitInfos;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;
using BBB.Match3.Systems.GoalsService;
using BBB.RaceEvents;
using GameAssets.Scripts.Match3.Settings;
using GameAssets.Scripts.Match3.Systems.CreateSimulationSystems.PopSystem;
using UnityEngine;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public struct CellWithTileKinds : IEquatable<CellWithTileKinds>
    {
        public Cell Cell { get; set; }

        public TileKinds TileKinds { get; set; }

        public bool Equals(CellWithTileKinds other)
        {
            return Equals(Cell, other.Cell) && TileKinds == other.TileKinds;
        }

        public override bool Equals(object obj)
        {
            return obj is CellWithTileKinds other && Equals(other);
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(Cell, (int) TileKinds);
        }

        /// <summary>
        /// Subdivides a list of `CellWithTileKinds` into groups based on their `TileKinds` and sorts these groups by their size.
        /// Optionally, a specific `TileKinds` can be prioritized and placed at the beginning of the sorted list.
        /// While using SDB, we hit 2 colors on the board, this function sorts which color to hit first.
        /// Then they are placed in a list of keypair values, where hit can be done in staged order.
        /// </summary>
        /// <param name="coordsList">The list of `CellWithTileKinds` to be subdivided and sorted.</param>
        /// <param name="maxKind">An optional `TileKinds` value to prioritize and place at the beginning of the sorted list. Default is `TileKinds.None`.</param>
        /// <returns>A sorted list of key-value pairs where the key is a `TileKinds` and the value is a list of `CellWithTileKinds` of that kind.</returns>
        public static List<KeyValuePair<TileKinds, List<CellWithTileKinds>>> SubdivideAndSortCoordsList (List<CellWithTileKinds> coordsList, TileKinds maxKind = TileKinds.None)
        {
            var groupedByTileKinds = new Dictionary<TileKinds, List<CellWithTileKinds>>(coordsList.Count);
            foreach (var item in coordsList)
            {
                if (!groupedByTileKinds.TryGetValue(item.TileKinds, out var list))
                {
                    list = new List<CellWithTileKinds>();
                    groupedByTileKinds[item.TileKinds] = list;
                }

                list.Add(item);
            }

            var sortedList = new List<KeyValuePair<TileKinds, List<CellWithTileKinds>>>(groupedByTileKinds.Count);
            KeyValuePair<TileKinds, List<CellWithTileKinds>>? tileKindPair = null;

            foreach (var pair in groupedByTileKinds)
            {
                if (maxKind != TileKinds.None && pair.Key.Equals(maxKind))
                {
                    tileKindPair = pair;
                }
                else
                {
                    sortedList.Add(pair);
                }
            }

            sortedList.Sort((pair1, pair2) => pair2.Value.Count.CompareTo(pair1.Value.Count));

            if (tileKindPair.HasValue)
            {
                sortedList.Insert(0, tileKindPair.Value);
            }

            return sortedList;
        }
    }
    
    public static class SpecialTileSystem
    {
        private static readonly List<Coords> SquareClearerDamageArea = new (25);
        private const bool IsBombDamageSpreadThroughGaps = true;

        private static int _boostIdCounter;

        public static Queue PopRowBreaker(HashSet<Tile> immuneTiles, IRootSimulationHandler events, Grid grid,
            SimulationInputParams inputParams, GoalsSystem goalSystem, Cell cell, HitWaitParams hitWaitParams)
        {
            var tile = cell.Tile;
            var tileKind = tile.IsNull() ? TileKinds.None : tile.Kind;
            var tileSpeciality = tile.IsNull() ? TileSpeciality.None : tile.Speciality;

            var q = SpecialTileSystemLogic.PopLineBreaker(immuneTiles: immuneTiles,
                handler: events,
                grid: grid,
                coords: cell.Coords,
                cardinalDirections: CardinalDirections.E_W,
                tileSpeciality: tileSpeciality,
                hitWaitParams: hitWaitParams);

            return q;
        }

        public static Queue PopColumnBreaker(HashSet<Tile> immuneTiles, IRootSimulationHandler events, Grid grid, GoalsSystem goalSystem, Cell cell, HitWaitParams hitWaitParams)
        {
            var tile = cell.Tile;
            var tileKind = tile.IsNull() ? TileKinds.None : tile.Kind;
            var tileSpeciality = tile.IsNull() ? TileSpeciality.None : tile.Speciality;
            
            var q = SpecialTileSystemLogic.PopLineBreaker(immuneTiles: immuneTiles,
                handler: events,
                grid: grid,
                coords: cell.Coords,
                cardinalDirections: CardinalDirections.N_S,
                tileSpeciality: tileSpeciality,
                hitWaitParams: hitWaitParams);

            return q;
        }

        public static Queue PopCross(HashSet<Tile> immuneTiles, SimulationInputParams inputParams,
            IRootSimulationHandler events, Grid grid, GoalsSystem goalSystem, Cell targetCell, Cell otherCell)
        {
            var tile = targetCell.Tile;
            var tileKind = tile.Kind;
            var coords = targetCell.Coords;
            var otherTile = otherCell?.Tile;
            var tileSpeciality = tile.IsNull() ? TileSpeciality.None : tile.Speciality;
            var q = SpecialTileSystemLogic.PopLineBreaker(immuneTiles, events, grid, coords,
                CardinalDirections.N_E_S_W, tileSpeciality, null);

            q.ClearAndBusyCell(inputParams, grid, goalSystem, targetCell, events, busyTime: M3Constants.BusyTimeCross);
            q.ClearAndBusyCell(inputParams, grid, goalSystem, otherCell, events, busyTime: M3Constants.BusyTimeCross);

            return q;
        }

        public static Queue PopSmallCross(HashSet<Tile> immuneTiles, SimulationInputParams inputParams,
            IRootSimulationHandler events,
            Grid grid, GoalsSystem goalSystem, Cell cell,
            bool noRealDamage = false,
            HitWaitParams hitWaitParams = null)
        {
            var coords = cell.Coords;

            var q = new Queue(context: "SmallCross");

            Hit<Cell> hit;
            if (!noRealDamage)
            {
                hit = new Hit<Cell>(
                    immuneTiles: immuneTiles,
                    target: cell,
                    busyWait: M3Constants.BusyTimeSmallCross,
                    damageSource: DamageSource.SmallCross,
                    coords: coords);
                q.AppendHit(0, hit);
            }

            var rightCoordDelta = new Coords(1, 0);
            var allCoords = new List<Coords>();
            for (var i = 0; i < 4; i++)
            {
                var workingCoords = i >= 0 ? coords + rightCoordDelta : coords;
                if (grid.TryGetCell(workingCoords, out var workingCell))
                {
                    // Propeller Small Cross action does not need to wait for this coords if there's no tile on it. This
                    // will avoid Propeller from disappearing for a split second when a match is happening at the same 
                    // time (https://trello.com/c/ZnWRZ1Qg/11807-bug-propeller-briefly-disappears-after-activation-in-some-cases)
                    if (workingCell.HasTile()) allCoords.Add(workingCoords);
                    if (!noRealDamage)
                    {
                        hit = new Hit<Cell>(
                            immuneTiles: immuneTiles,
                            target: workingCell,
                            busyWait: M3Constants.BusyTimeSmallCross,
                            damageSource: DamageSource.SmallCross,
                            coords: workingCoords);
                        q.AppendHit(0, hit);
                    }
                }

                rightCoordDelta = rightCoordDelta.Perp();
            }

            var dirsToInclude = CardinalDirections.None;

            foreach (var dir in CardinalDirectionsHelper.GetAllStraight())
            {
                var adjacentCoord = coords.GoSingleCardinalDirection(dir);
                if (grid.TryGetCell(adjacentCoord, out _))
                {
                    dirsToInclude |= dir;
                }
            }

            events.AddAction(new ActionSmallCrossExplosion(coords, allCoords, dirsToInclude, hitWaitParams));

            return q;
        }
        
        public static Queue PopSquareClearer(IRootSimulationHandler events,
            Grid grid, SimulationInputParams inputParams, GoalsSystem goalSystem, Cell targetCell, Cell otherCell,
            float radius = 1f, HitWaitParams hitWaitParams = null)
        {
            var bombId = _boostIdCounter++;
            var bombHitSourceUid = bombId;
            
            var q = new Queue(context: "PopSquareClearer");

            var targetCoords = targetCell.Coords;
            var otherCoords = otherCell?.Coords;

            var tile = targetCell.Tile;
            var tileKind = tile.IsNull() ? TileKinds.None : tile.Kind;
            var otherTile = otherCell?.Tile;

            var bombFromBolt = hitWaitParams is { DamageSource: DamageSource.RemoveColorTiles or DamageSource.MultiBomb };
            
            var damageSource = DamageSource.Bomb;
            
            if (bombFromBolt)
            {
                var hitSourceId = hitWaitParams.HitSourceUid;
                bombHitSourceUid = hitSourceId;
                damageSource = DamageSource.MultiBomb;
            }
            
            SquareClearerDamageArea.Clear();
            SquareClearerDamageArea.Add(targetCoords);
            if (otherCoords.HasValue)
            {
                SquareClearerDamageArea.Add(otherCoords.Value);
            }

            var diagonalAllowed = true;
            if (radius is < 1f and >= 0f)
            {
                diagonalAllowed = false;
                radius = 1f;
            }

            var hashSetFilter = new HashSet<Coords>();
            for (var i = 0; i <= radius; i++)
            {
                var minX = targetCoords.X - i;
                var maxX = targetCoords.X + i;
                var minY = targetCoords.Y - i;
                var maxY = targetCoords.Y + i;
                for (var x = minX; x <= maxX; x++)
                {
                    for (var y = minY; y <= maxY; y++)
                    {
                        if (x != minX && x != maxX && y != minY && y != maxY)
                        {
                            continue;
                        }

                        if (!diagonalAllowed)
                        {
                            if ((x == minX || x == maxX) && (y == minY || y == maxY))
                            {
                                continue;
                            }
                        }

                        var newCoords = new Coords(x, y);
                        if (hashSetFilter.Contains(newCoords))
                            continue;

                        grid.TryGetCell(newCoords, out var newCell);
                        
                        if (!IsBombWaveReachable(newCell, SquareClearerDamageArea)) continue;
                        
                        if (newCell == null)
                        {
                            if (IsBombDamageSpreadThroughGaps)
                            {
                                SquareClearerDamageArea.Add(newCoords);
                            }
                        }
                        else
                        {
                            SquareClearerDamageArea.Add(newCoords);
                        }

                        if (newCell == null) continue;
                        
                        hashSetFilter.Add(newCell.Coords);

                        var hit = new Hit<Cell>(
                            immuneTiles: null,
                            target: newCell,
                            damageSource: damageSource,
                            boostInfo: new BombInfo(bombId, bombFromBolt),
                            busyWait: M3Constants.BusyTimeBomb,
                            coords: newCell.Coords,
                            hitSourceUid: bombHitSourceUid);
                        q.AppendHit(0, hit);
                    }
                }
            }
            
            events?.AddAction(new ActionBomb(bombId, targetCoords, (int)radius, tileKind, SquareClearerDamageArea, hitWaitParams));

            return q;
        }

        /// <summary>
        /// Check if bomb damage can spread to the cell.
        /// </summary>
        private static bool IsBombWaveReachable(Cell cell, List<Coords> damageArea)
        {
            foreach (var c in damageArea)
            {
                if (cell == null || c == cell.Coords)
                {
                    return true;
                }

                // Spread wave to near cells (diagonal is allowed).
                var dx = Mathf.Abs(cell.Coords.X - c.X);
                if (dx > 1)
                {
                    continue;
                }

                var dy = Mathf.Abs(cell.Coords.Y - c.Y);
                if (dy > 1)
                {
                    continue;
                }

                return true;
            }

            return false;
        }

        public static Queue PopTripleCross(HashSet<Tile> immuneTiles, SimulationInputParams inputParams,
            IRootSimulationHandler events, Grid grid, GoalsSystem goalSystem, Cell targetCell, Cell otherCell,
            TileSpeciality lineBreakerSpeciality)
        {
            var tile = targetCell.Tile;
            var tileKind = tile.Kind;
            var otherTile = otherCell?.Tile;

            var q = new Queue(context: "PopTripleCross");

            var coords = targetCell.Coords;

            // Make square busy:
            var coordsAround = new HashSet<Coords>();

            foreach (var coord in coords.GetCoordsAround())
            {
                coordsAround.Add(coord);
            }
            
            coordsAround.Add(coords);

            foreach (var coordsToForceBusy in coordsAround)
            {
                if (!grid.TryGetCell(coordsToForceBusy, out var cellToForceBusy)) continue;

                if (!cellToForceBusy.HasTile())
                    continue;

                cellToForceBusy.IsBusy = M3Constants.BusyTimeTripleCross;
                q.AddBusyCell(cellToForceBusy);
            }

            q.ClearAndBusyCell(inputParams, grid, goalSystem, targetCell, events);
            q.ClearAndBusyCell(inputParams, grid, goalSystem, otherCell, events);

            events?.AddAction(new ActionTripleCross(coords, lineBreakerSpeciality));

            q.CreateTripleLineBreaker(immuneTiles, inputParams, events, grid, goalSystem, coords,
                CardinalDirections.E_W, TileSpeciality.Bomb);
            q.CreateTripleLineBreaker(immuneTiles, inputParams, events, grid, goalSystem, coords,
                CardinalDirections.N_S, TileSpeciality.Bomb);

            return q;
        }

        private static void CreateTripleLineBreaker(this Queue q, HashSet<Tile> immuneTiles,
            SimulationInputParams inputParams, IRootSimulationHandler events, Grid grid, 
            GoalsSystem goalSystem, Coords coords, CardinalDirections direction,
            TileSpeciality tileSpeciality)
        {
            Coords firstSideCoords;
            Coords secondSideCoords;
            switch (direction)
            {
                case CardinalDirections.E_W:
                    firstSideCoords = coords.GoSingleCardinalDirection(CardinalDirections.N);
                    secondSideCoords = coords.GoSingleCardinalDirection(CardinalDirections.S);
                    break;

                case CardinalDirections.N_S:
                    firstSideCoords = coords.GoSingleCardinalDirection(CardinalDirections.W);
                    secondSideCoords = coords.GoSingleCardinalDirection(CardinalDirections.E);
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(direction), direction, null);
            }


            var cell = grid.GetCell(coords);
            q.ClearAndBusyCell(inputParams, grid, goalSystem, cell, events);

            // Register hit for Triple LB origin
            var hit = new Hit<Cell>(
                immuneTiles: immuneTiles,
                target: cell,
                busyWait: M3Constants.BusyTimeLineBreaker,
                damageSource: DamageSource.BoostClear,
                coords: cell.Coords);
            q.AppendHit(0, hit);

            q.Append(SpecialTileSystemLogic.PopLineBreaker(immuneTiles, events, grid, coords, direction,
                tileSpeciality, hitWaitParams: null));
            q.Append(SpecialTileSystemLogic.PopLineBreaker(immuneTiles, events, grid, firstSideCoords, direction,
                tileSpeciality, hitWaitParams: null));
            q.Append(SpecialTileSystemLogic.PopLineBreaker(immuneTiles, events, grid, secondSideCoords, direction,
                tileSpeciality, hitWaitParams: null));
        }
        
        // Where firstCell and secondCell are cells to destroy:
        public static Queue WhirlpoolAllTiles(IRootSimulationHandler events, SimulationInputParams inputParams, Grid grid,
            GoalsSystem goalSystem, SettleTilesSystem settleTilesSystem, Cell firstCell, Cell secondCell)
        {
            var q = new Queue(context: "WhirlpoolAllTiles");
            
            var whirlpoolIds = new List<int>();
            
            var numberOfWaves = Mathf.CeilToInt(inputParams.Settings.SuperDiscoBall);
            
            if (numberOfWaves > 1)
            {
                for (var i = 0; i < numberOfWaves; i++)
                {
                    var id = _boostIdCounter++;
                    whirlpoolIds.Add(id);
                }
            }
            else
            {
                var id = _boostIdCounter++;
                whirlpoolIds.Add(id);
            }
            
            if (events != null)
            {
                if (firstCell == null)
                {
#if BBB_LOG
                    M3Debug.LogError("[SpecialTileSystem]".Paint() + " Whirlpool cell is Null");
#endif
                }
                else
                {
                    var coords = ApplyWhirlpoolToGrid(whirlpoolIds, firstCell.Coords, secondCell.Coords, grid, settleTilesSystem, q);
                    events.AddAction(new ActionWhirlpool(whirlpoolIds, coords, firstCoords: firstCell.Coords, secondCoords: secondCell.Coords));
                    
                    if (goalSystem.RaceEventMatch3Manager != null && goalSystem.RaceEventMatch3Manager.IsAnyRaceEventOfType(RaceEventTypes.RaceEventType.Collect, out _))
                    {
                        var score = Mathf.Max(1, inputParams.Settings.DiscoRushSimpleTileMatch * coords.Count * whirlpoolIds.Count);
                        events.AddAction(new ActionSpawnDiscoRushCollect(firstCell.Coords, 2, score, secondCell.Coords));
                        goalSystem.RaceEventMatch3Manager.AddScore(score);
                    }
                }
            }
            
            var waveIndex = numberOfWaves - 1;
            HitDiscoBall(firstCell);
            HitDiscoBall(secondCell);

            return q;

            void HitDiscoBall(Cell cell)
            {
                if (cell == null) return;

                var hit = new Hit<Cell>(
                    immuneTiles: null,
                    target: cell,
                    busyWait: M3Constants.BusyTimeWhirlpool,
                    damageSource: DamageSource.Whirlpool,
                    boostInfo: new WhirlpoolInfo(whirlpoolIds[0], waveIndex),
                    sourceKind: TileKinds.None,
                    coords: cell.Coords);

                q.ClearAndBusyCell(inputParams, grid, goalSystem, cell, events,
                    hit.GetHitParams(), busyTime: M3Constants.BusyTimeWhirlpool, true);

                for (var i = 0; i < waveIndex; i++)
                {
                    q.AppendHit(0, hit);
                }
            }
        }

        private static List<Coords> ApplyWhirlpoolToGrid(List<int> whirlpoolIds, Coords centerCoords, Coords? secondCellCoords,
            Grid grid, SettleTilesSystem settleTileSystem, Queue q)
        {
            var coordsList = new List<Coords>();
            
            foreach (var c in grid.Cells)
            {
                var cellCoords = c.Coords;
                // Do not hit cells where the Bolts are, since that was already taken care by calling method
                // ClearAndBusyCell on WhirlpoolAllTiles
                if (cellCoords.Equals(centerCoords) || (secondCellCoords != null && cellCoords.Equals(secondCellCoords.Value)))
                    continue;
                
                for (var i = 0; i < whirlpoolIds.Count; i++)
                {
                    var hit = new Hit<Cell>(
                        immuneTiles: null,
                        target: c,
                        busyWait: M3Constants.BusyTimeWhirlpool,
                        damageSource: DamageSource.Whirlpool,
                        boostInfo: new WhirlpoolInfo(whirlpoolIds[i], i),
                        coords: cellCoords,
                        hitSourceUid: whirlpoolIds[i]);

                    var lockId = Guid.NewGuid().GetHashCode();
                    settleTileSystem.AddFreezeLock(lockId);
                    q.AddPreHitAction(0, new RemoveFreezeLockPreHitAction(lockId));
                    q.AppendHit(0, hit);
                }

                coordsList.Add(c.Coords);
            }

            return coordsList;
        }

        public static Queue AlmostEmptyRemoveAllTiles(Grid grid)
        {
            var q = new Queue("AlmostEmptyRemoveAllTiles");

            foreach (var cell in grid.Cells)
            {
                if (cell.HasMultiSizeCellReferenceWithCellOverlay()) continue;
                var tile = cell.Tile;
                
                if (tile.IsNull()) continue;

                if (!cell.Tile.CanBeDamagedBy(DamageSource.Whirlpool)) continue;

                var hit = new Hit<Cell>(
                    immuneTiles: null,
                    target: cell,
                    busyWait: 1,
                    damageSource: DamageSource.Bomb,
                    coords: cell.Coords);

                q.AppendHit(0, hit);
            }

            return q;
        }

        public static Queue RemoveAllColorTiles(HashSet<Tile> immuneTiles,
            SimulationInputParams inputParams, IRootSimulationHandler handler, Grid grid,
            GoalsSystem goalSystem, SettleTilesSystem settleTilesSystem,
            Cell colorBombCell, List<TileKinds> tileKindsList)
        {
            var cellsWithTilesToDestroy = new List<CellWithTileKinds>();

            var superDiscoBallValue = inputParams.Settings.SuperDiscoBall;
            var numberOfColorsToDamage = Mathf.CeilToInt(superDiscoBallValue);
            var maxDamageKinds = Mathf.Min(numberOfColorsToDamage, tileKindsList.Count);

            if (maxDamageKinds < tileKindsList.Count)
            {
                tileKindsList.RemoveRange(maxDamageKinds, tileKindsList.Count - maxDamageKinds);
            }

            foreach (var cell in grid.Cells)
            {
                if (cell.IsTileKind(tileKindsList) &&
                    cell.IsColorBombTargetable() &&
                    (immuneTiles == null || !immuneTiles.Contains(cell.Tile)))
                {
                    cellsWithTilesToDestroy.Add(new CellWithTileKinds {Cell = cell, TileKinds = cell.Tile.Kind});
                }
            }

            // If the value of superDiscoBall is between 0 and 1, it represents a normal DiscoBall
            // and indicates the percentage of tiles of a SINGLE color to be destroyed.

            if (superDiscoBallValue is > 0 and < 1)
            {
                var cellsToRemoveCount = (int) (cellsWithTilesToDestroy.Count * superDiscoBallValue);
                if (cellsToRemoveCount > 0 && cellsToRemoveCount <= cellsWithTilesToDestroy.Count)
                {
                    cellsWithTilesToDestroy.RemoveRange(cellsWithTilesToDestroy.Count - cellsToRemoveCount, cellsToRemoveCount);
                }
            }

            var maxKind = tileKindsList.Count != 0 && tileKindsList[0] != TileKinds.None
                ? tileKindsList[0]
                : inputParams.UsedKinds.DeterministicRandomInSelf();

            var settings = inputParams.Settings.GetSettingsForEffect<BoltBoosterComboAnimSettings>(FxType.BoltRegularTileCombine);

            var countOfTilesToDestroy = cellsWithTilesToDestroy.Count;

            var lightningDuration = settings.MinDuration;
            var totalDuration = lightningDuration + (tileKindsList.Count - 1) * inputParams.Settings.RegularTilesStageDelayWhileUsingSuperDiscoBall;

            handler.AddAction(new ActionCombineBoltRegularTile(colorBombCell.Coords, totalDuration));
            var q = new Queue(context: "RemoveAllColorTiles with '" + maxKind + "' max tile kind");

            foreach (var direction in CardinalDirections.N_E_S_W.SplitIntoSingleDirections())
            {
                var adjacentCell = grid.GetCell(colorBombCell.Coords.GoSingleCardinalDirection(direction));
                if (adjacentCell != null)
                {
                    var adjacentHit = new Hit<Cell>(
                        immuneTiles: null,
                        target: adjacentCell,
                        busyWait: M3Constants.BusyTimeRemoveAllColorTiles,
                        damageSource: DamageSource.AdjacentFromBoost,
                        sourceKind: maxKind,
                        coords: adjacentCell.Coords);
                    q.AppendHit(0, adjacentHit);
                }
            }

            q.ClearAndBusyCell(inputParams, grid, goalSystem, colorBombCell, handler, busyTime: M3Constants.BusyTimeRemoveAllColorTiles);

            if (goalSystem.RaceEventMatch3Manager != null && goalSystem.RaceEventMatch3Manager.IsAnyRaceEventOfType(RaceEventTypes.RaceEventType.Collect, out _))
            {
                var score = Mathf.Max(1, inputParams.Settings.DiscoRushSimpleTileMatch * countOfTilesToDestroy);
                handler.AddAction(new ActionSpawnDiscoRushCollect(colorBombCell.Coords, totalDuration, score));
                goalSystem.RaceEventMatch3Manager.AddScore(score);
            }

            var boltId = _boostIdCounter++;

            // Add hit for disco ball cell
            var hit = new Hit<Cell>(
                immuneTiles: null,
                target: colorBombCell,
                busyWait: M3Constants.BusyTimeRemoveAllColorTiles,
                damageSource: DamageSource.RemoveColorTiles,
                boostInfo: new BoltInfo(boltId, false),
                sourceKind: maxKind,
                coords: colorBombCell.Coords,
                hitSourceUid: boltId);
            var lockId = Guid.NewGuid().GetHashCode();
            settleTilesSystem.AddFreezeLock(lockId);
            q.AddPreHitAction(0, new RemoveFreezeLockPreHitAction(lockId));
            q.AppendHit(0, hit);

            var adjacentHitsForColorBomb = hit.GetAdjacentHits(colorBombCell.Tile, grid, DamageSource.RemoveColorTiles, CardinalDirections.N_E_S_W, boltId);
            if (adjacentHitsForColorBomb != null)
            {
                foreach (var adjacentHit in adjacentHitsForColorBomb)
                {
                    q.AppendHit(0, adjacentHit);
                }
            }

            var visualActionCoordsList = new List<CellWithTileKinds>(countOfTilesToDestroy);

            for (var i = 0; i < countOfTilesToDestroy; i++)
            {
                var item = cellsWithTilesToDestroy.DeterministicRandomInSelf();
                visualActionCoordsList.Add(item);

                var cell = item.Cell;
                var tileKind = item.TileKinds;

                hit = new Hit<Cell>(
                    immuneTiles: null,
                    target: cell,
                    busyWait: M3Constants.BusyTimeRemoveAllColorTiles,
                    damageSource: DamageSource.RemoveColorTiles,
                    boostInfo: new BoltInfo(boltId, false),
                    sourceKind: tileKind,
                    coords: cell.Coords,
                    hitSourceUid: boltId);
                lockId = Guid.NewGuid().GetHashCode();
                settleTilesSystem.AddFreezeLock(lockId);
                q.AddPreHitAction(0, new RemoveFreezeLockPreHitAction(lockId));
                q.AppendHit(0, hit);

                cellsWithTilesToDestroy.Remove(item);

                var adjacentHits = hit.GetAdjacentHits(cell.Tile, grid, DamageSource.RemoveColorTiles,
                    CardinalDirections.N_E_S_W, boltId);
                if (adjacentHits != null)
                {
                    foreach (var adjacentHit in adjacentHits)
                    {
                        q.AppendHit(0, adjacentHit);
                    }
                }
            }

            handler.AddAction(new ActionSpawnLightningBoltsChain(boltId, maxKind, colorBombCell, visualActionCoordsList, settings.LightningsWidth, lightningDuration, totalDuration));

            return q;
        }

        public static Queue ChangeAllColorTilesIntoAndHit(HashSet<Tile> immuneTiles, IRootSimulationHandler handler,
            Grid grid, SimulationInputParams inputParams, GoalsSystem goalSystem, SettleTilesSystem settleTileSystem,
            Cell colorBombCell, Cell specialTileCell, List<TileKinds> tileKindsList)
        {
            var queue = new Queue(context: "ChangeAllColorTilesInto");
            var cellWithTileKinds = new List<CellWithTileKinds>();
            var superDiscoBallValue = inputParams.Settings.SuperDiscoBall;
            var numberOfColorsToDamage = Mathf.CeilToInt(superDiscoBallValue);
            var maxDamageKinds = Mathf.Min(numberOfColorsToDamage, tileKindsList.Count);

            if (maxDamageKinds < tileKindsList.Count)
            {
                tileKindsList.RemoveRange(maxDamageKinds, tileKindsList.Count - maxDamageKinds);
            }

            var maxKind = tileKindsList.Count != 0 && tileKindsList[0] != TileKinds.None
                ? tileKindsList[0]
                : inputParams.UsedKinds.DeterministicRandomInSelf();

            var colorBombCoords = colorBombCell.Coords;
            var specialTileCoords = specialTileCell.Coords;
            var specialTile = specialTileCell.Tile;
            var tileSpeciality = specialTile.Speciality;
            var boltId = _boostIdCounter++;

            foreach (var cell in grid.Cells)
            {
                if (specialTileCell == cell) continue;
                if (cell.Tile.IsNull()) continue;
                if (!cell.IsTileKind(tileKindsList)) continue;
                if (!cell.IsColorBombTargetable()) continue;
                if (cell.Tile.HasMultipleArmorLayer()) continue;
                cellWithTileKinds.Add(new CellWithTileKinds
                {
                    Cell = grid.GetCell(cell.Coords),
                    TileKinds = grid.GetCell(cell.Coords).Tile.Kind
                });
            }

            cellWithTileKinds.Sort(Comparison);

            var settings = inputParams.Settings.GetSettingsForEffect<BoltBoosterComboAnimSettings>(FxType.BoltBoosterCombine);

            handler.AddAction(new ActionPlayTileAnim(specialTileCoords, TileLayerViewAnims.FormationByBoltIntro));
            handler.AddAction(new ActionPowerUpVoiceover());

            var characterAnimAction = tileSpeciality == TileSpeciality.Bomb
                ? (Match3ActionBase) new ActionBoltBombCombineCharacterAnim()
                : new ActionBoltLinebreakerCombineCharacterAnim();
            handler.AddAction(characterAnimAction);

            var lightningDuration = settings.MinDuration;
            var totalDuration = lightningDuration + (tileKindsList.Count - 1) * inputParams.Settings.RegularTilesStageDelayWhileUsingSuperDiscoBall;

            if (goalSystem.RaceEventMatch3Manager != null && goalSystem.RaceEventMatch3Manager.IsAnyRaceEventOfType(RaceEventTypes.RaceEventType.Collect, out _))
            {
                var score = Mathf.Max(1, inputParams.Settings.DiscoRushSpecialTileMatch * cellWithTileKinds.Count);
                handler.AddAction(new ActionSpawnDiscoRushCollect(colorBombCoords, totalDuration, score));
                goalSystem.RaceEventMatch3Manager.AddScore(score);
            }

            handler.AddAction(new ActionSpawnBoltBoosterCombo(colorBombCoords, specialTileCoords, maxKind, totalDuration));
            handler.AddAction(new ActionSpawnLightningBoltsFromPoint(boltId, colorBombCoords,
                cellWithTileKinds, lightningDuration, totalDuration, settings.LightningsWidth, tileSpeciality));

            queue.ClearAndBusyCell(inputParams, grid, goalSystem, colorBombCell,
                handler, busyTime: M3Constants.BusyTimeChangeAllColorTilesInto);

            // Add hit for disco ball cell
            var hit = new Hit<Cell>(
                immuneTiles: null,
                target: colorBombCell,
                busyWait: M3Constants.BusyTime,
                damageSource: DamageSource.RemoveColorTiles,
                boostInfo: new BoltInfo(boltId, true),
                coords: colorBombCoords);
            var lockId = Guid.NewGuid().GetHashCode();
            settleTileSystem.AddFreezeLock(lockId);
            queue.AddPreHitAction(0, new RemoveFreezeLockPreHitAction(lockId));
            queue.AppendHit(0, hit);

            if (immuneTiles == null || !immuneTiles.Contains(specialTileCell.Tile))
            {
                lockId = Guid.NewGuid().GetHashCode();
                settleTileSystem.AddFreezeLock(lockId);

                hit = new Hit<Cell>(
                    immuneTiles: null,
                    target: specialTileCell,
                    busyWait: M3Constants.BusyTime,
                    damageSource: DamageSource.RemoveColorTiles,
                    boostInfo: new BoltInfo(boltId, true),
                    coords: specialTileCell.Coords,
                    hitSourceUid: boltId);
                queue.AddPreHitAction(0, new RemoveFreezeLockPreHitAction(lockId));
                queue.AppendHit(0, hit);
            }

            foreach (var item in cellWithTileKinds)
            {
                var cell = item.Cell;
                var tile = cell.Tile;

                if (IsTileConvertableToBooster(tile))
                {
                    queue.ClearAndBusyCell(inputParams, grid, goalSystem, cell, handler);

                    TileSpeciality newTileSpeciality;
                    if (tileSpeciality is TileSpeciality.RowBreaker or TileSpeciality.ColumnBreaker)
                    {
                        newTileSpeciality = RandomSystem.Next(2) == 0
                            ? TileSpeciality.RowBreaker
                            : TileSpeciality.ColumnBreaker;
                    }
                    else
                    {
                        newTileSpeciality = tileSpeciality;
                    }

                    var tileAsset = newTileSpeciality switch
                    {
                        TileSpeciality.Bomb => TileAsset.Bomb,
                        TileSpeciality.Propeller => TileAsset.Propeller,
                        TileSpeciality.RowBreaker => TileAsset.RowBreaker,
                        _ => TileAsset.ColumnBreaker
                    };

                    var newTile = TileFactory.CreateTile(grid.TilesSpawnedCount++, tileAsset,
                        new TileOrigin(Creator.ColorBombCombo, cell));

                    hit = new Hit<Cell>(
                        immuneTiles: null,
                        target: cell,
                        busyWait: M3Constants.BusyTime,
                        damageSource: DamageSource.RemoveColorTiles,
                        boostInfo: new BoltInfo(boltId, true),
                        coords: cell.Coords,
                        hitSourceUid: boltId);

                    cell.AddTile(newTile);
                    handler.AddAction(new ActionAddTile(cell.Coords, newTile));
                    handler.AddAction(new ActionPlayTileAnim(cell.Coords, TileLayerViewAnims.FormationByBoltIntro, inputParams.Settings.FormationByBoltIntroTime));

                    lockId = Guid.NewGuid().GetHashCode();
                    settleTileSystem.AddFreezeLock(lockId);
                    queue.AddPreHitAction(0, new RemoveFreezeLockPreHitAction(lockId));
                    queue.AppendHit(0, hit);
                }
                else
                {
                    hit = new Hit<Cell>(
                        immuneTiles: null,
                        target: cell,
                        busyWait: M3Constants.BusyTime,
                        damageSource: DamageSource.RemoveColorTiles,
                        boostInfo: new BoltInfo(boltId, true),
                        coords: cell.Coords);
                    lockId = Guid.NewGuid().GetHashCode();
                    settleTileSystem.AddFreezeLock(lockId);
                    queue.AddPreHitAction(0, new RemoveFreezeLockPreHitAction(lockId));
                    queue.AppendHit(0, hit);
                }
            }

            // This action here serves as a waiting checkpoint, so that all previous actions can be finished before
            // other actions can be executed. We need this to be able to sync explosion/activation of the boosters
            // created by disco ball
            handler.AddAction(new ActionEndTurnCheckpoint());

            return queue;

            int Comparison(CellWithTileKinds a, CellWithTileKinds b)
            {
                var distA = (a.Cell.Coords - colorBombCoords).SqrMagnitude;
                var distB = (b.Cell.Coords - colorBombCoords).SqrMagnitude;
                return Math.Sign(distA - distB);
            }
        }

        private static bool IsTileConvertableToBooster(Tile tile)
        {
            return tile.Speciality == TileSpeciality.None;
        }

        public static Queue RemoveWithSkunkHit(Tile tile, Hit hit, Cell cell, HitWaitParams hitWaitParams,
            SettleTilesSystem settleTilesSystem, IRootSimulationHandler handler)
        {
            var q = new Queue(context: "SkunkHit");
            var skunkHitId = _boostIdCounter++;

            var skunkHit = new Hit<Cell>(
                hit.ImmuneTiles,
                null,
                hit.BusyWait,
                DamageSource.Skunk,
                coords: cell.Coords,
                boostInfo: new SkunkHitInfo(skunkHitId));

            var hitInfo = (SkunkHitInfo)skunkHit.BoostInfo;

            q.AppendHit(1, skunkHit);
            handler.AddAction(new ActionPlaySkunkHit(hitInfo.SkunkHitId, cell.Coords, hitWaitParams, tile.Id));
            return q;
        }
        
        public static Queue RemoveWithFireWorks(Cell cell, M3SpawnSystem spawnSystem, SimulationInputParams inputParams, SettleTilesSystem settleTilesSystem,
            IRootSimulationHandler handler, HitWaitParams hitWaitParams)
        {
            var q = new Queue(context: "FireWorksHit");
            var tileId = cell.HasTile() ? cell.Tile.Id : -1;
            var settings = inputParams.Settings.GetSettingsForEffect<FireWorksEffectSettings>(FxType.FireWorksFlight);
            
            for (var i = 0; i < settings.FireWorksNumberOfRockets ; i++)
            {
                var removeRandomTileQueue = RemoveWithFireWorksAttack(i,
                    handler, settleTilesSystem, spawnSystem, tileId, cell.Coords, hitWaitParams);

                if (removeRandomTileQueue != null)
                {
                    q.Append(removeRandomTileQueue);
                }
            }
            return q;
        }

        private static Queue RemoveWithFireWorksAttack(int fireWorkIndex,
            IRootSimulationHandler handler, SettleTilesSystem settleTilesSystem, M3SpawnSystem spawnSystem,
            int sourceTileId, Coords sourceCoords, HitWaitParams hitWaitParams)
        {
            var q = new Queue();

            var fireWorksId = _boostIdCounter++;

            // FireWorks will select its target when processing the hit. For that reason, target is set to null. Also,
            // we are using Hit coords parameter to pass the sourceCoords instead of the expected target coords (since,
            // as mentioned before, the target will be selected at a later time and for that, we need the source coords)
            var hit = new Hit<Cell>(
                immuneTiles: null,
                target: null,
                busyWait: M3Constants.BusyTimeRemoveAllColorTiles,
                damageSource: DamageSource.FireWorks,
                boostInfo: new FireWorksInfo(fireWorksId),
                coords: sourceCoords);

            q.AppendHit(1, hit);
            handler.AddAction(new ActionFireWorksFlight(fireWorkIndex, sourceTileId, sourceCoords, fireWorksId, hitWaitParams));
            return q;
        }

        public static Queue RemoveWithPropellerAttack(HashSet<Tile> immuneTiles,
            IRootSimulationHandler handler, SettleTilesSystem settleTilesSystem, M3SpawnSystem spawnSystem,
            int sourceTileId, Coords sourceCoords, HitWaitParams hitWaitParams, int propellerComboIndex = -1)
        {
            var q = new Queue(context: "SmallCross");

            var propellerId = _boostIdCounter++;

            // Propeller will select its target when processing the hit. For that reason, target is set to null. Also,
            // we are using Hit coords parameter to pass the sourceCoords instead of the expected target coords (since,
            // as mentioned before, the target will be selected at a later time and for that, we need the source coords)
            var hit = new Hit<Cell>(
                immuneTiles: immuneTiles,
                target: null,
                busyWait: M3Constants.BusyTimeRemoveAllColorTiles,
                damageSource: DamageSource.Propeller,
                boostInfo: new PropellerInfo(propellerId),
                coords: sourceCoords);
            
            q.AppendHit(1, hit);
            handler.AddAction(new ActionPropellerFlight(sourceTileId, sourceCoords, propellerId, hitWaitParams,
                propellerComboIndex));
            return q;
        }

        public static Queue AttackWithPropellerCombo(
            IRootSimulationHandler handler,
            SettleTilesSystem settleTilesSystem,
            Coords firstCellCoords, Coords secondCellCoords,
            TileSpeciality speciality)
        {
            var propellerId = _boostIdCounter++;

            var tileAsset = TileAsset.Undefined;
            var additionalEffect = AdditionalEffect.None;
            switch (speciality)
            {
                case TileSpeciality.Bomb:
                    additionalEffect = AdditionalEffect.SquareClearer;
                    tileAsset = TileAsset.Bomb;
                    break;
                case TileSpeciality.RowBreaker:
                    additionalEffect = AdditionalEffect.RowClearer;
                    tileAsset = TileAsset.RowBreaker;
                    break;
                case TileSpeciality.ColumnBreaker:
                    additionalEffect = AdditionalEffect.ColumnClearer;
                    tileAsset = TileAsset.ColumnBreaker;
                    break;
                default:
                    M3Debug.LogError(
                        $"ChangeSingleTileInto: Can not do this for speciality {speciality}");
                    break;
            }

            handler.AddAction(new ActionPropellerComboFlight(propellerId,
                firstCellCoords, secondCellCoords, speciality));

            var result = new Queue("PropellerCombo");

            var hit = new Hit<Cell>(
                immuneTiles: null,
                target: null,
                busyWait: M3Constants.BusyTime,
                damageSource: DamageSource.PropellerCombo,
                boostInfo: new PropellerInfo(propellerId, tileAsset, speciality),
                additionalEffect: additionalEffect,
                coords: firstCellCoords);

            result.AppendHit(1, hit);

            return result;
        }
    }
}