using BBB.Match3.Debug;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionVisualizeTileDestroy : Match3ActionBase
    {
        private readonly Coords _target;
        private readonly HitWaitParams _hitWaitParams;
        private readonly int _tileId;

        public ActionVisualizeTileDestroy(Coords target, Tile tile, 
            HitWaitParams hitWaitParams)
        {
            if (tile.IsNull())
            {
#if BBB_LOG
                M3Debug.LogError("[LA]: RemoveTile. Tile is null!!!");
#endif
                return;
            }

            _target = target;
            _hitWaitParams = hitWaitParams;
            _tileId = tile.Id;
            WaitCondition = ActionWaitConditionFactory.Create(hitWaitParams, _tileId, _target);
            
            AffectedCoords.Add(_target);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ReleasedCoords.Add(_target);
            
            var targetCell = grid.GetCell(_target);
            var tileToRemove = targetCell.Tile;
            if (SimulationPlayUtils.CheckTileForRemoval(tileToRemove, _target))
                return;
        
            if (!proxy.GoalsSystem.IsTileShouldSkipDestroyAnim(_tileId))
            {
                var view = proxy.TileController.GetTileViewByCoord(targetCell.Coords, false);
                if (view != null)
                {

                    var animParams = _hitWaitParams?.DamageSource.ToAnimParams() ?? TileLayerViewAnimParams.None;
                    var preDestroyTweener = tileToRemove.VisualizeTileDestroy(proxy, view, () => OnMoveDone(view));
                    proxy.TileController.VisualizeTileDestruction(targetCell, animParams, preDestroyTweener);
                }
            }
    
            void OnMoveDone(TileView tileView)
            {
                tileView.Hide();
            }
        }

        protected override string GetMembersString()
        {
            return $"tileId={_tileId} coords={_target}";
        }
    }
}
