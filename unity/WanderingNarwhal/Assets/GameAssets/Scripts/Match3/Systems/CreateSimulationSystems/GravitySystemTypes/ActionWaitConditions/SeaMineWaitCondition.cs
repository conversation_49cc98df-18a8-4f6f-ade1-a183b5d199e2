using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes.AdditionalHitInfos;

namespace BBB.Match3
{
    public class SeaMineWaitCondition : WaitConditionBase
    {
        private readonly SeaMineInfo _seaMineInfo;
        private readonly string _textData;

        public SeaMineWaitCondition(HitWaitParams hitWaitParams) : base(hitWaitParams)
        {
            _seaMineInfo = hitWaitParams.SeaMineInfo;
            _textData = $"[ coords={hitWaitParams.Coords} ]";
        }
        
        public override bool WaitForExpectedState(Grid grid, PlaySimulationActionProxy proxy)
        {
            return !proxy.TileTickPlayer.BoardObjectFactory.WasDamageSourceSpawned(TileAsset.SeaMine, _seaMineInfo.SeaMineId);
        }
        
        public override string GetTextData(Grid grid, PlaySimulationActionProxy proxy)
        {
            return _textData;
        }
    }
}