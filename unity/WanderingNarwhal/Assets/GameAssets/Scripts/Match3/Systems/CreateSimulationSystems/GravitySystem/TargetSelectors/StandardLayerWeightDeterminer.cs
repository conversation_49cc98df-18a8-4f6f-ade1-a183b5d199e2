using System.Collections.Concurrent;
using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Match3.Debug;
using BBB.Match3.Renderer;
using BBB.Match3.Systems.GoalsService;
using GameAssets.Scripts.Match3.Logic;
using GameAssets.Scripts.Match3.Settings;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public struct TntGoal
    {
        public TntTargetType TargetType;
        public TileKinds TntKind;
    }
    
    public class StandardLayerWeightDeterminer : ILayerWeightsDeterminer
    {
        private static readonly ConcurrentDictionary<string, bool> LoggedMissingLayers = new();

        private Grid _grid;
        private GoalsSystem _goalsSystem;
        private MechanicTargetingSettings _mechanicTargetingSettings;
        private Dictionary<TntGoal, int> _tntGoalCache;
        private Dictionary<TileKinds, int> _tuktukGoalCache;
        private readonly Dictionary<Cell, float> _weightsCache = new ();
        
        private const string Ivy = "Ivy";

        public void Init(Grid grid, GoalsSystem goalsSystem, MechanicTargetingSettings mechanicTargetingSettings)
        {
            _grid = grid;
            _goalsSystem = goalsSystem;
            _mechanicTargetingSettings = mechanicTargetingSettings;
            _tntGoalCache = null;
            _tuktukGoalCache = null;
        }

        public void InvalidateCache()
        {
            _weightsCache.Clear();
        }

        public void UnInit()
        {
            _grid = null;
            _goalsSystem = null;
            _mechanicTargetingSettings = null;
            _tntGoalCache = null;
            _tuktukGoalCache = null;
            _weightsCache.Clear();
        }
        
        public float DetermineWeights(Cell cell, List<float> removableWeights = null)
        {
            if (_weightsCache.TryGetValue(cell, out var result))
                return result;
            
            if (_tntGoalCache == null)
            {
                _tntGoalCache = new Dictionary<TntGoal, int>();
                _tuktukGoalCache = new Dictionary<TileKinds, int>();
                
                // Cache TNT cells
                foreach (var tempCell in _grid.Cells)
                {
                    if (tempCell.IsAnyOf(CellState.Tnt))
                    {
                        var key = new TntGoal
                        {
                            TargetType = tempCell.TntTarget,
                            TntKind = tempCell.TntKind
                        };
                        _tntGoalCache.TryAdd(key, 0);

                        _tntGoalCache[key] += tempCell.TntCount;
                    }
                    
                    if (tempCell.HasTile() && tempCell.Tile.Speciality == TileSpeciality.TukTuk)
                    {
                        var color = (TileKinds) tempCell.Tile.GetParam(TileParamEnum.TukTukColor);
                        var count = tempCell.Tile.GetParam(TileParamEnum.TukTukCount);

                        _tuktukGoalCache.TryAdd(color, 0);
                        _tuktukGoalCache[color] += count;
                    }
                }
            }
            
            if (_mechanicTargetingSettings == null)
            {
                M3Debug.LogError($"Propeller not found");
                return 0f;
            }

            var weightTable = _mechanicTargetingSettings.TargetingWeightTable;
            if (weightTable != null)
            {
                foreach (var generalizedLayer in cell.GetGeneralizedLayers())
                {
                    if (generalizedLayer.IsNullOrEmpty())
                    {
                        M3Debug.LogError("Generalized layer is null or empty");
                        continue;
                    }
                    
                    if (weightTable.TryGetValue(generalizedLayer, out var weight))
                    {
                        result += weight;
                        removableWeights?.Add(weight);
                    }
                    else
                    {
                        if (LoggedMissingLayers.TryAdd(generalizedLayer, true))
                        {
                            M3Debug.LogError($"{generalizedLayer} not found in weight table");
                        }
                    }
                }

                if (cell.IsAnyOf(CellState.Ivy))
                {
                    if (weightTable.TryGetValue(Ivy, out var weight))
                    {
                        result = weight;
                        removableWeights?.Clear();
                        removableWeights?.Add(weight);
                    }
                }
            }

            var goalTypeRelatedWeights = _mechanicTargetingSettings.GoalTypeRelatedWeights;
            if (goalTypeRelatedWeights != null)
            {
                foreach (var goalType in _goalsSystem.OriginalGoalTypes)
                {
                    var leftCount = _goalsSystem.GetLeftGoalCount(goalType);
                    if (leftCount > 0)
                    {
                        if (GoalState.IsCellContainsGridBasedGoalRelatedItem(cell, goalType))
                        {
                            if(_mechanicTargetingSettings.GoalTypeRelatedWeights.TryGetValue(goalType, out float weight))
                            {
                                result += weight;
                            }
                        }
                    }
                }
            }

            var coords = cell.Coords;

            var x = coords.X;
            var height = 0;
            var dropItemFound = false;
            var y = coords.Y;
            var loopCounter = 0;
            while(y < _grid.Height)
            {
                if (loopCounter > 100)
                    break;
                
                loopCounter++;
                
                var workingCoords = new Coords(x,y);
                
                if (_grid.TryGetCell(workingCoords, out var workingCell))
                {
                    if( workingCell.HasTile() &&
                        workingCell.Tile.Speciality == TileSpeciality.DropItem)
                    {
                        dropItemFound = true;
                        break;
                    }
                }

                y++;
                height++;
            }

            if (dropItemFound && height > 0)
            {
                var dropItemWeight = _mechanicTargetingSettings.WeightUnderDropItem -
                                     height * _mechanicTargetingSettings.DropItemWeightDiminishStep;

                result += dropItemWeight;
            }

            if (cell.HasTile())
            {
                foreach (var tntGoal in _tntGoalCache)
                {
                    if (cell.Tile.MatchesTntTarget(tntGoal.Key.TargetType, tntGoal.Key.TntKind))
                    {
                        result += _mechanicTargetingSettings.TntGoalWeight;
                        break;
                    }
                }

                foreach (var tuktukGoal in _tuktukGoalCache)
                {
                    if(cell.Tile.MatchesTukTukTarget(tuktukGoal.Key))
                    {
                        result += _mechanicTargetingSettings.TukTukGoalWeight;
                        break;
                    }
                }

                if (cell.Tile.Speciality == TileSpeciality.Frame && !cell.Tile.IsAnyOf(TileState.AnimalMod))
                {
                    var adjacentCells = _grid.GetAdjacentCells(cell.Coords);
                    foreach (var cellTemp in adjacentCells)
                    {
                        if (cellTemp.HasTile() && cellTemp.Tile.Speciality == TileSpeciality.Frame &&
                            cellTemp.Tile.IsAnyOf(TileState.AnimalMod))
                        {
                            result += _mechanicTargetingSettings.FrameNearAnimalWeight;
                            break;
                        }
                    }
                }
            }

            _weightsCache[cell] = result;
            return result;
        }
    }
}