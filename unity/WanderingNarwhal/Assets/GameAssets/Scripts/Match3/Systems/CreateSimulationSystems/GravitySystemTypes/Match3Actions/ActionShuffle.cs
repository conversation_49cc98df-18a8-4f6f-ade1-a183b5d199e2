using System.Collections.Generic;
using BBB.Audio;
using BBB.Match3.Debug;
using BBB.Match3.Renderer;
using BebopBee.Core;
using BebopBee.Core.Audio;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionShuffle : Match3ActionBase
    {
        private readonly Dictionary<int, TileKinds> _lateDefinedTileKinds;
        private readonly Dictionary<int, Coords> _moves;
        private readonly int _movesLeftOriginal;
        private readonly bool _byBooster;
        private ShuffleAnimationsSettings _shuffleAnimationsSettings;

        public ActionShuffle(Dictionary<int, Coords> moves, bool byBooster,
            Dictionary<int, TileKinds> definedTileKinds)
        {
            _moves = moves;
            _movesLeftOriginal = _moves.Count;
            _byBooster = byBooster;
            _lateDefinedTileKinds = definedTileKinds;
        }
        
        private static ShuffleAnimationsSettings GetShuffleAnimationSettings(PlaySimulationActionProxy proxy)
        {
            var shuffleAnimSettings = proxy.TileResourceSelector.GetShuffleAnimationSettings();
            
            if (shuffleAnimSettings != null)
            {
                return shuffleAnimSettings;
            }
            shuffleAnimSettings.angle = 0f;
            shuffleAnimSettings.animationCurve = null;
            shuffleAnimSettings.perpOffset = 0.2f;
            shuffleAnimSettings.unitsPerSound = 4f;
            
            return shuffleAnimSettings;
        }

        public override void Execute(Grid grid, PlaySimulationActionProxy proxy, List<Coords> releasedCoords = null)
        {
            if (proxy == null)
            {
                InitialExecution(grid, proxy);
            }
            else
            {
                base.Execute(grid, proxy, releasedCoords);
            }
        }
        
        protected override bool CanExecute(Grid grid, PlaySimulationActionProxy proxy)
        {
            return proxy.TileTickPlayer.AllFrozen() && base.CanExecute(grid, proxy);
        }

        private bool ModifyGrid(Grid grid)
        {
            var tileidToTileMap = new Dictionary<int, Tile>();
            foreach (var cell in grid.Cells)
            {
                if (cell.HasTile())
                {
                    tileidToTileMap[cell.Tile.Id] = cell.Tile;
                }
            }

            foreach (var move in _moves)
            {
                if (!tileidToTileMap.ContainsKey(move.Key))
                {
#if BBB_LOG
                    M3Debug.LogError($"Tile {move.Key} not found to move to {move.Value}");
#endif
                    continue;
                }

                var tile = tileidToTileMap[move.Key];
                var cell = grid.GetCell(move.Value);
                if (_lateDefinedTileKinds.ContainsKey(move.Key))
                {
                    _lateDefinedTileKinds[move.Key] = tile.Kind;
                }

                cell.HardRemoveTile(0);
                cell.AddTile(tile);
            }

            return true;
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            if (ModifyGrid(grid))
            {
                
                if (proxy == null)
                    return;
                
                proxy.TileController.Update(grid);
                
                if (_movesLeftOriginal <= 0) 
                    return;

                var movesLeft = _movesLeftOriginal;
                var finishedMoving = new HashSet<Coords>();
                WaitingForCompletion = true;
                
                _shuffleAnimationsSettings = GetShuffleAnimationSettings(proxy);
                foreach (var kvp in _moves)
                {
                    var tileView = proxy.TileController.GetTileViewById(kvp.Key);
                    if (tileView != null)
                    {
                        var currentCoord = tileView.TileMotionController.Coords;
                        var targetCoord = kvp.Value;
                        
                        var magnitude = (targetCoord - currentCoord).ToUnityVector2().magnitude;
                        
                        var unitsPerSecond = _shuffleAnimationsSettings.unitsPerSound;
                        var perpOffset = _shuffleAnimationsSettings.perpOffset;
                        var animationCurve = _shuffleAnimationsSettings.animationCurve;
                        var angle = _shuffleAnimationsSettings.angle;
                        
                        var duration = magnitude / unitsPerSecond;

                        var startPos = proxy.GridController.ToLocalPosition(currentCoord);
                        var targetPos = proxy.GridController.ToLocalPosition(targetCoord);
                        var path = PathFactory.GetTwoPointsSimplePath(startPos, targetPos, perpOffset);
                        tileView.MoveLayersOnTop();
                        tileView.TweenLocalPosition(startCoord:currentCoord, targetCoords: targetCoord, 
                            duration: duration,
                            path: path, animationCurve,
                            callback: () => OnMoveDone(tileView, targetCoord));
                        
                        tileView.TweenAddRotation(angle, duration);
                    }
                }

                void OnMoveDone(TileView tileView, Coords targetCoord)
                {
                    // OnMoveDone can be called more than once for each tile, but we should process it only once
                    if (!finishedMoving.Add(targetCoord)) return;
                    
                    movesLeft--;
                    tileView.MoveLayersToBottom();
                    tileView.TileMotionController.Clean();
                    tileView.TileMotionController.SetPosAndFreeze(targetCoord.ToUnityVector2());

                    if (movesLeft <= 0)
                    {
                        WaitingForCompletion = false;
                    }
                }

                AudioProxy.PlaySound(_byBooster ? Match3SoundIds.ShuffleBoost : Match3SoundIds.ShuffleSimple);
            }
        }

        protected override string GetMembersString()
        {
            return $"moves={string.Join(",", _moves)}";
        }
    }
}