using System.Collections.Generic;
using BBB.Match3.Debug;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public static class SimulationPlayUtils
    {
        private static readonly HashSet<Coords> _removedThisTurn = new HashSet<Coords>();

        public static void ClearDebugInfo()
        {
            _removedThisTurn.Clear();
        }

        public static bool CheckTileForRemoval(Tile tile, Coords coords)
        {
            if (tile.IsNull())
            {
                if (_removedThisTurn.Contains(coords))
                {
                    return true;
                }

#if BBB_LOG
                M3Debug.LogError($"Trying to remove null tile");
#endif
                return true;
            }

            return false;
        }

        public static void AddTileToRemoval(Coords coords)
        {
            _removedThisTurn.Add(coords);
        }
    }
}