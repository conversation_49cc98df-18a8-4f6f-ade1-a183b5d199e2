using System;
using System.Collections.Generic;

namespace BBB.Match3.Systems.CreateSimulationSystems.SearchMatchesSystemTypes
{
    public enum PossibleMoveType
    {
        Simple = 0,
        LineBreaker = 1,
        Bomb = 2,
        ColorBomb = 3,
        DoubleTap = 5,
        Propeller = 6
    }

    public enum PossibleMoveDir
    {
        Unknown = 0,
        Hor = 1,
        Ver = 2
    }

    public readonly struct PossibleMove : IEquatable<PossibleMove>
    {
        public readonly Cell FirstCell;
        public readonly Cell SecondCell;
        public readonly ICollection<Match> Matches;
        public readonly PossibleMoveType Type;

        private PossibleMoveDir DirType
        {
            get
            {
                var deltaCoord = FirstCell.Coords - SecondCell.Coords;
                if (deltaCoord.X == 0)
                    return PossibleMoveDir.Ver;

                return deltaCoord.Y == 0 ? PossibleMoveDir.Hor : PossibleMoveDir.Unknown;
            }
        }

        public int GetLongestMatchLength()
        {
            if (Matches == null || Matches.Count == 0)
                return 0;

            var max = 0;
            foreach (var m in Matches)
            {
                if (m.Length > max)
                {
                    max = m.Length;
                }
            }
            return max;
        }

        public int GetMaxEqualMatchesLength()
        {
            if (Matches == null || Matches.Count == 0)
            {
                return 0;
            }
            var tempSpan = (Span<int>)stackalloc int[8];
            foreach (var m in Matches)
            {
                if (m.Kind is TileKinds.None or TileKinds.Undefined or TileKinds.Error)
                    continue;
                tempSpan[(int) m.Kind] += m.Length;
            }

            return tempSpan.Max();
        }
        
        public CardinalDirections GetMoveDirectionForTile(Coords coords)
        {
            var dirType = DirType;

            switch (dirType)
            {
                case PossibleMoveDir.Unknown: throw new Exception("The DirType is undefined"); ;
                case PossibleMoveDir.Hor:
                    {
                        var x = coords.X;
                        var delta = x - FirstCell.Coords.X;
                        if (delta == 0)
                            delta = x - SecondCell.Coords.X;

                        return delta switch
                        {
                            //if its right to the other one
                            1 => CardinalDirections.W,
                            -1 => CardinalDirections.E,
                            _ => throw new Exception("GetMoveDirectionForTile: delta is wrong")
                        };
                    }
                case PossibleMoveDir.Ver:
                    {
                        var y = coords.Y;
                        var delta = y - FirstCell.Coords.Y;
                        if (delta == 0)
                            delta = y - SecondCell.Coords.Y;

                        return delta switch
                        {
                            1 => CardinalDirections.S,
                            -1 => CardinalDirections.N,
                            _ => throw new Exception("GetMoveDirectionForTile: delta is wrong")
                        };
                    }
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }

        public bool HasCoord(Coords coords)
        {
            return FirstCell.Coords == coords || SecondCell.Coords == coords;
        }

        public bool HasSpeciality(TileSpeciality spec)
        {
            return (FirstCell.HasTile() && FirstCell.Tile.Speciality == spec)
                   || (SecondCell.HasTile() && SecondCell.Tile.Speciality == spec);
        }

        public IEnumerable<Coords> GetAllInvolvedCoords
        {
            get
            {
                yield return FirstCell.Coords;
                yield return SecondCell.Coords;

                if (Matches != null)
                    foreach (var match in Matches)
                    foreach (var coord in match.GetAllCoords())
                    {
                        if (coord == FirstCell.Coords || coord == SecondCell.Coords)
                            continue;
                        
                        yield return coord;
                    }
                           
            }
        }
        
        public IEnumerable<Coords> GetAllMatchedCoords
        {
            get
            {
                if (Matches != null)
                    foreach (var match in Matches)
                    foreach (var coord in match.GetAllCoords())
                    {
                        yield return coord;
                    }
            }
        }
        
        public PossibleMove(Cell cell, Cell otherCell, ICollection<Match> matches)
        {
            if (cell.Coords < otherCell.Coords)
            {
                FirstCell = cell;
                SecondCell = otherCell;
            }
            else
            {
                FirstCell = otherCell;
                SecondCell = cell;
            }

            Matches = matches;
            Type = PossibleMoveType.Simple;
        }

        public PossibleMove(Cell cell, Cell otherCell, ICollection<Match> matches, PossibleMoveType type = PossibleMoveType.Simple)
        {
            if (cell.Coords < otherCell.Coords)
            {
                FirstCell = cell;
                SecondCell = otherCell;
            }
            else
            {
                FirstCell = otherCell;
                SecondCell = cell;
            }

            Matches = matches;

            Type = type;
        }

        public override string ToString()
        {
            var result = string.Empty;
            result += FirstCell + " ";
            result += SecondCell + " ";
            foreach (var match in Matches)
                result += match + " ";

            return result;
        }

        public bool Equals(PossibleMove other)
        {
            return Equals(FirstCell, other.FirstCell) && Equals(SecondCell, other.SecondCell);
        }

        public override bool Equals(object obj)
        {
            if (ReferenceEquals(null, obj)) return false;
            return obj is PossibleMove && Equals((PossibleMove)obj);
        }

        public override int GetHashCode()
        {
            unchecked
            {
                return ((FirstCell != null ? FirstCell.GetHashCode() : 0) * 397) ^ (SecondCell != null ? SecondCell.GetHashCode() : 0);
            }
        }
    }
}
