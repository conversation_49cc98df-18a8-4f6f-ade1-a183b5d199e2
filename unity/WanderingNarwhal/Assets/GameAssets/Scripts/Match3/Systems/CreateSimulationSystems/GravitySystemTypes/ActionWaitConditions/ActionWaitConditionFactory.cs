using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace BBB.Match3
{
    public static class ActionWaitConditionFactory
    {
        public static WaitConditionBase Create(HitWaitParams hitWaitParams = null, int tileId = -1, Coords? coords = null)
        {
            if (hitWaitParams != null)
            {
                if ((hitWaitParams.DamageSource & DamageSource.AutoMatch) != 0)
                {
                    return new AutoMatchWaitCondition(hitWaitParams, tileId);
                }
                
                if ((hitWaitParams.DamageSource & DamageSource.AnyBomb) != 0)
                {
                    return new BombWaitCondition(hitWaitParams);
                }
                
                if ((hitWaitParams.DamageSource & DamageSource.LineBreakerArrow) != 0)
                {
                    return new LinebreakerWaitCondition(hitWaitParams);
                }
                
                if ((hitWaitParams.DamageSource & DamageSource.PropellerOrPropellerCombo) != 0)
                {
                    return new PropellerWaitCondition(hitWaitParams);
                }
                
                if ((hitWaitParams.DamageSource & DamageSource.Skunk) != 0)
                {
                    return new SkunkWaitCondition(hitWaitParams);
                }

                if ((hitWaitParams.DamageSource & DamageSource.RemoveColorTiles) != 0)
                {
                    return new BoltWaitCondition(hitWaitParams);
                }

                if ((hitWaitParams.DamageSource & DamageSource.Whirlpool) != 0 ||
                    (hitWaitParams.DamageSource & DamageSource.Dynamite) != 0)
                {
                    return new WhirlpoolWaitCondition(hitWaitParams);
                }
                
                if ((hitWaitParams.DamageSource & DamageSource.TukTuk) != 0)
                {
                    return new TukTukWaitCondition(hitWaitParams);
                }
                
                if ((hitWaitParams.DamageSource & DamageSource.UsableBoost) != 0 && 
                    hitWaitParams is {DirectionalBoosterInfo: not null})
                {
                    return new DirectionalBoostersWaitCondition(hitWaitParams);
                }
                
                if ((hitWaitParams.DamageSource & DamageSource.FireWorks) != 0)
                {
                    return new FireWorksWaitCondition(hitWaitParams);
                }
                
                if ((hitWaitParams.DamageSource & DamageSource.SeaMine) != 0)
                {
                    return new SeaMineWaitCondition(hitWaitParams);
                }
            }

            return new NotFrozenWaitCondition(tileId, coords ?? Coords.OutOfGrid);
        }
    }
}