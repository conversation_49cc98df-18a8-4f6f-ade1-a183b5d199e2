using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.DI;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.SearchMatchesSystemTypes;
using FBConfig;
using GameAssets.Scripts.Match3.Settings;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class AutoBoostInstance
    {
        public readonly string Name;
        public Coords? CellPos;
        public readonly object ExtraInfo;

        public AutoBoostInstance(string name, Coords? cellPos = null, object extraInfo = null)
        {
            Name = name;
            CellPos = cellPos;
            ExtraInfo = extraInfo;
        }
    }

    public interface IAutoBoostInputFactory
    {
        IEnumerable<AutoBoostPlayerInput> GetInputForAutoBoost(IEnumerable<AutoBoostInstance> boosters, Grid grid,
            bool checkForNeighbourPowerups, bool ordered);
    }

    public struct AutoBoostPlayerInput
    {
        public string AutoBoostUid;
        public IPlayerInput Input;
    }

    public sealed class AutoBoosterInputFactory : IAutoBoostInputFactory, IContextInitializable
    {
        private readonly Dictionary<string, BoosterItem> _enumMap = new();
        private readonly Dictionary<BoosterItem, string> _enumMapReversed = new();
        private static HashSet<PossibleMove> _possibleMoves = new();
        private static readonly List<Match> MatchList = new();
        private static readonly Dictionary<Cell, int> PossibleCells = new();
        private static readonly List<Match> PreviousMatches = new();

        private static M3Settings _m3Settings;
        
        public void InitializeByContext(IContext context)
        {
            var configProvider = context.Resolve<IConfig>();
            var boosterConfigs = configProvider.Get<BoosterConfig>();
            _enumMap.Clear();
            _enumMapReversed.Clear();
            _m3Settings = context.Resolve<M3Settings>();

            foreach (var kvp in boosterConfigs)
            {
                var config = kvp.Value;
                BoosterItem boostItem;
                try
                {
                    boostItem = (BoosterItem)Enum.Parse(typeof(BoosterItem), config.EnumName);
                }
                catch (Exception e)
                {
                    BDebug.LogErrorFormat(LogCat.Match3,"Exception while parsing booster enum name for {0} {1} {2}", config.Uid, e.Message, e.StackTrace);
                    continue;
                }

                _enumMap[config.Uid] = boostItem;
                if (!_enumMapReversed.ContainsKey(boostItem))
                {
                    _enumMapReversed[boostItem] = config.Uid;
                }
            }
        }

        private static IEnumerable<Cell> GetPossibleCellsForBoosterPlacement(Grid grid)
        {
            _possibleMoves.Clear();
            MatchList.Clear();
            PossibleCells.Clear();
            PreviousMatches.Clear();
            SearchMatchesSystem.SearchForAllPossibleMovesNoAlloc(grid, ref _possibleMoves);
            
            foreach (var possibleMove in _possibleMoves)
            {
                if (possibleMove.Matches == null)
                {
                    foreach (var coords in possibleMove.GetAllInvolvedCoords)
                    {
                        if (!grid.TryGetCell(coords, out var cell)) continue;
                        if (IsAppropriateCell(cell)) 
                            PossibleCells.TryAdd(cell, _m3Settings.GetAutoBoosterPriorityByType
                                (AutoBoosterSettlePriorityType.PowerUpMatchesPriority));
                    }
                    continue;
                }
                var priority = AutoBoosterSettlePriorityType.None;
                PreviousMatches.Clear();
                foreach (var match in possibleMove.Matches)
                {
                    priority = match.Length switch
                    {
                        3 => AutoBoosterSettlePriorityType.SimpleMatchPriority,
                        4 when match.MatchType == MatchType.Square => AutoBoosterSettlePriorityType
                            .PropellerFormationPriority,
                        4 when match.MatchType != MatchType.Square => AutoBoosterSettlePriorityType
                            .LineBreakerFormationPriority,
                        >= 5 => AutoBoosterSettlePriorityType.ColorBombFormationPriority,
                        _ => priority
                    };

                    if (match.Length >= 3 && match.MatchType != MatchType.Square)
                    {
                        foreach (var previousMatch in PreviousMatches)
                        {
                            if (match.Length < 5 && match.AnyOverlap(previousMatch))
                            {
                                //Bomb
                                priority = AutoBoosterSettlePriorityType.BombFormationPriority;
                            }
                        }
                    }

                    CheckAndAddCellPriority(possibleMove.FirstCell, PossibleCells, priority);
                    CheckAndAddCellPriority(possibleMove.SecondCell, PossibleCells, priority);
                    
                    foreach (var coords in match.GetAllCoords())
                    {
                        if (!grid.TryGetCell(coords, out var cell)) continue;
                        CheckAndAddCellPriority(cell, PossibleCells, priority);
                    }
                    MatchList.Add(match);
                    PreviousMatches.Add(match);
                }
            }
            //Checking for simple tiles that are not of the same kind in power up matches
            foreach (var matchA in MatchList)
            {
                if(matchA.Length <= 3)continue;
                foreach (var coords in matchA.GetAllCoords())
                {
                    if (!grid.TryGetCell(coords, out var cell)) continue;
                    if (!IsAppropriateCell(cell)) continue;
                    if (cell.Tile?.Kind != matchA.Kind)
                    {
                        PossibleCells[cell] -= _m3Settings.GetAutoBoosterPriorityByType
                            (AutoBoosterSettlePriorityType.SimpleTileWithinMatchPriority);
                    }
                }
            }
            var shuffledDict = ShuffleDictionary(PossibleCells);
            var keysList = new List<Cell>(shuffledDict.Keys);

            keysList.Sort((key1, key2) => shuffledDict[key1].CompareTo(shuffledDict[key2]));

            return keysList;
        } 
        
        private static Dictionary<Cell, int> ShuffleDictionary(Dictionary<Cell, int> inputDict)
        {
            var groups = new Dictionary<int, List<Cell>>();

            foreach (var kvp in inputDict)
            {
                if (!groups.ContainsKey(kvp.Value))
                {
                    groups[kvp.Value] = new List<Cell>();
                }
                groups[kvp.Value].Add(kvp.Key);
            }

            var shuffledDict = new Dictionary<Cell, int>();
            var random = new Random();

            foreach (var (key, cellList) in groups)
            {
                for (var i = cellList.Count - 1; i > 0; i--)
                {
                    var j = random.Next(0, i + 1);
                    (cellList[i], cellList[j]) = (cellList[j], cellList[i]);
                }

                foreach (var cell in cellList)
                {
                    shuffledDict[cell] = key;
                }
            }

            return shuffledDict;
        }

        private static void CheckAndAddCellPriority(Cell cell, Dictionary<Cell, int> cellList, 
            AutoBoosterSettlePriorityType priorityType)
        {
            if (!IsAppropriateCell(cell)) return;
            
            var value = cellList.GetValueOrDefault(cell);
            var priorityToUpdate = _m3Settings.GetAutoBoosterPriorityByType(priorityType);
            if (priorityToUpdate > value)
            {
                cellList[cell] = priorityToUpdate;
            }
        }
        
        public static bool CanPlaceAllExtraBoosters(Grid grid, int count)
        {
            var appropriateCellCount = 0;

            foreach (var cell in grid.Cells)
            {
                if (!IsAppropriateCell(cell)) continue;
                
                appropriateCellCount++;

                if (appropriateCellCount >= count)
                {
                    return true;
                }
            }

            return appropriateCellCount >= count;
        }
        
        public static List<Cell> GetAppropriateCells(Grid grid, bool useSpecialCheck, bool ordered = false)
        {
            List<Cell> neighbourCells = null;

            if (useSpecialCheck || ordered)
            {
                neighbourCells = new List<Cell>();
                foreach (var cell in grid.Cells)
                {
                    if (IsCellAppropriateAndNotBreakingMoves(grid, cell))
                    {
                        neighbourCells.Add(cell);
                    }
                }
            }

            if (!ordered)
            {
                if (neighbourCells is { Count: > 0 })
                {
                    return new List<Cell>(neighbourCells);
                }

                var appropriateCells = new List<Cell>();
                foreach (var cell in grid.Cells)
                {
                    if (IsAppropriateCell(cell))
                    {
                        appropriateCells.Add(cell);
                    }
                }

                return appropriateCells;
            }

            var random = new Random();
            for (var i = neighbourCells.Count - 1; i > 0; i--)
            {
                var j = random.Next(i + 1);
                (neighbourCells[i], neighbourCells[j]) = (neighbourCells[j], neighbourCells[i]);
            }

            var priorityCellsList = GetPossibleCellsForBoosterPlacement(grid);

            var mergedCells = new List<Cell>(neighbourCells);
            foreach (var cell in priorityCellsList)
            {
                if (!mergedCells.Contains(cell))
                {
                    mergedCells.Add(cell);
                }
            }

            return mergedCells;
        }

        public IEnumerable<AutoBoostPlayerInput> GetInputForAutoBoost(IEnumerable<AutoBoostInstance> boosters, Grid grid,
            bool checkNeighbourCellsForBoosters, bool ordered)
        {
            var appropriateCells = GetAppropriateCells(grid, checkNeighbourCellsForBoosters, ordered);

            if (appropriateCells.Count == 0)
            {
                BDebug.LogError(LogCat.Match3,$"No enough cells found to put auto-boosters");
                yield break;
            }

            foreach (var booster in boosters)
            {
                if (!_enumMap.TryGetValue(booster.Name, out var boosterItem))
                    continue;

                if (appropriateCells.Count == 0)
                    break;

                var autoBoostUid = _enumMapReversed.GetValueOrDefault(boosterItem, string.Empty);

                Cell cellPos = null;

                if (booster.CellPos != null)
                {
                    foreach (var cell in grid.Cells)
                    {
                        if (cell.Coords != booster.CellPos) continue;
                        
                        cellPos = cell;
                        break;
                    }
                }
                else
                {
                    if (ordered)
                    {
                        cellPos = appropriateCells.Count > 0 ? appropriateCells[0] : null;
                    }
                    else
                    {
                        cellPos = appropriateCells.DeterministicRandomInSelf();
                    }
                }
                
                appropriateCells.Remove(cellPos);

                switch (boosterItem)
                {
                    case BoosterItem.CreateLineBreaker:

                        var dir = booster.ExtraInfo != null
                            ? (SimplifiedDirections)booster.ExtraInfo
                            : RandomSystem.Next() > 0.5f
                                ? SimplifiedDirections.Horizontal
                                : SimplifiedDirections.Vertical;
                        yield return new AutoBoostPlayerInput { AutoBoostUid = autoBoostUid, Input = new PlayerInputItemCreateLineBreaker(cellPos.Coords, dir) };
                        break;

                    case BoosterItem.CreateBomb:
                        yield return new AutoBoostPlayerInput { AutoBoostUid = autoBoostUid, Input = new PlayerInputItemCreateBomb(cellPos.Coords) };
                        break;

                    case BoosterItem.CreateColorBomb:
                        yield return new AutoBoostPlayerInput { AutoBoostUid = autoBoostUid, Input = new PlayerInputItemCreateColorBomb(cellPos.Coords) };
                        break;
                    
                    case BoosterItem.CreatePropeller:
                        yield return new AutoBoostPlayerInput { AutoBoostUid = autoBoostUid, Input = new PlayerInputItemCreatePropeller(cellPos.Coords) };
                        break;
                    
                    case BoosterItem.CreatePropellerButler:
                        yield return new AutoBoostPlayerInput { AutoBoostUid = autoBoostUid, Input = new PlayerInputItemCreatePropellerButler(cellPos.Coords) };
                        break;

                    case BoosterItem.CreateBombButler:
                        yield return new AutoBoostPlayerInput { AutoBoostUid = autoBoostUid, Input = new PlayerInputItemCreateBombButler(cellPos.Coords) };
                        break; 
                    case BoosterItem.CreateColorBombButler:
                        yield return new AutoBoostPlayerInput { AutoBoostUid = autoBoostUid, Input = new PlayerInputItemCreateColorBombButler(cellPos.Coords) };
                        break; 
                    case BoosterItem.CreateLineBreakerButler:
                         dir = booster.ExtraInfo != null
                            ? (SimplifiedDirections)booster.ExtraInfo
                            : RandomSystem.Next() > 0.5f
                                ? SimplifiedDirections.Horizontal
                                : SimplifiedDirections.Vertical;
                        yield return new AutoBoostPlayerInput { AutoBoostUid = autoBoostUid, Input = new PlayerInputItemCreateLineBreakerButler(cellPos.Coords, dir) };
                        break;
                    default:
                        throw new ArgumentOutOfRangeException("boostItems", boosterItem, $"{boosterItem} boost can not be used as autoboost");
                }
            }
        }

        private static bool IsAppropriateCell(Cell cell)
        {
            return cell.HasTile()
                   && cell.IsBaseCellMatchable()
                   && cell.Tile.IsMatchableSimpleTile
                   && cell.Tile.IsShufflable
                   && !cell.Tile.IsBoost
                   && cell.Tile.Speciality != TileSpeciality.ColorCrate;
        }

        private static List<Coords> _cellOffsets = new()
        {
            new Coords(-1, 0),
            new Coords(1, 0),
            new Coords(0, 1),
            new Coords(0, -1),
        };
        
        private static bool IsCellAppropriateAndNotBreakingMoves(Grid grid, Cell cell)
        {
            if (!IsAppropriateCell(cell))
                return false;

            var possibleMoves = SearchMatchesSystem.SearchForAllPossibleMoves(grid);
            foreach (var move in possibleMoves)
            {
                if (move.Matches == null) continue;

                foreach (var coord in move.GetAllInvolvedCoords)
                {
                    if (coord == cell.Coords)
                    {
                        return false;
                    }
                }
            }

            return true;
        }
    }
}