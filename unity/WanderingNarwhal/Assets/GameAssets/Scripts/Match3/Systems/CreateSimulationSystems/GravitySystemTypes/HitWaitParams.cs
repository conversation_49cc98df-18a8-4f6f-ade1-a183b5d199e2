using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes.AdditionalHitInfos;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class HitWaitParams
    {
        public readonly Coords Coords;
        public readonly Coords SourceCoords;
        public readonly DamageSource DamageSource;
        private IBoostInfo _boostInfo;
        public LineBreakerInfo LinebreakerInfo => _boostInfo as LineBreakerInfo;
        public PropellerInfo PropellerInfo => _boostInfo as PropellerInfo;
        public BombInfo BombInfo => _boostInfo as BombInfo;
        public BoltInfo BoltInfo  => _boostInfo as BoltInfo;
        public WhirlpoolInfo WhirlpoolInfo => _boostInfo as WhirlpoolInfo;
        public SkunkHitInfo SkunkHitInfo => _boostInfo as SkunkHitInfo;
        public TukTukInfo TukTukInfo => _boostInfo as TukTukInfo;
        public FireWorksInfo FireWorksInfo => _boostInfo as FireWorksInfo;
        public DirectionalBoosterInfo DirectionalBoosterInfo => _boostInfo as DirectionalBoosterInfo;
        public SeaMineInfo SeaMineInfo => _boostInfo as SeaMineInfo;
    
        public int[] MatchIds { get; private set; }
        public int HitSourceUid { get; private set; }
        public int HitDistance { get; private set; }
        public  Dictionary<TileKinds, HashSet<Coords>> SameTileMatchSet { get; private set;}

        public HitWaitParams()
        {
            
        }

        public HitWaitParams(Coords coords, DamageSource damageSource, Coords sourceCoords = default)
        {
            Coords = coords;
            SourceCoords = sourceCoords;
            DamageSource = damageSource;
        }

        public HitWaitParams AddMatchIds(int[] ids)
        {
            MatchIds = ids;
            return this;
        }
        
        public HitWaitParams AddMatchSet( Dictionary<TileKinds, HashSet<Coords>> sameTileMatchSet)
        {
            SameTileMatchSet = sameTileMatchSet;
            return this;
        }
        
        public HitWaitParams AddBoostInfo(IBoostInfo boostInfo)
        {
            _boostInfo = boostInfo;
            return this;
        }

        public HitWaitParams AddHitSourceUid(int hitSourceUid)
        {
            HitSourceUid = hitSourceUid;
            return this;
        }

        public int GetAdditionalSaltValue()
        {
            if (_boostInfo is LineBreakerInfo lbInfo)
            {
                return DamageSource == DamageSource.LineBreakerArrowHor
                    ? lbInfo.OriginCoords.X
                    : lbInfo.OriginCoords.Y;
            }

            return 0;
        }

        public HitWaitParams AddHitDistance(int hitDistance)
        {
            HitDistance = hitDistance;
            return this;
        }
    }
}