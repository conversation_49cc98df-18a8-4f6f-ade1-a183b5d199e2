namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes.AdditionalHitInfos
{
    public sealed class SeaMineInfo : IBoostInfo
    {
        public int SeaMineId { get; }

        public SeaMineInfo(int id)
        {
            SeaMineId = id;
        }

        public bool BoostEquals(IBoostInfo boostInfo)
        {
            return boostInfo is SeaMineInfo seaMineInfo && SeaMineId == seaMineInfo.SeaMineId;
        }
    }
}