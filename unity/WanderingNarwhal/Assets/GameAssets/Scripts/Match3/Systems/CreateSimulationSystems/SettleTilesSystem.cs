using System;
using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using GameAssets.Scripts.Match3.Settings;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public class SettleTilesSystem
    {
        private readonly List<Coords> _settledTiles = new();
        private readonly HashSet<int> _freezeLocks = new();

        private static readonly Comparison<Cell> _cellSortComparisonDelegate = (a, b) =>
        {
            var result = a.Coords.Y.CompareTo(b.Coords.Y);
            if (result == 0)
            {
                result = a.SpecialOrder().CompareTo(b.SpecialOrder());
            }

            return result;
        };

        public void Clear()
        {
        }

        /// <summary>
        /// Settling tiles during one turn
        /// </summary>
        /// <returns>True if any tile was moved</returns>
        public bool SettleOneTurn(Grid grid, TileHitReactionHandler reactionHandler, Dictionary<Coords, Path> cellsAndDirections, IRootSimulationHandler handler,
            out bool isTileMoved, out bool isTileFrozen)
        {
            lock (_settledTiles)
            {
                if (_freezeLocks.Count > 0)
                {
                    isTileMoved = false;
                    isTileFrozen = true;
                    return false;
                }

                isTileFrozen = false;

                _settledTiles.Clear();
                isTileMoved = false;

                grid.Cells.Sort(_cellSortComparisonDelegate);

                foreach (var cell in grid.Cells)
                {
                    var tile = cell.Tile;

                    if (ReferenceEquals(tile, null) || tile.IsAnyOf(TileState.ZeroGravity))
                    {
                        continue;
                    }

                    if (tile.Speciality == TileSpeciality.DropItem)
                    {
                        if (cell.IsAnyOf(CellState.Despawner))
                        {
                            reactionHandler.RegisterTileShouldReactOnDie(grid, handler, null, cell.Coords, tile);
                            cell.HardRemoveTile(0);
                            handler.AddAction(new ActionVisualizeTileDestroy(cell.Coords, tile, null));

                            if (tile.IsAnyOf(TileState.InTransition))
                            {
                                handler.FinalHandleMovementAction(tile, cell.Coords);
                            }

                            handler.AddAction(new ActionRemoveTile(cell.Coords, tile, null));
                            isTileMoved = true;
                            continue;
                        }
                    }

                    var path = cellsAndDirections[cell.Coords];
                    var directions = path.Directions;

                    if (path.State == PathState.Blocked)
                    {
                        if (!tile.IsAnyOf(TileState.InTransition)) continue;

                        // Tile settled:
                        handler.FinalHandleMovementAction(tile, cell.Coords);
                        _settledTiles.Add(cell.Coords);

                        continue;
                    }

                    if (directions.IsAnyOf(CardinalDirections.S))
                    {
                        // S
                        var southCoords = cell.Coords.GoSouthDirectionExtruded(grid, out int jumpingThroughGapOfSize);
                        if (CheckAndMoveUsingDirection(handler, grid, cell, southCoords, LogicalActionType.Move, jumpingThroughGapOfSize))
                        {
                            isTileMoved = true;
                            continue;
                        }
                    }

                    var firstDir = cell.CellDir;
                    var secondDir = firstDir.Opposite();

                    if (DiagMove(handler, directions, cell, grid, (CardinalDirections)firstDir, cellsAndDirections))
                    {
                        cell.SwitchCellDir();
                        isTileMoved = true;
                        continue;
                    }

                    if (DiagMove(handler, directions, cell, grid, (CardinalDirections)secondDir, cellsAndDirections))
                    {
                        cell.SwitchCellDir();
                        isTileMoved = true;
                        continue;
                    }

                    if (!tile.IsAnyOf(TileState.InTransition))
                        continue;

                    // Tile settled:
                    handler.FinalHandleMovementAction(tile, cell.Coords);
                    _settledTiles.Add(cell.Coords);
                }

                FilterSettledTiles(grid, _settledTiles);
            }

            return _settledTiles.Count > 0;
        }

        private readonly HashSet<Coords> _coordsToRemove = new();
        private readonly List<Coords> _tempCoordsList = new();
        private HashSet<Match> _tempMatchesSet = new();

        private void FilterSettledTiles(Grid grid, List<Coords> settleCoords)
        {
            _tempCoordsList.Clear();
            _coordsToRemove.Clear();

            foreach (var coords in settleCoords)
            {
                var underCoord = coords.GoSingleCardinalDirection(CardinalDirections.S);
                if (grid.TryGetCell(underCoord, out var cell))
                {
                    _tempMatchesSet.Clear();
                    SearchMatchesSystem.FindMatchForCell(grid, cell, ref _tempMatchesSet);
                    if (_tempMatchesSet.Count > 0)
                    {
                        for (int y = coords.Y; y < Grid.MaxDimensionY; y++)
                        {
                            var coordToRemove = new Coords
                            {
                                X = coords.X,
                                Y = y
                            };

                            _coordsToRemove.Add(coordToRemove);
                        }
                    }
                }
            }

            foreach (var coord in settleCoords)
            {
                if (!_coordsToRemove.Contains(coord))
                {
                    _tempCoordsList.Add(coord);
                }
            }

            settleCoords.Clear();
            settleCoords.AddRange(_tempCoordsList);

            _tempCoordsList.Clear();
            _coordsToRemove.Clear();
        }

        private static bool DiagMove(IRootSimulationHandler events, CardinalDirections directions, Cell cell, Grid grid,
            CardinalDirections diagDir, Dictionary<Coords, Path> cellsAndDirections)
        {
#if M3_PROFILE
            Profiler.BeginSample("DiagMove");
#endif
            if (directions.IsAnyOf(diagDir))
            {
                diagDir.ToXY(out var x, out var y);
                // Target diagonal cell coord where potentially tile can move to.
                var targetSideCoord = new Coords(cell.Coords.X + x, cell.Coords.Y + y);
                var underCoords = new Coords(cell.Coords.X, cell.Coords.Y - 1);

                // Cell above target diagonal cell, which may be neighbour cell or distant cell (if there is a gap).
                var coordAboveTargetSideCoord = targetSideCoord.GoNorthDirectionExtruded(grid);

                // If cell above target cell contains tile that is ready to fall, then we skip diagonal move,
                // because straight tile fall has bigger priority than diagonal tile fall. -VK

                bool dropItemDiagPriority = cell.HasTile()
                                            && cell.Tile.Speciality == TileSpeciality.DropItem
                                            && (!cellsAndDirections.ContainsKey(underCoords) || cellsAndDirections[underCoords].State == PathState.Blocked);
                //for drop item we make diag move to be higher priority then straight falling
                if (dropItemDiagPriority || !CheckIfColumnIsBusyByOtherTile(coordAboveTargetSideCoord, grid))
                {
                    var diagCoord = cell.Coords.GoDiagDirectionExtruded(grid, diagDir);
                    if (CheckAndMoveUsingDirection(events, grid, cell, diagCoord, LogicalActionType.Move, 0))
                    {
#if M3_PROFILE
                        Profiler.EndSample();
#endif
                        return true;
                    }
                }
            }
#if M3_PROFILE
            Profiler.EndSample();
#endif
            return false;
        }

        private static bool CheckIfColumnIsBusyByOtherTile(Coords startCoords, Grid grid)
        {
            var coords = startCoords;
            var loopCounter = 0;
            while (true)
            {
                loopCounter++;

                if (loopCounter > 300)
                    throw new Exception("Looped in CheckIfVerticalIsBusy!");

                if (grid.TryGetCell(coords, out var cell))
                {
                    if (cell.IsAnyOf(CellState.NotAcceptingTiles))
                        return false;

                    if (cell.HasMultiSizeCellReference())
                        return false;

                    if (cell.HasAnyWall(CardinalDirections.S))
                        return false;

                    if (ReferenceEquals(cell.Tile, null))
                    {
                        if (cell.IsAnyOf(CellState.Spawner))
                            return true;

                        if (cell.HasAnyWall(CardinalDirections.N))
                            return false;

                        coords = coords.GoSingleCardinalDirection(CardinalDirections.N);
                        continue;
                    }

                    if (cell.Tile.IsAnyOf(TileState.ZeroGravity))
                        return false;

                    return true;
                }

                return false;
            }
        }

        private static bool CheckAndMoveUsingDirection(IRootSimulationHandler handler, Grid grid, Cell oldCell, Coords newCoords,
            LogicalActionType moveType, int jumpingThroughGapOfSize)
        {
            var oldCellCoords = oldCell.Coords;

            if (!grid.TryGetCell(newCoords, out var newCell))
                return false;

            if (oldCell.Tile.IsAnyOf(TileState.ZeroGravity))
                return false;

            if (!newCell.CanAcceptTile())
                return false;

            var tile = oldCell.Tile;

            newCell.SwapTileWith(oldCell); // oldCell getting null from newCell

            switch (moveType)
            {
                case LogicalActionType.Move:
                    handler.AddAction(new ActionMove(oldCellCoords, newCoords, newCell.Tile, jumpingThroughGapOfSize));
                    break;

                default:
                    throw new ArgumentOutOfRangeException("moveType", moveType, null);
            }

            handler.HandleMovementAction(tile, oldCell.Coords);

            return true;
        }

        public void AddFreezeLock(int lockId)
        {
            _freezeLocks.Add(lockId);
        }

        public void RemoveFreezeLock(int lockId)
        {
            _freezeLocks.Remove(lockId);
        }
    }
}