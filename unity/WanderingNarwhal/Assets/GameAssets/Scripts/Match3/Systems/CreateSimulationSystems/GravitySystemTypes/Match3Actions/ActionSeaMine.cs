using System;
using System.Collections.Generic;
using BBB.Audio;
using BebopBee.Core;
using BebopBee.Core.Audio;
using UniRx;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionSeaMine : Match3ActionBase
    {
        private readonly int _seaMineId;
        private readonly Coords _coords;
        private readonly HitWaitParams _hitWaitParams;
        private readonly HashSet<Coords> _damageArea;

        public ActionSeaMine(int seaMineId, Coords coords, HashSet<Coords> damageArea, HitWaitParams hitWaitParams)
        {
            _seaMineId = seaMineId;
            _coords = coords;
            _hitWaitParams = hitWaitParams;
            _damageArea = damageArea;
            AffectedCoords.Add(_coords);
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ReleasedCoords.Add(_coords);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var minX = int.MaxValue;
            var maxX = int.MinValue;
            var minY = int.MaxValue;
            var maxY = int.MinValue;

            foreach (var coord in _damageArea)
            {
                if (coord.X < minX)
                {
                    minX = coord.X;
                }

                if (coord.X > maxX)
                {
                    maxX = coord.X;
                }

                if (coord.Y < minY)
                {
                    minY = coord.Y;
                }

                if (coord.Y > maxY)
                {
                    maxY = coord.Y;
                }
            }

            var waitUntil = new Func<bool>(() => true);
            
            if (_hitWaitParams != null)
            {
                var waitCondition = ActionWaitConditionFactory.Create(_hitWaitParams);
                waitUntil = () => !waitCondition.WaitForExpectedState(grid, proxy);
            }

            if (waitUntil())
            {
                SpawnSeaMine();
            }
            else
            {
                // Do not spawn SeaMine until waiting condition is met
                MainThreadDispatcher.StartUpdateMicroCoroutine(Rx.WaitUntil(
                    waitUntil, SpawnSeaMine));
            }

            return;

            void SpawnSeaMine()
            {
                AudioProxy.PlaySound(Match3SoundIds.SeaMineBlip);
                Rx.Invoke(proxy.Settings.SeaMineAnticipationDuration, _ =>
                {
                    AudioProxy.PlaySound(Match3SoundIds.SeaMineDestroy);
                    proxy.TileTickPlayer.BoardObjectFactory.MarkDamageSourceSpawned(TileAsset.SeaMine, _seaMineId);
                    proxy.FXRenderer.SpawnSeaMine(_coords);
                    proxy.TileTickPlayer.BoardObjectFactory.CreateOccupiersOver(proxy.Settings.SeaMineDestroyBusyTime, 
                        new Coords(minX, minY), new Coords(maxX, maxY));
                
                    ReleasedCoords.Add(_coords);
                });
            }
        }

        protected override string GetMembersString()
        {
            return $"coords={_coords}";
        }
    }
}