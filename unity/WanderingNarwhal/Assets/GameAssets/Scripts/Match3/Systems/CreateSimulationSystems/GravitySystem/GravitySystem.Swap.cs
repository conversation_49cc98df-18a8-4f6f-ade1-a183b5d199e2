using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.SearchMatchesSystemTypes;
using BBB.Match3.Systems.GoalsService;
using BBB.UI.Level.Input;
using GameAssets.Scripts.Match3.Logic.Tiles;
using GameAssets.Scripts.Match3.Settings;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public partial class GravitySystem : IAssistLogger
    {
        private HashSet<Match> _matchesCache = new HashSet<Match>();
        
        void IAssistLogger.Log(string log)
        {
            AddAction(new ActionAssistLog(log));
        }

        private bool TriggerBoosterAtCoordIfPossible(SimulationInputParams inputParams, Grid grid, Coords coords)
        {
            if (!grid.TryGetCell(coords, out var cell)) return false;
            if (cell.Tile is null) return false;

            if (cell.IsAnyOf(CellState.Ivy)) return false;
            if (!cell.Tile.IsTriggeredByTap) return false;
            if (cell.Tile.IsAnyOf(TileState.ArmorMods)) return false;

            if (cell.Tile.Speciality == TileSpeciality.ColorBomb)
            {
                var tileKindsList = grid.GetTopTileKinds();

                if (tileKindsList.Count == 0 || tileKindsList[0] == TileKinds.None)
                {
                    return false;
                }

                // As seen here, ColorBomb is different from other boosters when being triggered by a tap. In this case,
                // the Hit is not being triggered, so we need to add this action here to make sure SuperBoost progress is added
                AddAction(new ActionTryIncrementSuperBoost(cell, null, new HitWaitParams(cell.Coords, DamageSource.DTap)));

                var cellsToDamage = SpecialTileSystem.RemoveAllColorTiles(null, inputParams, this,
                    grid, _goalSystem, _settleTileSystem, cell, tileKindsList);

                if (cellsToDamage is {HasSomething: true})
                {
                    Queue.Append(cellsToDamage);
                }

                return true;
            }

            var hit = new Hit<Cell>(
                immuneTiles:null,
                target: cell,
                busyWait: M3Constants.BusyTime,
                damageSource: DamageSource.DTap,
                coords: coords);
            Queue.AppendHit(0, hit);

            IncrementCurrentTurn();
            return true;
        }

        private bool IsSwapAllowed(Grid grid, Coords firstCoords, Coords secondCoords)
        {
            return grid.IsSwappableCell(firstCoords)
                   && grid.IsSwappableCell(secondCoords)
                   && grid.IsSwapAllowedBetween(firstCoords, secondCoords);
        }

        private bool SwapTilesIfPossible(Grid grid, TileHitReactionHandler reactionHandler,
            SimulationInputParams inputParams, GoalsSystem goalsSystem, TileResourceSelector tileResources, EasyTouchInputController.CoordsPair coordsPair)
        {
            if (!IsSwapAllowed(grid, coordsPair.FirstCoords, coordsPair.SecondCoords))
            {
                if (grid.IsSwappableCell(coordsPair.FirstCoords))
                {
                    AddAction(new ActionShortSwap(coordsPair.FirstCoords, coordsPair.SecondCoords));
                }
                    
                return false;
            }

            var gotSomeCell = grid.TryGetCell(coordsPair.FirstCoords, out var someCell);
            var gotOtherCell = grid.TryGetCell(coordsPair.SecondCoords, out var otherCell);
            if (!gotSomeCell || !gotOtherCell || !AttemptToSwap(out var firstTile, out var secondTile))
            {
                AddAction(new ActionSwapBoth(coordsPair.FirstCoords, coordsPair.SecondCoords, true));
                return false;
            }

            AddAction(new ActionSwapBoth(someCell.Coords, otherCell.Coords));
            AddAction(new ActionTryIncrementSuperBoost(someCell, otherCell));

            var preferedCoords = new List<Coords> { coordsPair.FirstCoords, coordsPair.SecondCoords };
            var specialTilesCalculation = SpecialTileCalculationResult.Spawn();
            CalculateSpecialTilesSystem.CalculateSpecialTiles(specialTilesCalculation,
                grid, _matchesCache, preferedCoords,
                true, _lastSettledCoordinatesQueue);

            var immuneTiles = specialTilesCalculation.GetTilesImmuneToSpecial(grid);

            specialTilesCalculation.Release();

            var cellsToDamage = CalculateSpecialMatch(immuneTiles, grid, inputParams, goalsSystem, someCell, otherCell);
            if (cellsToDamage is {HasSomething: true})
            {
                Queue.Append(cellsToDamage);
            }
            else
            {
                if (!(firstTile is null) && firstTile.IsTriggeredByTap)
                {
                    var hit = new Hit<Cell>(
                        immuneTiles:null,
                        target: otherCell,
                        busyWait: M3Constants.BusyTime,
                        damageSource: DamageSource.Swap,
                        coords: someCell.Coords);
                    Queue.AppendHit(0, hit);
                }
                else if (!(secondTile is null) && secondTile.IsTriggeredByTap)
                {
                    var hit = new Hit<Cell>(
                        immuneTiles:null,
                        target: someCell,
                        busyWait: M3Constants.BusyTime,
                        damageSource: DamageSource.Swap,
                        coords: otherCell.Coords);
                    Queue.AppendHit(0, hit);
                }
            }

#if M3_PROFILE
            UnityEngine.Profiling.Profiler.BeginSample("SwapTilesIfPossible::CheckAndProcessNewMatches");
#endif
            var result = CheckAndProcessNewMatches(grid, inputParams, goalsSystem, preferedCoords);
#if M3_PROFILE
            UnityEngine.Profiling.Profiler.EndSample();
#endif
            if (result || firstTile.IsNull() || secondTile.IsNull())
            {
                IncrementCurrentTurn();
            }

            return true;

            bool AttemptToSwap(out Tile firstTile, out Tile secondTile)
            {
                firstTile = someCell.Tile;
                secondTile = otherCell.Tile;

                someCell.SwapTileWith(otherCell);

                _matchesCache.Clear();
                SearchMatchesSystem.FindMatchesForCells(grid, someCell, otherCell, ref _matchesCache);
                if ((_matchesCache == null || _matchesCache.Count == 0) && !CheckForSpecialMove(firstTile, secondTile, out _))
                {
                    someCell.SwapTileWith(otherCell);
                    return false;
                }

                return true;
            }
        }

        // This is a copyPaste of function below
        public static bool CheckForSpecialMove(Tile tile, Tile otherTile,  out PossibleMoveType type)
        {
            type = PossibleMoveType.Simple;

            if (tile.IsNull() || otherTile.IsNull())
            {
                if (tile.IsNull() && !otherTile.IsNull())
                {
                    if (otherTile.IsTriggeredByTap && !otherTile.IsAnyOf(TileState.ArmorMods))
                    {
                        return true;
                    }
                } else if (!tile.IsNull())
                {
                    if (tile is ColorBombTile)
                    {
                        return false;
                    }

                    if (tile.IsTriggeredByTap && !tile.IsAnyOf(TileState.ArmorMods))
                    {
                        return true;
                    }
                }

                return false;
            }

            if (!tile.IsSwappable || !otherTile.IsSwappable)
                return false;

            var tileSpeciality = tile.Speciality;
            var otherTileSpeciality = otherTile.Speciality;

            TileSpeciality firstTileSpeciality, secondTileSpeciality;
            if (TileSpecialityHelper.TileSpecialityComparison(tileSpeciality, otherTileSpeciality))
            {
                firstTileSpeciality = tileSpeciality;
                secondTileSpeciality = otherTileSpeciality;
            }
            else
            {
                firstTileSpeciality = otherTileSpeciality;
                secondTileSpeciality = tileSpeciality;
            }

            switch (firstTileSpeciality)
            {
                case TileSpeciality.None:
                    return false;

                case TileSpeciality.RowBreaker:
                case TileSpeciality.ColumnBreaker:
                    switch (secondTileSpeciality)
                    {
                        case TileSpeciality.RowBreaker:
                        case TileSpeciality.ColumnBreaker:
                            type = PossibleMoveType.LineBreaker;
                            return true;
                    }
                    break;

                case TileSpeciality.Bomb:
                    switch (secondTileSpeciality)
                    {
                        case TileSpeciality.RowBreaker:
                        case TileSpeciality.ColumnBreaker:
                        case TileSpeciality.Bomb:
                            type = PossibleMoveType.Bomb;
                            return true;
                    }
                    break;

                case TileSpeciality.ColorBomb:
                    switch (secondTileSpeciality)
                    {
                        case TileSpeciality.None:
                        case TileSpeciality.RowBreaker:
                        case TileSpeciality.ColumnBreaker:
                        case TileSpeciality.Bomb:
                        case TileSpeciality.ColorBomb:
                        case TileSpeciality.Propeller:
                            type = PossibleMoveType.ColorBomb;
                            return true;
                    }
                    break;
                case TileSpeciality.Propeller:
                    switch (secondTileSpeciality)
                    {

                        case TileSpeciality.None:
                        case TileSpeciality.RowBreaker:
                        case TileSpeciality.ColumnBreaker:
                        case TileSpeciality.Bomb:
                        case TileSpeciality.ColorBomb:
                        case TileSpeciality.Propeller:
                            type = PossibleMoveType.Propeller;
                            return true;
                    }
                    break;

            }

            if (tile.IsTriggeredByTap && tile is not ColorBombTile)
                return true;

            if (otherTile.IsTriggeredByTap && otherTile is not ColorBombTile)
                return true;

            return false;
        }
    }
}
