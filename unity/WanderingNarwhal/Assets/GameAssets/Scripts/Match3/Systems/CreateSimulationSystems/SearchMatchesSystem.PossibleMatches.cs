using System.Collections.Generic;
using BBB.Match3.Systems.CreateSimulationSystems.SearchMatchesSystemTypes;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public static partial class SearchMatchesSystem
    {
        private static readonly List<CardinalDirections> DirectionsToCheckForSimple = new List<CardinalDirections>
        {
            CardinalDirections.N,
            CardinalDirections.E,
        };

        private static readonly List<CardinalDirections> DirectionsToCheckForSpecial = new List<CardinalDirections>
        {
            CardinalDirections.N,
            CardinalDirections.E,
            CardinalDirections.S,
            CardinalDirections.W
        };

        public static HashSet<PossibleMove> SearchForAllPossibleMoves(Grid grid)
        {
            var possibleMatches = new HashSet<PossibleMove>();
            SearchForAllSimplePossibleMovesNoAlloc(grid, possibleMatches);
            SearchForAllSpecialPossibleMovesNoAlloc(grid, possibleMatches);
            return possibleMatches;
        }
        
        public static void SearchForAllPossibleMovesNoAlloc(Grid grid, ref HashSet<PossibleMove> possibleMoves)
        {
            SearchForAllSimplePossibleMovesNoAlloc(grid, possibleMoves);
            SearchForAllSpecialPossibleMovesNoAlloc(grid, possibleMoves);
        }

        public static HashSet<PossibleMove> SearchForAllSimplePossibleMoves(Grid grid)
        {
            var possibleMatches = new HashSet<PossibleMove>();
            SearchForAllSimplePossibleMovesNoAlloc(grid, possibleMatches);
            return possibleMatches;
        }

        private static void SearchForAllSimplePossibleMovesNoAlloc(Grid grid, HashSet<PossibleMove> possibleMatches)
        {
            foreach (var cell in grid.Cells)
            {
                foreach (var cardinalDirection in DirectionsToCheckForSimple)
                {
                    var newCellCoords = cell.Coords.GoSingleCardinalDirection(cardinalDirection);
                    if (!grid.TryGetCell(newCellCoords, out var newCell))
                        continue;

                    if (TryFindPossibleMoveBetweenCells(grid, cell, newCell, out var possibleMatch))
                        possibleMatches.Add(possibleMatch);
                }
            }
        }
        
        public static bool SearchForAnyPossibleMove(this Grid grid, Cell cell)
        {
            foreach (var cardinalDirection in DirectionsToCheckForSimple)
            {
                var newCellCoords = cell.Coords.GoSingleCardinalDirection(cardinalDirection);
                
                if (!grid.TryGetCell(newCellCoords, out Cell newCell)) 
                    continue;

                if (TryFindPossibleMoveBetweenCells(grid, cell, newCell, out PossibleMove possibleMatch))
                    return true;
            }

            return false;
        }



        public static bool SearchForAnyPossibleMove(Grid grid, bool leaveOutColorBomb = false)
        {
            return SearchForAnySpecialPossibleMove(grid, leaveOutColorBomb) || SearchForAnySimplePossibleMove(grid, leaveOutColorBomb);
        }

        public static bool SearchForAnySimplePossibleMove(Grid grid, bool leaveOutColorBomb = false)
        {
            foreach (var cell in grid.Cells)
            {
                if (cell.Tile is null) continue;
                if (!cell.IsBaseCellMatchable()) continue;

                foreach (var cardinalDirection in DirectionsToCheckForSimple)
                {
                    var newCellCoords = cell.Coords.GoSingleCardinalDirection(cardinalDirection);
                    
                    if(!grid.TryGetCell(newCellCoords, out var newCell)) continue;

                    PossibleMove possibleMatch;
                    if (TryFindPossibleMoveBetweenCells(grid, cell, newCell, out possibleMatch, leaveOutColorBomb))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        private static HashSet<PossibleMove> SearchForAllSpecialPossibleMoves(Grid grid)
        {
            var possibleMatches = new HashSet<PossibleMove>();
            SearchForAllSpecialPossibleMovesNoAlloc(grid, possibleMatches);
            return possibleMatches;
        }

        private static void SearchForAllSpecialPossibleMovesNoAlloc(Grid grid, HashSet<PossibleMove> possibleMatches)
        {
            foreach (var cell in grid.Cells)
            {
                if (!cell.IsBaseCellMatchable()) continue;
                var tile = cell.Tile;
                if (!tile.IsSpecialMatchable()) continue;

                foreach (var cardinalDirection in DirectionsToCheckForSpecial)
                {
                    var newCellCoords = cell.Coords.GoSingleCardinalDirection(cardinalDirection);

                    Cell newCell;
                    if (!grid.TryGetCell(newCellCoords, out newCell)) continue;

                    if (!newCell.IsBaseCellMatchable() || cell.HasWallTo(newCellCoords) || newCell.HasWallTo(cell.Coords))
                        continue;

                    var newTile = newCell.Tile;

                    if (newCell.CanAcceptTile() || newTile.IsSpecialMatchable())
                    {
                        if (GravitySystem.CheckForSpecialMove(tile, newTile, out var type))
                        {
                            possibleMatches.Add(new PossibleMove(cell, newCell, null, type));
                        }
                    }
                }

                if (tile.IsTriggeredByTap && !tile.IsAnyOf(TileState.ArmorMods))
                {
                    possibleMatches.Add(new PossibleMove(cell, cell, null, PossibleMoveType.DoubleTap));
                }
            }
        }

        public static bool SearchForAnySpecialPossibleMove(Grid grid, bool leaveOutColorBomb = false)
        {
            foreach (var cell in grid.Cells)
            {
                var tile = cell.Tile;
                if (tile is null) continue;
                if (!cell.IsBaseCellMatchable()) continue;
                if (!tile.IsSpecialMatchable()) continue;

                if(leaveOutColorBomb && tile.Speciality == TileSpeciality.ColorBomb)
                    continue;

                if (tile.IsTriggeredByTap && !tile.IsAnyOf(TileState.ArmorMods))
                {
                    return true;
                }

                foreach (var cardinalDirection in DirectionsToCheckForSpecial)
                {
                    var newCellCoords = cell.Coords.GoSingleCardinalDirection(cardinalDirection);

                    Cell newCell;
                    if (!grid.TryGetCell(newCellCoords, out newCell)) continue;

                    if (!newCell.IsBaseCellMatchable() || cell.HasWallTo(newCellCoords) || newCell.HasWallTo(cell.Coords))
                        continue;

                    var newTile = newCell.Tile;
                    
                    if(leaveOutColorBomb && newTile is not null && newTile.Speciality == TileSpeciality.ColorBomb)
                        continue;

                    if (newCell.CanAcceptTile() || newTile.IsSpecialMatchable())
                    {
                        if (GravitySystem.CheckForSpecialMove(tile, newTile, out _))
                        {
                            return true;
                        }
                    }
                }
            }

            return false;
        }

        public static bool TryFindPossibleMoveBetweenCells(
            Grid grid, Cell firstCell, Cell secondCell, out PossibleMove possibleMatch, bool leaveOutColorBomb = false)

        {
            var firstTile = firstCell.Tile;
            var secondTile = secondCell.Tile;

            if (!firstCell.IsBaseCellSwappable() || !secondCell.IsBaseCellSwappable() || firstCell.HasWallTo(secondCell.Coords) || secondCell.HasWallTo(firstCell.Coords))
            {
                possibleMatch = default;
                return false;
            }
            
            // First tile must not be null; second tile may be null.
            // Swap action requires the first tile to be present.
            if (firstTile.IsNull())
            {
                possibleMatch = default;
                return false;
            }
            
            if (firstTile.IsNull() && secondTile.IsNull())
            {
                possibleMatch = default;
                return false;
            }

            if (!firstTile.IsNull() && !firstTile.IsSwappable)
            {
                possibleMatch = default;
                return false;
            }

            if (!secondTile.IsNull() && !secondTile.IsSwappable)
            {
                possibleMatch = default;
                return false;
            }

            if (leaveOutColorBomb && (!firstTile.IsNull() && firstTile.Speciality == TileSpeciality.ColorBomb || !secondTile.IsNull() && secondTile.Speciality == TileSpeciality.ColorBomb))
            {
                possibleMatch = default;
                return false;
            }

            firstCell.SwapTileWith(secondCell);

            HashSet<Match> matches = new HashSet<Match>();
            FindMatchesForCells(grid, firstCell, secondCell, ref matches);

            firstCell.SwapTileWith(secondCell);

            if (matches == null || matches.Count == 0)
            {
                possibleMatch = default;
                return false;
            }

            possibleMatch = new PossibleMove(firstCell, secondCell, matches);
            return true;
        }
    }
}
