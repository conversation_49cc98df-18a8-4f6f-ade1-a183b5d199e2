using System;
using System.Collections.Generic;
using BBB.Audio;
using BBB.Match3.Logic;
using BebopBee.Core;
using BebopBee.Core.Audio;
using UniRx;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionBomb : Match3ActionBase
    {
        private readonly int _bombId;
        private readonly Coords _coords;
        private readonly TileKinds _kind;
        private readonly HitWaitParams _hitWaitParams;
        private readonly int _radius;
        private readonly List<Coords> _damageArea;
        private readonly bool _isFromBoltOrBomb;

        public ActionBomb(int bombId, Coords coords, int radius, TileKinds kind, List<Coords> damageArea, HitWaitParams hitWaitParams)
        {
            _bombId = bombId;
            _coords = coords;
            _radius = radius;
            _kind = kind;
            _hitWaitParams = hitWaitParams;
            _isFromBoltOrBomb = hitWaitParams != null && 
                                (hitWaitParams.DamageSource & (DamageSource.RemoveColorTiles | DamageSource.AnyBomb)) != 0;
            _damageArea = damageArea != null ? new List<Coords>(damageArea) : null;
            AffectedCoords.Add(_coords);
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ReleasedCoords.Add(_coords);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var bombBusyTime = proxy.Settings.BombBusyTime;
            var minX = int.MaxValue;
            var maxX = int.MinValue;
            var minY = int.MaxValue;
            var maxY = int.MinValue;
            foreach (var coord in _damageArea)
            {
                if (coord.X < minX)
                    minX = coord.X;

                if (coord.X > maxX)
                    maxX = coord.X;

                if (coord.Y < minY)
                    minY = coord.Y;

                if (coord.Y > maxY)
                    maxY = coord.Y;
            }

            var waitUntil = new Func<bool>(() => true);
            
            if (_hitWaitParams != null)
            {
                var waitCondition = ActionWaitConditionFactory.Create(_hitWaitParams);
                waitUntil = () => !waitCondition.WaitForExpectedState(grid, proxy);
            }

            if (waitUntil())
            {
                SpawnBomb();
            }
            else
            {
                // Do not spawn bomb until waiting condition is met
                MainThreadDispatcher.StartUpdateMicroCoroutine(Rx.WaitUntil(
                    waitUntil, SpawnBomb));
            }

            return;

            void SpawnBomb()
            {
                if (_radius == 2)
                {
                    AudioProxy.PlaySound(Match3SoundIds.SquareBomb);
                }
                proxy.TileTickPlayer.BoardObjectFactory.MarkDamageSourceSpawned(TileAsset.Bomb, _bombId);
                proxy.FXRenderer.SpawnBomb(grid, _coords, _radius, _kind, !_isFromBoltOrBomb);
    
                proxy.TileTickPlayer.BoardObjectFactory.CreateOccupiersOver(bombBusyTime, 
                    new Coords(minX, minY), new Coords(maxX, maxY));
                
                ReleasedCoords.Add(_coords);
            }
        }

        protected override string GetMembersString()
        {
            return $"coords={_coords}";
        }
    }
}