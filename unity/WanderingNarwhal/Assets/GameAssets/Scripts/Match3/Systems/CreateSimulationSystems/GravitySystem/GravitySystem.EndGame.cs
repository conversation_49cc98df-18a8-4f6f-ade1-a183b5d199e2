using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;
using BBB.Match3.Systems.GoalsService;
using BBB.RaceEvents;
using GameAssets.Scripts.Match3.Settings;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public partial class GravitySystem
    {
        private abstract class EndGameActionBase
        {
            public bool Completed { get; protected set; }

            public abstract void Execute(GravitySystem gravitySystem, Grid grid, SimulationInputParams inputParams,
                GoalsSystem goalSystem,
                SettleTilesSystem settleTilesSystem, TileResourceSelector tileResources, int remainingMoves);
        }

        private class EndGameTellAboutVictoryAction : EndGameActionBase
        {
            public override void Execute(GravitySystem gravitySystem, Grid grid,
                SimulationInputParams inputParams, GoalsSystem goalSystem,
                SettleTilesSystem settleTilesSystem,
                TileResourceSelector tileResources, int remainingMoves)
            {
                gravitySystem.AddAction(new ActionEndTurnCheckpoint());
                gravitySystem.IncrementCurrentTurn();

                Completed = true;
            }
        }

        private class EndGameTryUsingSuperBoostAction : EndGameActionBase
        {
            private List<Cell> _hitCells;
            private List<LightningRecord> _lightningRecords;
            private PlayerInputItemRain _rainInput;

            public override void Execute(GravitySystem gravitySystem, Grid grid,
                SimulationInputParams inputParams, GoalsSystem goalSystem,
                SettleTilesSystem settleTilesSystem,
                TileResourceSelector tileResources, int remainingMoves)
            {
                if (SuperBoostSystem.IsAllowedToUse())
                {
                    Coords? coords = null;
                    var appropriateCells = AutoBoosterInputFactory.GetAppropriateCells(grid, true);
                    if (appropriateCells.Count > 0)
                        coords = appropriateCells.DeterministicRandomInSelf().Coords;

                    gravitySystem.AddAction(new ActionUseSuperBoost(coords));
                    if (coords.HasValue)
                    {
                        Cell oldCell = grid.GetCell(coords.Value);
                        var tileOrigin = new TileOrigin(Creator.EndGame, oldCell);
                        var spawnedTile = TileFactory.CreateTile(grid.TilesSpawnedCount++, TileAsset.ColorBomb, tileOrigin);
                        oldCell.ReplaceTile(spawnedTile);
                        gravitySystem.AddAction(new ActionReplace(oldCell.Coords, spawnedTile));
                    }

                    gravitySystem.IncrementCurrentTurn();
                }

                Completed = true;
            }
        }

        private class EndGameDestroyBoostsAction : EndGameActionBase
        {
            private List<Cell> _finalizableCells;
            private readonly bool _playStarsWave;
            
            public EndGameDestroyBoostsAction(bool playStarsWave)
            {
                _playStarsWave = playStarsWave;
            }
            
            public override void Execute(GravitySystem gravitySystem, Grid grid,
                SimulationInputParams inputParams, GoalsSystem goalSystem,
                SettleTilesSystem settleTilesSystem,
                TileResourceSelector tileResources, int remainingMoves)
            {
                if (_finalizableCells == null)
                {
                    //step 2 star wave
                    if (_playStarsWave)
                    {
                        gravitySystem.AddAction(new ActionStarWave());
                    }
                    
                    gravitySystem.IncrementCurrentTurn();

                    //step 3 finalize existing special tiles
                    _finalizableCells = new List<Cell>();
                    foreach (var cell in grid.Cells)
                    {
                        if (gravitySystem.CellContainsFinalizableTile(cell))
                        {
                            _finalizableCells.Add(cell);
                        }
                    }
                }
                
                if (_finalizableCells is { Count: > 0 })
                {
                    var cell = _finalizableCells[0];
                    _finalizableCells.RemoveAtSwapBack(0);

                    if (!cell.HasTile() || !cell.Tile.IsBoost)
                    {
                        Completed = true;
                        return;
                    }

                    var hit = new Hit<Tile>(
                        immuneTiles: null,
                        target: cell.Tile,
                        busyWait: M3Constants.BusyTime,
                        damageSource: DamageSource.EndGame,
                        coords: cell.Coords);

                    gravitySystem.Queue.AppendSingleHitForEndTiles(hit);
                    gravitySystem.IncrementCurrentTurn();
                }
                else
                {
                    Completed = true;
                }
            }
        }

        private class EndGameCometsAction : EndGameActionBase
        {
            public override void Execute(GravitySystem gravitySystem, Grid grid,
                SimulationInputParams inputParams, GoalsSystem goalSystem,
                SettleTilesSystem settleTilesSystem,
                TileResourceSelector tileResources, int remainingMoves)
            {
                //step 4 spawn breaker tiles from moves
                gravitySystem.AddAction(new ActionDimBoard(true));
                gravitySystem.IncrementCurrentTurn();

                var cells = grid.Cells;

                var allSimpleTileCells = new HashSet<Cell>();
                foreach (var cell in cells)
                {
                    if (cell.Tile?.IsRegularTile ?? false)
                    {
                        allSimpleTileCells.Add(cell);
                    }
                }

                if (remainingMoves > 0)
                {
                    // Changing random simple tiles into special tiles
                    for (var i = 0; i < remainingMoves; i++)
                    {
                        if (allSimpleTileCells.Count == 0)
                            break;

                        var oldCell = allSimpleTileCells.DeterministicRandomInSelf();

                        var tileOrigin = new TileOrigin(Creator.EndGame, oldCell);

                        Tile spawnedTile = null;
                        var spawnBomb = RandomSystem.Range(0f, 1f) < inputParams.Settings.ChanceToSpawnBombInBonusTime;

                        if (spawnBomb)
                        {
                            spawnedTile = TileFactory.CreateTile(grid.TilesSpawnedCount++, TileAsset.Bomb, tileOrigin);
                        }
                        else
                        {
                            var tileAsset = EndGameSettings.EndGameConversionTypes.DeterministicRandomInSelf();
                            spawnedTile = TileFactory.CreateTile(grid.TilesSpawnedCount++, tileAsset, tileOrigin);
                        }

                        allSimpleTileCells.Remove(oldCell);

                        oldCell.ReplaceTile(spawnedTile);

                        if (goalSystem.RaceEventMatch3Manager != null && goalSystem.RaceEventMatch3Manager.IsAnyRaceEventOfType(RaceEventTypes.RaceEventType.Collect, out var _))
                        {
                            gravitySystem.AddAction(new ActionSpawnDiscoRushCollect(oldCell.Coords, 0.1f,
                                inputParams.Settings.DiscoRushEndGameSpawn));
                            goalSystem.RaceEventMatch3Manager.AddScore(inputParams.Settings.DiscoRushEndGameSpawn);
                        }

                        gravitySystem.AddAction(new ActionComet(oldCell.Coords));
                        gravitySystem.AddAction(new ActionReplace(oldCell.Coords, spawnedTile));
                        gravitySystem.IncrementCurrentTurn();
                    }
                }

                gravitySystem.AddAction(new ActionDimBoard(false));
                //step 5 finilize breaker tiles
                var cellContainsNeighbourBreaker = new List<Cell>();
                foreach (var cell in cells)
                {
                    if (gravitySystem.CellContainsNeighbourBreaker(cell))
                    {
                        cellContainsNeighbourBreaker.Add(cell);
                    }
                }

                var bombHits = new List<Hit<Tile>>();
                var otherHits = new List<Hit<Tile>>();

                foreach (var cell in cellContainsNeighbourBreaker)
                {
                    var hit = new Hit<Tile>(
                        immuneTiles: null,
                        target: cell.Tile,
                        busyWait: M3Constants.BusyTime,
                        damageSource: DamageSource.EndGame,
                        coords: cell.Coords
                    );

                    if (cell.Tile?.Speciality == TileSpeciality.Bomb)
                    {
                        bombHits.Add(hit);
                    }
                    else
                    {
                        otherHits.Add(hit);
                    }
                }
                
                bombHits.AddRange(otherHits);
                var hits = new HashSet<Hit<Tile>>(bombHits);

                gravitySystem.Queue.AppendHitsForEndTiles(hits);

                Completed = true;
            }
        }

        private bool CellContainsFinalizableTile(Cell c)
        {
            return c.HasTile() && c.Tile.IsBoost;
        }

        private bool CellContainsNeighbourBreaker(Cell c)
        {
            return c.HasTile() && c.Tile.IsRegularBoost;
        }
    }
}