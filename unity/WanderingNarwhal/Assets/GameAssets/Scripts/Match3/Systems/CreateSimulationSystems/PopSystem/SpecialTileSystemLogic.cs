using System;
using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes.AdditionalHitInfos;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;
using BBB.Match3.Systems.GoalsService;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public static class SpecialTileSystemLogic
    {
        private static int _lineBreakerIdCounter;
        private static TileHitReactionHandler _reactionHandler;

        public static void Init(TileHitReactionHandler reactionHandler)
        {
            _reactionHandler = reactionHandler;
        }

        public static void Uninit()
        {
            _reactionHandler = null;
        }

        private static readonly List<(CardinalDirections, Coords)> TempCoordsWithTimeList = new List<(CardinalDirections, Coords)>(9);

        public static void ClearAndBusyCell(this Queue q, SimulationInputParams inputParams, Grid grid,
            GoalsSystem goalSystem, Cell cell, IRootSimulationHandler handler,
            HitWaitParams hitWaitParams = null, int busyTime = 0, bool applyCellStateHit = false)
        {
            if (cell == null)
                return;
            var tile = cell.Tile;

            if (!tile.IsNull())
            {
                if (!tile.IsDead)
                {
                    _reactionHandler.RegisterTileShouldReactOnDie(grid, handler, null, cell.Coords, tile);

                    if(tile.IsInTransition)
                        handler.FinalHandleMovementAction(tile, cell.Coords);
                    
                    handler.AddAction(new ActionRemoveTile(cell.Coords, cell.Tile, hitWaitParams));
                }
                
                if (applyCellStateHit && hitWaitParams != null && tile.IsAffectingCellState(hitWaitParams.DamageSource))
                {
                    PopSystem.TryApplyCellStateHit(inputParams, grid, cell, _reactionHandler, handler,
                        goalSystem, false, true, hitWaitParams);
                }
            }

            if (busyTime > 0)
                q.AddBusyCell(cell);

            cell.HardRemoveTile(busyTime, hitWaitParams);
        }

        /// <summary>
        /// Creating coords hits in specific directions
        /// </summary>
        /// <param name="immuneTiles">Immune tiles</param>
        /// <param name="grid"></param>
        /// <param name="coords">Start coords</param>
        /// <param name="cardinalDirections">Directions in which the hits will go</param>
        /// <param name="busyTime">Damage delay. Example witout delay:3210123</param>
        /// <returns>Damaged coords</returns>
        public static Queue PopLineBreaker(
            HashSet<Tile> immuneTiles,
            IRootSimulationHandler handler,
            Grid grid,
            Coords coords,
            CardinalDirections cardinalDirections,
            TileSpeciality tileSpeciality,
            HitWaitParams hitWaitParams)
        {
            var cell = grid.GetCell(coords);

            // Adding effect start cell:
            var damagedCellsWithDelay = new Queue(context: "PopLineBreaker");
            
            int delay = 0;
            var id = _lineBreakerIdCounter++;
            
            if (cell != null)
            {
                if (!cell.Tile.IsNull() || cell.HasMultiSizeCellReference() || cell.IsAnyOf(CellState.Ivy))
                {
                    var damageSource = cardinalDirections.IsAnyOf(CardinalDirections.E | CardinalDirections.W)
                        ? DamageSource.LineBreakerArrowHor
                        : DamageSource.LineBreakerArrowVer;

                    var lbInfo = new LineBreakerInfo(id, cardinalDirections.IsAnyOf(CardinalDirections.E | CardinalDirections.W) ? CardinalDirections.E : CardinalDirections.N, cell.Coords);
                    var hit = new Hit<Cell>(
                        immuneTiles: immuneTiles,
                        target: cell,
                        busyWait: M3Constants.BusyTimeLineBreaker,
                        damageSource: damageSource,
                        coords: cell.Coords,
                        boostInfo: lbInfo,
                        hitSourceUid: id);

                    damagedCellsWithDelay.AppendHit(delay, hit);
                }
            }

            var distances = new Dictionary<CardinalDirections, int>();

            MoveLinebreakerOneStep(grid, coords, id, cardinalDirections, damagedCellsWithDelay, immuneTiles, distances, delay, hitWaitParams?.HitDistance ?? 0);
            
            handler?.AddAction(new ActionLinebreakers(coords, distances, tileSpeciality, id, hitWaitParams));

            return damagedCellsWithDelay;
        }

        private static void MoveLinebreakerOneStep(Grid grid,
            Coords originCoords, int id, CardinalDirections directions,
            Queue damagedCellsWithDelay,
            HashSet<Tile> immuneTiles, Dictionary<CardinalDirections, int> distances, int delay, int hitDistance)
        {
            float oldArrowPosition = 0f;
            Coords previousCoords = originCoords;

            var damageSource = directions.IsAnyOf(CardinalDirections.E | CardinalDirections.W)
                ? DamageSource.LineBreakerArrowHor
                : DamageSource.LineBreakerArrowVer;

            for (var i = 0;; i++)
            {
                var newArrowPosition = oldArrowPosition + 1;

                lock (TempCoordsWithTimeList)
                {
                    TempCoordsWithTimeList.Clear();

                    var directionsCount = 0;
                    if (directions.Contains(CardinalDirections.N))
                    {
                        GetCoordsBetweenArrowPositions(originCoords, CardinalDirections.N, oldArrowPosition, newArrowPosition);
                        directionsCount++;
                    }

                    if (directions.Contains(CardinalDirections.E))
                    {
                        GetCoordsBetweenArrowPositions(originCoords, CardinalDirections.E, oldArrowPosition, newArrowPosition);
                        directionsCount++;
                    }

                    if (directions.Contains(CardinalDirections.S))
                    {
                        GetCoordsBetweenArrowPositions(originCoords, CardinalDirections.S, oldArrowPosition, newArrowPosition);
                        directionsCount++;
                    }

                    if (directions.Contains(CardinalDirections.W))
                    {
                        GetCoordsBetweenArrowPositions(originCoords, CardinalDirections.W, oldArrowPosition, newArrowPosition);
                        directionsCount++;
                    }

                    if (TempCoordsWithTimeList.Count == 0) 
                        return;

                    foreach (var (singleDirection, coord) in TempCoordsWithTimeList)
                    {
                        if (coord == previousCoords) continue;
                        previousCoords = coord;

                        if (grid.TryGetCell(coord, out var newCell))
                        {
                            var lbInfo = new LineBreakerInfo(id, singleDirection, originCoords);

                            var distance = (int) Math.Abs((coord.ToUnityVector2() - originCoords.ToUnityVector2()).magnitude);
                            var newHit = new Hit<Cell>(
                                immuneTiles: immuneTiles,
                                target: newCell,
                                busyWait: M3Constants.BusyTimeLineBreaker,
                                damageSource: damageSource,
                                boostInfo: lbInfo,
                                coords: newCell.Coords,
                                hitSourceUid: id,
                                hitDistance: hitDistance + distance);

                            damagedCellsWithDelay.AppendHit(delay, newHit);
                        }

                        if (grid.IsOutsideGridBounds(coord.X, coord.Y) && !distances.ContainsKey(singleDirection))
                        {
                            distances.Add(singleDirection, coord.DistanceBetweenCoordsOnTheSameLine(originCoords));
                            if (distances.Count == directionsCount) return;
                        }
                    }
                }

                oldArrowPosition = newArrowPosition;
            }
        }

        private static void GetCoordsBetweenArrowPositions(Coords coords, CardinalDirections singleDirection,
            float oldArrowPosition, float newArrowPosition)
        {
            var start = Mathf.FloorToInt(oldArrowPosition);
            var count = Mathf.Abs(Mathf.FloorToInt(newArrowPosition - oldArrowPosition));
            if (count == 0)
            {
                return;
            }

            if (count > TempCoordsWithTimeList.Capacity)
            {
                TempCoordsWithTimeList.Capacity = count;
            }

            for (var n = start; n < start + count; n++)
            {
                var element = coords + new Coords(
                    singleDirection switch
                    {
                        CardinalDirections.E => n,
                        CardinalDirections.W => -n,
                        _ => 0
                    },
                    singleDirection switch
                    {
                        CardinalDirections.N => n,
                        CardinalDirections.S => -n,
                        _ => 0
                    });

                TempCoordsWithTimeList.Add((singleDirection, element));
            }
        }
    }
}