using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.GoalsService;
using BBB.UI.Level;
using GameAssets.Scripts.Match3.Settings;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public partial class GravitySystem
    {
        private Queue CalculateSpecialMatch(HashSet<Tile> immuneTiles, Grid grid,
            SimulationInputParams inputParams, GoalsSystem goalSystem, Cell someCell, Cell otherCell)
        {
            Queue result = null;

            if (!someCell.HasTile() || !otherCell.HasTile())
                return null;

            var someTileSpeciality = someCell.Tile.Speciality;
            var otherTileSpeciality = otherCell.Tile.Speciality;

            var firstCell = TileSpecialityHelper.TileSpecialityComparison(someTileSpeciality, otherTileSpeciality) ? someCell : otherCell;
            var secondCell = TileSpecialityHelper.TileSpecialityComparison(someTileSpeciality, otherTileSpeciality) ? otherCell : someCell;

            var firstTile = firstCell.Tile;
            var secondTile = secondCell.Tile;

            var firstTileSpeciality = firstTile.Speciality;
            var secondTileSpeciality = secondTile.Speciality;

            switch (firstTileSpeciality)
            {
                case TileSpeciality.None:
                    break;
                case TileSpeciality.RowBreaker:
                case TileSpeciality.ColumnBreaker:
                    switch (secondTileSpeciality)
                    {
                        case TileSpeciality.RowBreaker:
                        case TileSpeciality.ColumnBreaker:
                            // Create cross on the second
                            AddAction(
                                new ActionCharacterAnimationPlay(Match3CharacterAnim.Celebration_03));
                            result = SpecialTileSystem.PopCross(immuneTiles, inputParams, this, grid, goalSystem,
                                otherCell, someCell);
                            break;
                    }

                    break;

                case TileSpeciality.Bomb:
                    switch (secondTileSpeciality)
                    {
                        case TileSpeciality.RowBreaker:
                        case TileSpeciality.ColumnBreaker:
                            AddAction(
                                new ActionCharacterAnimationPlay(Match3CharacterAnim.Celebration_06));
                            AddAction(new ActionPowerUpVoiceover());
                            result = SpecialTileSystem.PopTripleCross(immuneTiles, inputParams, this, grid,
                                goalSystem, otherCell, someCell, secondTileSpeciality);
                            break;
                        case TileSpeciality.Bomb:
                            AddAction(
                                new ActionCharacterAnimationPlay(Match3CharacterAnim.Celebration_04));
                            AddAction(new ActionPowerUpVoiceover());
                            AddAction(new ActionBombBombCombine(otherCell.Coords, someCell.Coords));
                            result = SpecialTileSystem.PopSquareClearer(
                                events: this,
                                grid: grid,
                                inputParams: inputParams,
                                goalSystem: goalSystem,
                                targetCell: otherCell,
                                otherCell: someCell,
                                radius: M3Constants.ExtendedBombRadius);

                            if (result != null)
                            {
                                result.ClearAndBusyCell(inputParams, grid, goalSystem, otherCell, this, null, M3Constants.BusyTimeBomb);
                                result.ClearAndBusyCell(inputParams, grid, goalSystem, someCell, this, null, M3Constants.BusyTimeBomb);
                            }

                            break;
                    }

                    break;

                case TileSpeciality.ColorBomb:
                    switch (secondTileSpeciality)
                    {
                        case TileSpeciality.None:
                            // Remove all simple tiles
                            var swapTileKind = secondCell.Tile.Kind;
                            var topTileKinds = grid.GetTopTileKinds();

                            topTileKinds.Remove(swapTileKind);
                            topTileKinds.Insert(0, swapTileKind);
                            
                            result = SpecialTileSystem.RemoveAllColorTiles(immuneTiles, inputParams,
                                this, grid, goalSystem, _settleTileSystem, firstCell, topTileKinds);
                            break;
                        case TileSpeciality.RowBreaker:
                        case TileSpeciality.ColumnBreaker:
                            // Random convert all to line breakers
                            AddAction(new ActionCharacterAnimationPlay(Match3CharacterAnim.Celebration_07));
                            result = SpecialTileSystem.ChangeAllColorTilesIntoAndHit(immuneTiles, this, grid, inputParams,
                                goalSystem, _settleTileSystem, firstCell, secondCell, grid.GetTopTileKinds());
                            break;
                        case TileSpeciality.Propeller:
                            AddAction(new ActionCharacterAnimationPlay(Match3CharacterAnim.Celebration_08));
                            result = SpecialTileSystem.ChangeAllColorTilesIntoAndHit(immuneTiles, this, grid, inputParams,
                                goalSystem, _settleTileSystem, firstCell, secondCell, grid.GetTopTileKinds());
                            break;
                        case TileSpeciality.Bomb:
                            // Random convert all to bombs
                            AddAction(new ActionCharacterAnimationPlay(Match3CharacterAnim.Celebration_08));
                            result = SpecialTileSystem.ChangeAllColorTilesIntoAndHit(immuneTiles, this, grid, inputParams,
                                goalSystem, _settleTileSystem, firstCell, secondCell, grid.GetTopTileKinds());
                            break;
                        case TileSpeciality.ColorBomb:
                            // Remove all tiles
                            AddAction(new ActionCharacterAnimationPlay(Match3CharacterAnim.Celebration_05));
                            AddAction(new ActionBoltBoltCombine(someCell.Coords, otherCell.Coords));
                            AddAction(new ActionPowerUpVoiceover());
                            result = SpecialTileSystem.WhirlpoolAllTiles(this, inputParams, grid,
                                goalSystem, _settleTileSystem, firstCell, secondCell);
                            break;
                    }

                    break;
                case TileSpeciality.Propeller:
                {
                    switch (secondTileSpeciality)
                    {
                        case TileSpeciality.Propeller:
                        {
                            result = new Queue("Propeller+Propeller");

                            var smallCrossQ = SpecialTileSystem.PopSmallCross(
                                immuneTiles: immuneTiles,
                                inputParams: inputParams,
                                events: this,
                                grid: grid,
                                goalSystem: goalSystem,
                                cell: secondCell);
                            result.Append(smallCrossQ);

                            result.ClearAndBusyCell(inputParams, grid, goalSystem, firstCell, this, null, M3Constants.BusyTimeSmallCross);
                            result.ClearAndBusyCell(inputParams, grid, goalSystem, secondCell, this, null, M3Constants.BusyTimeSmallCross);

                            for (var i = 0; i < 2; i++)
                            {
                                var sourceCell = i == 0 ? firstCell : secondCell;
                                var tileId = sourceCell.HasTile() ? sourceCell.Tile.Id : -1;
                                var duplicatesCount = i == 0 ? 1 : 2;

                                for (var j = 0; j < duplicatesCount; j++)
                                {
                                    var removeRandomTileQueue = SpecialTileSystem.RemoveWithPropellerAttack(immuneTiles,
                                        this, _settleTileSystem, _spawnSystem, tileId, sourceCell.Coords, null, i + j);

                                    if (removeRandomTileQueue != null)
                                    {
                                        result.Append(removeRandomTileQueue);
                                    }
                                }
                            }

                            break;
                        }
                        case TileSpeciality.Bomb:
                        case TileSpeciality.ColumnBreaker:
                        case TileSpeciality.RowBreaker:

                            result = new Queue("Propeller+Boost");

                            result.ClearAndBusyCell(inputParams, grid, goalSystem, firstCell, this, null, M3Constants.BusyTimeSmallCross);
                            result.ClearAndBusyCell(inputParams, grid, goalSystem, secondCell, this, null, M3Constants.BusyTimeSmallCross);

                            var smallCross = SpecialTileSystem.PopSmallCross(
                                immuneTiles: immuneTiles,
                                inputParams: inputParams,
                                events: this,
                                grid: grid,
                                goalSystem: goalSystem,
                                cell: otherCell);
                            result.Append(smallCross);

                            var changeQueue = SpecialTileSystem.AttackWithPropellerCombo(
                                this, _settleTileSystem,
                                firstCell.Coords, secondCell.Coords,
                                secondTileSpeciality);

                            if (changeQueue != null)
                            {
                                result.Append(changeQueue);
                            }

                            break;
                    }
                }

                    break;
            }

            return result;
        }
    }
}