using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes.AdditionalHitInfos;

namespace BBB.Match3
{
    public class BombWaitCondition : WaitConditionBase
    {
        private readonly Coords _hitCoords;
        private readonly BombInfo _bombInfo;
        private readonly string _textData;

        public BombWaitCondition(HitWaitParams hitWaitParams) : base(hitWaitParams)
        {
            _hitCoords = hitWaitParams.Coords;
            _bombInfo = hitWaitParams.BombInfo;
            _textData = $"[ coords={_hitCoords} ]";
        }

        // Returns true after bomb with id {_bombInfo.BombId} has exploded
        public override bool WaitForExpectedState(Grid grid, PlaySimulationActionProxy proxy)
        {
            return !proxy.TileTickPlayer.BoardObjectFactory.WasDamageSourceSpawned(TileAsset.Bomb, _bombInfo.BombId);
        }
        
        public override string GetTextData(Grid grid, PlaySimulationActionProxy proxy)
        {
            return _textData;
        }
    }
}