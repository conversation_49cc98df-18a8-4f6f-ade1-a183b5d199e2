namespace BBB.Match3.Systems
{
    public static class SuperBoostConstants
    {
        //general
        public const float ProgressReachedThreshold = 0.98f;
        public const float SuperBoostOverlayTitleDisplayTime = 1.3f;
        public const float SuperBoostOverlayTitleBlockDuration = 0f;
        public const float SecondsToWaitBeforeMakeSuperBoostReady = 0.65f;
        public const float FillSpeed = 0.5f;
        
        //rain
        public const float UmbrellaSoundOffset = 1.5f;

        /// <summary>
        /// Time before Rain FX appear, starting from the time when SuperBoostOverlay spawns.
        /// if value is 0, then rain FX and title overlay will appear at the same time. -VK
        /// </summary>
        public const float PauseBeforeSpawningRainCloud = 1.5f;
        public const float PauseAfterSpawningRainCloud = 0.2f;
        public const float PauseBeforeHidingUmbrella = 2f;
        
        //debug
        public const float DebugSecondsToWaitForLockedInput = 30f; //debug value

        public const string SuperBoostLockId = "superboost";
    }
}