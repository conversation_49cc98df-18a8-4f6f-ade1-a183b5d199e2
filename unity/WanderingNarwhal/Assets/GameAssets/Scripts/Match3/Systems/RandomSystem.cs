using System.Collections.Generic;
using Lidgren.Network;

namespace BBB.Match3.Systems
{
    public static class RandomSystem
    {
        public static uint Seed { get; private set; }

        private static XorShiftRandom _random;

// #if BBB_DEBUG
        public static bool DebugLockSeed { get; set; }
// #endif

        public static void Reset(uint seed = 0)
        {
#if BBB_DEBUG
            if (!DebugLockSeed || Seed == 0)
#endif
            {
                Seed = seed > 0 ? seed : (uint)UnityEngine.Random.Range(0, 999999999);
            }

            _random = new XorShiftRandom();
            _random.Initialize(Seed);
        }

        public static void ShuffleRandomSystem<T>(this List<T> list)
        {
            for (var i = 0; i < list.Count; i++)
            {
                var index = Next(list.Count);
                (list[index], list[i]) = (list[i], list[index]);
            }
        }

        public static int Next(int from, int to)
        {
            return _random.Next(from, to);
        }

        public static int Next(int to)
        {
            return Next(0, to);
        }

        public static float Next(float to)
        {
            return _random.NextSingle() * to;
        }

        public static float Range(float from, float to)
        {
            return from + _random.NextSingle() * (to-from);
        }

        public static float Next()
        {
            return (float)Next(0, 101)/100;
        }

        public static NetRandomGenerationValues GetRandomGenerationValues()
        {
            return _random.GetRandomGenerationValues();
        }

        public static void SetRandomGenerationValues(NetRandomGenerationValues netRandomGenerationValues)
        {
            _random.SetRandomGenerationValues(netRandomGenerationValues);
        }

        public static string SeedToString()
        {
            return "Seed: " + Seed + "\nSeed values: " + GetRandomGenerationValues();
        }
    }
}
