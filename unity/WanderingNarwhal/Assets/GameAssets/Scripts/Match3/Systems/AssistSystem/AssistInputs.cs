using System.Collections.Generic;
using System.Text;

namespace BBB.Match3.Systems.CreateSimulationSystems.AssistSystemTypes
{
    public sealed class AssistInputs
    {
        public readonly Dictionary<AssistInput, AssistInputBase> Inputs;

        public AssistInputs(Dictionary<AssistInput, AssistInputBase> inputs)
        {
            Inputs = inputs;
        }

        public bool Contains(AssistInput assistInput)
        {
            return Inputs.ContainsKey(assistInput);
        }

        public bool TryGetValue(AssistInput assistInput, out AssistInputBase assistInputBase)
        {
            return Inputs.TryGetValue(assistInput, out assistInputBase);
        }

        public override string ToString()
        {
            var stringBuilder = new StringBuilder();

            foreach (var assistInputBase in Inputs)
            {
                const string comma = ",";
                stringBuilder.Append(assistInputBase).Append(comma);
            }

            if (stringBuilder.Length > 0)
            {
                stringBuilder.Length--;
            }

            return stringBuilder.ToString();
        }

    }
}
