using System.Collections.Generic;
using FBConfig;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    
    public class AssistParams
    {
        public class Builder
        {
            private readonly AssistParams _result = new AssistParams();
            
            public Builder SetWinLossValue(float winValue, float lossValue)
            {
                _result.WinValue = winValue;
                _result.LossValue = lossValue;
                return this;
            }

            public Builder SetAssisSystemConfig(AssistSystemConfig assistSystemConfig)
            {
                _result.AssistSystemConfig = assistSystemConfig;
                return this;
            }
            
            public AssistParams GetResult()
            {
                return _result;
            }

            public Builder SetPlayerSkillModifiers(float aimToWinSkillModifier, float aimToLoseSkillModifier)
            {
                _result.AimToWinSkillModifier = aimToWinSkillModifier;
                _result.AimToLoseSkillModifier = aimToLoseSkillModifier;
                return this;
            }
        }
        
        public float AssistValue { get; private set; }  // -1..1
        public float WinValue { get; private set; }     // 0..1
        public float LossValue { get; private set; }    // 0..1
        public float AimToWinSkillModifier { get; private set; }
        public float AimToLoseSkillModifier { get; private set; }
        public static bool AimAtWinningLevel;
        public static bool OriginalAimAtWinningLevel;
        public static bool AssistOnLossStreak;
        public static bool UsingAssistAfterIap;
        public Dictionary<GoalType, float> GoalValueWeights { get; private set; }
        
        public AssistSystemConfig AssistSystemConfig { get; private set; }

        public int MaxAutoMatchesInDefinition => AssistSystemConfig.MaxAutomatches;

        public void ResetAssistValue(float av)
        {
            AssistValue = av;
        }

        public float WinLossValueDiff()
        {
            var diff = WinValue - LossValue;
            diff /= AimAtWinningLevel
                ? AssistSystemConfig.AimingToWin?.WinLossModifier ?? 1 + AimToWinSkillModifier
                : AssistSystemConfig.AimingToLose?.WinLossModifier ?? 1 + AimToLoseSkillModifier; //limit help if user should not win

            return diff;
        }

        public override string ToString()
        {
            return $"AP WV={WinValue:F2} LV={LossValue:F2} SM={(AimAtWinningLevel ? AimToWinSkillModifier : AimToLoseSkillModifier):F2} DIFF={WinLossValueDiff():F2}";
        }
    }

}