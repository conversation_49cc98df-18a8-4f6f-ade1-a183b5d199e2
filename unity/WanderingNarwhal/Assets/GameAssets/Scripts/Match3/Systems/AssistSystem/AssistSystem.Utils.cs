using System;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using BBB.Match3.Systems.CreateSimulationSystems.SearchMatchesSystemTypes;
using BBB.Match3.Systems.GoalsService;
using UnityEngine;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public partial class AssistSystem
    {
        private static HashSet<Coords> _adjacentCoords = new HashSet<Coords>();
            
        private static void ApplyDamageToTile(Cell cell, TileKinds damageKind,
            ref Dictionary<Coords, Tile> tilesToRestore)
        {
            var tile = cell.Tile;
            if (tile == null)
                return;

            switch (tile.Speciality)
            {
                case TileSpeciality.Soda:
                    if (!tilesToRestore.ContainsKey(cell.Coords))
                        tilesToRestore[cell.Coords] = cell.Tile.Clone();

                    var sodaCount = tile.GetParam(TileParamEnum.SodaBottlesCount);
                    var state = tile.GetParam(TileParamEnum.SodaColors);
                    var damageColorNum = SodaTileLayer.ColorToEncodedNum(damageKind);
                    for (var i = 0; i < 4; i++)
                    {
                        var subState = SodaTileLayer.GetColorNumFromState(state, i);
                        if (subState == damageColorNum)
                        {
                            state = SodaTileLayer.SetColorNumInState(state, 0, i);
                            sodaCount--;
                            break;
                        }
                    }

                    tile.SetParam(TileParamEnum.SodaColors, state);
                    tile.SetParam(TileParamEnum.SodaBottlesCount, sodaCount);
                    break;
                
                case TileSpeciality.DynamiteBox:
                    if (!tilesToRestore.ContainsKey(cell.Coords))
                        tilesToRestore[cell.Coords] = cell.Tile.Clone();

                    var sticksCount = tile.GetParam(TileParamEnum.DynamiteSticksCount);
                    var boxState = tile.GetParam(TileParamEnum.DynamiteBoxColors);
                    var damageColorNumOfBox = DynamiteBoxTileLayer.ColorToEncodedNum(damageKind);
                    for (var i = 0; i < 8; i++)
                    {
                        var subState = DynamiteBoxTileLayer.GetColorNumFromState(boxState, i);
                        if (subState == damageColorNumOfBox)
                        {
                            boxState = DynamiteBoxTileLayer.SetColorNumInState(boxState, 0, i);
                            sticksCount--;
                            break;
                        }
                    }

                    tile.SetParam(TileParamEnum.DynamiteBoxColors, boxState);
                    tile.SetParam(TileParamEnum.DynamiteSticksCount, sticksCount);
                    break;
                    
            }
        }

        private static void RestoreTiles(Grid grid, ref Dictionary<Coords, Tile> tilesToRestore)
        {
            foreach (var (coords, tile) in tilesToRestore)
            {
                grid.GetCell(coords).ReplaceTile(tile);
            }

            tilesToRestore.Clear();
        }

        private static bool HasAffectedGoalCoords(Grid grid, Match match, HashSet<Coords> goalCoords,
            ref HashSet<Coords> affectedGoalCoords, ref Dictionary<Coords, Tile> tilesToRestore)
        {
            _adjacentCoords.Clear();
            match.GetMatchAdjacentCoords(ref _adjacentCoords);
            foreach (var coord in _adjacentCoords)
            {
                if (!grid.TryGetCell(coord, out var cell))
                    continue;

                Cell mainCell = cell.GetMainCellReference(out _);


                //cell is not in our goal
                if (!goalCoords.Contains(mainCell.Coords))
                    continue;

                bool isMatchCoord = match.Contains(cell.Coords);
                var tile = mainCell.Tile;
                if (isMatchCoord) //possible background remove or match a goal tile
                {
                    if (mainCell.Tile is null && !mainCell.CanReceiveDirectDamageWhenCellIsEmpty)
                        continue;

                    if (mainCell.BackgroundCount > 0 ||
                        (tile?.CanBeDamagedBy(DamageSource.Match, match.Kind) ?? false))
                        affectedGoalCoords.Add(cell.Coords);
                }
                else //Adjacent damage
                {
                    if (mainCell.Tile is null && !mainCell.CanReceiveAdjacentDamageWhenCellIsEmpty)
                        continue;

                    if (tile == null || tile.CanBeDamagedBy(DamageSource.Adjacent, match.Kind))
                    {
                        ApplyDamageToTile(mainCell, match.Kind, ref tilesToRestore);

                        affectedGoalCoords.Add(cell.Coords);
                    }
                }
            }

            return affectedGoalCoords.Count > 0;
        }

        /// <summary>
        /// Checks if move affects any goal cell
        /// </summary>
        public static bool HasAffectedGoalCoords(Grid grid, PossibleMove move,
            MechanicTargetingManager mechanicTargetingManager,
            HashSet<Coords> goalCoords, GoalsSystem goalsSystem, ref HashSet<Coords> affectedGoalCoords,
            ref Dictionary<Coords, Tile> tilesToRestore)
        {
            affectedGoalCoords.Clear();

            if (move.Matches != null)
            {
                foreach (Match match in move.Matches)
                    HasAffectedGoalCoords(grid, match, goalCoords, ref affectedGoalCoords, ref tilesToRestore);
            }
            
            GetAffectedGoalCoordsForBooster(grid, move, mechanicTargetingManager,
                    goalCoords, goalsSystem, ref affectedGoalCoords, ref tilesToRestore);

            //restore tiles after damage
            RestoreTiles(grid, ref tilesToRestore);
            return affectedGoalCoords.Count > 0;
        }

        private static bool CanDamageCell(Cell cell, DamageSource source, TileKinds kind)
        {
            Cell mainCell = cell.GetMainCellReference(out _);
            if (mainCell.Tile is null && !mainCell.CanReceiveAdjacentDamageWhenCellIsEmpty
                && !mainCell.CanReceiveDirectDamageWhenCellIsEmpty)
                return false;

            return mainCell.Tile == null || mainCell.Tile.CanBeDamagedBy(source, kind);
        }

        private static bool TryDamageGoalCoord(Cell cell, DamageSource damageSource, TileKinds tileKinds,
            HashSet<Coords> goalCoords, ref HashSet<Coords> affectedGoalCoords,
            ref Dictionary<Coords, Tile> tilesToRestore)
        {
            if (cell != null && goalCoords.Contains(cell.Coords) && CanDamageCell(cell, damageSource, tileKinds))
            {
                ApplyDamageToTile(cell, tileKinds, ref tilesToRestore);
                affectedGoalCoords.Add(cell.Coords);
                return true;
            }

            return false;
        }

        private static PossibleMoveType TileSpecialityToMoveType(TileSpeciality speciality)
        {
            return speciality switch
            {
                TileSpeciality.Bomb => PossibleMoveType.Bomb,
                TileSpeciality.Propeller => PossibleMoveType.Propeller,
                TileSpeciality.RowBreaker => PossibleMoveType.LineBreaker,
                TileSpeciality.ColumnBreaker => PossibleMoveType.LineBreaker,
                TileSpeciality.ColorBomb => PossibleMoveType.ColorBomb,
                _ => PossibleMoveType.Simple
            };
        }

        private static void ApplyPropellerDamage(Grid grid, Coords coord, HashSet<Coords> goalCoords,
            ref HashSet<Coords> affectedGoalCoords, ref Dictionary<Coords, Tile> tilesToRestore)
        {
            TryDamageGoalCoord(grid.GetCell(coord), DamageSource.Propeller, TileKinds.None, goalCoords,
                ref affectedGoalCoords, ref tilesToRestore);
            foreach (var cell in grid.GetAdjacentCells(coord))
                TryDamageGoalCoord(cell, DamageSource.Propeller, TileKinds.None, goalCoords,
                    ref affectedGoalCoords, ref tilesToRestore);
        }

        private static void ApplyCrossDamage(Grid grid, Coords coords, HashSet<Coords> goalCoords,
            ref HashSet<Coords> affectedGoalCoords, ref Dictionary<Coords, Tile> tilesToRestore)
        {
            for (int x = 0; x < grid.Width; ++x)
            {
                if (grid.TryGetCell(new Coords(x, coords.Y), out Cell cell))
                    TryDamageGoalCoord(cell, DamageSource.LineBreakerArrowHor, TileKinds.None, goalCoords,
                        ref affectedGoalCoords, ref tilesToRestore);
            }

            for (int y = 0; y < grid.Height; ++y)
            {
                if (grid.TryGetCell(new Coords(coords.X, y), out Cell cell))
                    TryDamageGoalCoord(cell, DamageSource.LineBreakerArrowVer, TileKinds.None, goalCoords,
                        ref affectedGoalCoords, ref tilesToRestore);
            }
        }

        private static void ApplyTripleCrossDamage(Grid grid, Coords coords, HashSet<Coords> goalCoords,
            ref HashSet<Coords> affectedGoalCoords, ref Dictionary<Coords, Tile> tilesToRestore)
        {
            for (int x = 0; x < grid.Width; ++x)
            for (int y = coords.Y - 1; y <= coords.Y + 1; ++y)
            {
                if (grid.TryGetCell(new Coords(x, y), out Cell cell))
                    TryDamageGoalCoord(cell, DamageSource.LineBreakerArrowHor, TileKinds.None, goalCoords,
                        ref affectedGoalCoords, ref tilesToRestore);
            }

            for (int x = coords.X - 1; x <= coords.X + 1; ++x)
            for (int y = 0; y < grid.Height; ++y)
            {
                if (grid.TryGetCell(new Coords(x, y), out Cell cell))
                    TryDamageGoalCoord(cell, DamageSource.LineBreakerArrowVer, TileKinds.None, goalCoords,
                        ref affectedGoalCoords, ref tilesToRestore);
            }
        }

        private static void ApplyBombDamage(Grid grid, Coords coords, HashSet<Coords> goalCoords,
            ref HashSet<Coords> affectedGoalCoords, ref Dictionary<Coords, Tile> tilesToRestore)
        {
            foreach (var cell in grid.GetCellsInSquare(coords, 5))
                TryDamageGoalCoord(cell, DamageSource.Bomb, TileKinds.None, goalCoords, ref affectedGoalCoords,
                    ref tilesToRestore);
        }

        private static void ApplyFullGridDamage(Grid grid, HashSet<Coords> goalCoords,
            ref HashSet<Coords> affectedGoalCoords, ref Dictionary<Coords, Tile> tilesToRestore)
        {
            foreach (var goalCoord in goalCoords)
            {
                Cell cell = grid.GetCell(goalCoord);
                TryDamageGoalCoord(cell, DamageSource.MultiBomb, TileKinds.None, goalCoords, ref affectedGoalCoords,
                    ref tilesToRestore);
            }
        }

        private static TileSpeciality GetSecondSpeciality(PossibleMove move)
        {
            if (move.SecondCell.Tile == null || move.FirstCell.Tile == null)
                return TileSpeciality.None;
            
            switch (move.Type)
            {
                case PossibleMoveType.DoubleTap:
                    return TileSpeciality.None;
                case PossibleMoveType.Simple:
                    return move.SecondCell.Tile.Speciality;
                case PossibleMoveType.Bomb:
                    return move.FirstCell.Tile.Speciality == TileSpeciality.Bomb
                        ? move.SecondCell.Tile.Speciality
                        : move.FirstCell.Tile.Speciality;
                case PossibleMoveType.Propeller:
                    return move.FirstCell.Tile.Speciality == TileSpeciality.Propeller
                        ? move.SecondCell.Tile.Speciality
                        : move.FirstCell.Tile.Speciality;
                case PossibleMoveType.ColorBomb:
                    return move.FirstCell.Tile.Speciality == TileSpeciality.ColorBomb
                        ? move.SecondCell.Tile.Speciality
                        : move.FirstCell.Tile.Speciality;
                case PossibleMoveType.LineBreaker:
                    return move.FirstCell.Tile.Speciality is TileSpeciality.RowBreaker or TileSpeciality.ColumnBreaker
                        ? move.SecondCell.Tile.Speciality
                        : move.FirstCell.Tile.Speciality;
                default:
                    throw new NotImplementedException($"Move type {move.Type} is not implemented!");
            }
        }

        private static void ApplyBombMove(Grid grid, PossibleMove move,
            MechanicTargetingManager mechanicTargetingManager, HashSet<Coords> goalCoords,
            ref HashSet<Coords> affectedGoalCoords, ref Dictionary<Coords, Tile> tilesToRestore)
        {
            if (move.Type == PossibleMoveType.DoubleTap)
            {
                ApplyBombDamage(grid, move.FirstCell.Coords, goalCoords, ref affectedGoalCoords, ref tilesToRestore);
                return;
            }

            var secondSpeciality = GetSecondSpeciality(move);
            switch (secondSpeciality)
            {
                case TileSpeciality.Bomb:
                case TileSpeciality.ColorBomb:
                    ApplyFullGridDamage(grid, goalCoords, ref affectedGoalCoords, ref tilesToRestore);
                    break;
                case TileSpeciality.RowBreaker:
                case TileSpeciality.ColumnBreaker:
                    ApplyTripleCrossDamage(grid, move.SecondCell.Coords, goalCoords, ref affectedGoalCoords,
                        ref tilesToRestore);
                    break;
                case TileSpeciality.Propeller:
                    ApplyPropellerDamage(grid, move.SecondCell.Coords, goalCoords, ref affectedGoalCoords,
                        ref tilesToRestore);
                    var target = mechanicTargetingManager.SelectComboTarget(DamageSource.PropellerCombo, secondSpeciality);
                    ApplyBombDamage(grid, target.Coords, goalCoords, ref affectedGoalCoords, ref tilesToRestore);
                    break;
                default:
                    ApplyBombDamage(grid, move.SecondCell.Coords, goalCoords, ref affectedGoalCoords,
                        ref tilesToRestore);
                    break;
            }
        }

        private static void ApplyLineBreakerDamage(Grid grid, TileSpeciality speciality, Coords coords,
            HashSet<Coords> goalCoords,
            ref HashSet<Coords> affectedGoalCoords, ref Dictionary<Coords, Tile> tilesToRestore)
        {
            if (speciality == TileSpeciality.RowBreaker)
            {
                foreach (var cell in grid.GetCellsInRow(coords.Y))
                    TryDamageGoalCoord(cell, DamageSource.LineBreakerArrowHor, TileKinds.None, goalCoords,
                        ref affectedGoalCoords, ref tilesToRestore);
            }
            else
            {
                foreach (var cell in grid.GetCellsInColl(coords.X))
                    TryDamageGoalCoord(cell, DamageSource.LineBreakerArrowVer, TileKinds.None, goalCoords,
                        ref affectedGoalCoords, ref tilesToRestore);
            }
        }

        private static void ApplyLineBreakerMove(Grid grid, PossibleMove move,
            MechanicTargetingManager mechanicTargetingManager, HashSet<Coords> goalCoords,
            ref HashSet<Coords> affectedGoalCoords, ref Dictionary<Coords, Tile> tilesToRestore)
        {
            if (move.Type == PossibleMoveType.DoubleTap)
            {
                ApplyLineBreakerDamage(grid, move.FirstCell.Tile.Speciality, move.FirstCell.Coords, goalCoords,
                    ref affectedGoalCoords, ref tilesToRestore);
                return;
            }

            TileSpeciality secondSpeciality = GetSecondSpeciality(move);
            switch (secondSpeciality)
            {
                case TileSpeciality.Bomb:
                    ApplyTripleCrossDamage(grid, move.SecondCell.Coords, goalCoords, ref affectedGoalCoords,
                        ref tilesToRestore);
                    break;
                case TileSpeciality.ColorBomb:
                    ApplyFullGridDamage(grid, goalCoords, ref affectedGoalCoords, ref tilesToRestore);
                    break;
                case TileSpeciality.RowBreaker:
                case TileSpeciality.ColumnBreaker:
                    ApplyCrossDamage(grid, move.SecondCell.Coords, goalCoords, ref affectedGoalCoords,
                        ref tilesToRestore);
                    break;
                case TileSpeciality.Propeller:
                    ApplyPropellerDamage(grid, move.SecondCell.Coords, goalCoords, ref affectedGoalCoords,
                        ref tilesToRestore);
                    var target = mechanicTargetingManager.SelectComboTarget(DamageSource.PropellerCombo, secondSpeciality);
                    ApplyLineBreakerDamage(grid, move.SecondCell.Tile.Speciality, target.Coords, goalCoords,
                        ref affectedGoalCoords, ref tilesToRestore);
                    break;
                default:
                    ApplyLineBreakerDamage(grid, move.SecondCell.Tile.Speciality, move.SecondCell.Coords, goalCoords,
                        ref affectedGoalCoords, ref tilesToRestore);
                    break;
            }
        }

        private static void ApplyPropellerMove(Grid grid, PossibleMove move,
            MechanicTargetingManager mechanicTargetingManager,
            HashSet<Coords> goalCoords, GoalsSystem goalsSystem, ref HashSet<Coords> affectedGoalCoords,
            ref Dictionary<Coords, Tile> tilesToRestore)
        {
            ApplyPropellerDamage(grid, move.FirstCell.Coords, goalCoords, ref affectedGoalCoords, ref tilesToRestore);
            if (move.Type == PossibleMoveType.DoubleTap)
            {
                Cell targetCell = mechanicTargetingManager.SelectTarget(DamageSource.Propeller, move.FirstCell.Coords);
                TryDamageGoalCoord(targetCell, DamageSource.Propeller, TileKinds.None, goalCoords,
                    ref affectedGoalCoords, ref tilesToRestore);
                return;
            }

            Cell targetComboCell;
            TileSpeciality secondSpeciality = GetSecondSpeciality(move);
            switch (secondSpeciality)
            {
                case TileSpeciality.Bomb:
                    targetComboCell = mechanicTargetingManager.SelectComboTarget(DamageSource.PropellerCombo, secondSpeciality);
                    ApplyBombDamage(grid, targetComboCell.Coords, goalCoords, ref affectedGoalCoords,
                        ref tilesToRestore);
                    break;
                case TileSpeciality.ColorBomb:
                    ApplyFullGridDamage(grid, goalCoords, ref affectedGoalCoords, ref tilesToRestore);
                    break;
                case TileSpeciality.RowBreaker:
                case TileSpeciality.ColumnBreaker:
                    targetComboCell = mechanicTargetingManager.SelectComboTarget(DamageSource.PropellerCombo, secondSpeciality);
                    ApplyLineBreakerDamage(grid, move.SecondCell.Tile.Speciality, targetComboCell.Coords, goalCoords,
                        ref affectedGoalCoords, ref tilesToRestore);
                    break;
                case TileSpeciality.Propeller:
                    ApplyPropellerDamage(grid, move.SecondCell.Coords, goalCoords, ref affectedGoalCoords,
                        ref tilesToRestore);
                    targetComboCell = mechanicTargetingManager.SelectTarget(DamageSource.Propeller, move.SecondCell.Coords);
                    TryDamageGoalCoord(targetComboCell, DamageSource.PropellerCombo, TileKinds.None, goalCoords,
                        ref affectedGoalCoords, ref tilesToRestore);
                    targetComboCell = mechanicTargetingManager.SelectTarget(DamageSource.Propeller, move.SecondCell.Coords);
                    TryDamageGoalCoord(targetComboCell, DamageSource.PropellerCombo, TileKinds.None, goalCoords,
                        ref affectedGoalCoords, ref tilesToRestore);
                    targetComboCell = mechanicTargetingManager.SelectTarget(DamageSource.Propeller, move.SecondCell.Coords);
                    TryDamageGoalCoord(targetComboCell, DamageSource.PropellerCombo, TileKinds.None, goalCoords,
                        ref affectedGoalCoords, ref tilesToRestore);
                    break;
                default:
                    targetComboCell = mechanicTargetingManager.SelectTarget(DamageSource.Propeller, move.FirstCell.Coords);
                    TryDamageGoalCoord(targetComboCell, DamageSource.Propeller, TileKinds.None, goalCoords,
                        ref affectedGoalCoords, ref tilesToRestore);
                    break;
            }
        }

        private static void ApplyColorBombMove(Grid grid, PossibleMove move, HashSet<Coords> goalCoords,
            ref HashSet<Coords> affectedGoalCoords, ref Dictionary<Coords, Tile> tilesToRestore)
        {
            if (move.Type == PossibleMoveType.DoubleTap)
                return;
            
            TryDamageGoalCoord(move.FirstCell, DamageSource.RemoveColorTiles,
                TileKinds.None, goalCoords, ref affectedGoalCoords, ref tilesToRestore);
            
            TileSpeciality secondSpeciality = GetSecondSpeciality(move);
            switch (secondSpeciality)
            {
                case TileSpeciality.Bomb:
                case TileSpeciality.ColorBomb:
                case TileSpeciality.RowBreaker:
                case TileSpeciality.ColumnBreaker:
                case TileSpeciality.Propeller:
                    ApplyFullGridDamage(grid, goalCoords, ref affectedGoalCoords, ref tilesToRestore);
                    break;
                default:
                    TileKinds targetKind = move.SecondCell.Tile.Speciality == TileSpeciality.ColorBomb
                        ? move.FirstCell.Tile.Kind
                        : move.SecondCell.Tile.Kind;
                    foreach (var cell in grid.Cells)
                    {
                        if (cell.HasTile() && cell.Tile.Kind == targetKind)
                        {
                            if (TryDamageGoalCoord(cell, DamageSource.RemoveColorTiles, targetKind, goalCoords,
                                    ref affectedGoalCoords, ref tilesToRestore))
                            {
                                foreach (var adjacentCell in grid.GetAdjacentCells(cell.Coords))
                                    TryDamageGoalCoord(adjacentCell, DamageSource.AdjacentFromBoost, targetKind,
                                        goalCoords, ref affectedGoalCoords, ref tilesToRestore);
                            }

                        }
                    }

                    break;
            }
        }

        public static bool GetAffectedGoalCoordsForBooster(Grid grid, PossibleMove move,
            MechanicTargetingManager mechanicTargetingManager,
            HashSet<Coords> goalCoords, GoalsSystem goalsSystem, ref HashSet<Coords> affectedGoalCoords,
            ref Dictionary<Coords, Tile> tilesToRestore)
        {
            if (move.Type == PossibleMoveType.Simple)
                return false;

            PossibleMoveType boosterType = move.Type;
            if (boosterType == PossibleMoveType.DoubleTap)
                boosterType = TileSpecialityToMoveType(move.FirstCell.Tile.Speciality);

            switch (boosterType)
            {
                case PossibleMoveType.Bomb:
                    ApplyBombMove(grid, move, mechanicTargetingManager, goalCoords, ref affectedGoalCoords,
                        ref tilesToRestore);
                    break;
                case PossibleMoveType.LineBreaker:
                    ApplyLineBreakerMove(grid, move, mechanicTargetingManager, goalCoords, ref affectedGoalCoords,
                        ref tilesToRestore);
                    break;
                case PossibleMoveType.Propeller:
                    ApplyPropellerMove(grid, move, mechanicTargetingManager, goalCoords,
                        goalsSystem, ref affectedGoalCoords, ref tilesToRestore);
                    break;
                case PossibleMoveType.ColorBomb:
                    ApplyColorBombMove(grid, move, goalCoords, ref affectedGoalCoords, ref tilesToRestore);
                    break;
            }

            return affectedGoalCoords.Count > 0;
        }

        public static bool CanAffectGoalCoords(Match match, TileSpeciality boosterSpeciality, Grid grid,
            HashSet<Coords> goalCoords)
        {
            Coords spawnCoord = match.GetSpawnCoord(grid);
            switch (boosterSpeciality)
            {
                case TileSpeciality.Propeller:
                    return true;
                case TileSpeciality.RowBreaker:
                    int lowerBound = Mathf.Max(0, spawnCoord.Y - 1);
                    int upperBound = Mathf.Min(grid.Height - 1, spawnCoord.Y + 1);
                    //check 3 rows for goals
                    for (int y = lowerBound; y <= upperBound; ++y)
                    {
                        if (grid.DoesRowContain(y,
                                cell => goalCoords.Contains(cell.Coords) &&
                                        CanDamageCell(cell, DamageSource.LineBreakerArrowHor, match.Kind)))
                            return true;
                    }

                    return false;
                case TileSpeciality.ColumnBreaker:
                    int leftBound = Mathf.Max(0, spawnCoord.X - 1);
                    int rightBound = Mathf.Max(grid.Width - 1, spawnCoord.X + 1);
                    //check 3 columns for goals
                    for (int x = leftBound; x <= rightBound; ++x)
                    {
                        if (grid.DoesRowContain(x,
                                cell => goalCoords.Contains(cell.Coords) &&
                                        CanDamageCell(cell, DamageSource.LineBreakerArrowVer, match.Kind)))
                            return true;
                    }

                    return false;
                case TileSpeciality.Bomb:
                    return grid.DoesSquareContain(spawnCoord, 7, //5 bomb radius + 2 for possible swap on each side
                        cell => goalCoords.Contains(cell.Coords) && CanDamageCell(cell, DamageSource.Bomb, match.Kind));
                case TileSpeciality.ColorBomb:
                    Span<bool> possibleKinds = stackalloc bool[(int)TileKinds.COUNT];
                    possibleKinds.Clear();
                    //get all matchable colors around
                    foreach (var tile in grid.GetTilesAround(spawnCoord))
                    {
                        if (tile.IsMatchableSimpleTile())
                            possibleKinds[(int)tile.Kind] = true;
                    }

                    foreach (var cell in grid.Cells)
                    {
                        if (!goalCoords.Contains(cell.Coords))
                            continue;

                        TileKinds tileKind = cell.Tile?.Kind ?? TileKinds.None;
                        int tileKindIndex = (int)tileKind;
                        if (tileKindIndex > 0
                            && possibleKinds[tileKindIndex]
                            && CanDamageCell(cell, DamageSource.RemoveColorTiles, tileKind))
                            return true;
                    }

                    return false;
                default:
                    throw new NotSupportedException($"{boosterSpeciality} isn't supported!");
            }
        }
    }
}