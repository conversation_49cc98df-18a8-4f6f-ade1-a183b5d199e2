using System;
using System.Collections.Generic;
using BBB.Match3.Debug;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public partial class AssistSystem
    {
        private Dictionary<TileKinds, int> _groupedTileKindsCount = new ();

        public bool Shuffle(GameSimulation simulation, Grid grid, IRootSimulationHandler simHandler, bool byBooster = false)
        {
            M3Debug.Disabled = false;
            var shuffleSuccess = false;
            var loopDetector = 0;
            ActionShuffle shuffleAction = null;
            while (!shuffleSuccess)
            {
                shuffleSuccess = ShuffleAttempt(simulation, grid, simHandler, byBooster, out shuffleAction, out var gridClone);

                // 1000 loops changed to 100 because usually shuffle takes no more than 10 cycles, 50 cycles max.
                // And failed loop is no longer exception, because it is valid possibility of level outcome, if not enough tiles or free cells left on the grid. 
                if (loopDetector >= 100)
                {
#if UNITY_EDITOR
                    try
                    {
                        M3DebugInfoSystem.SnapshotGrid(grid, gridClone);
                    }
                    catch (Exception ex)
                    {
                        UnityEngine.Debug.LogException(ex);
                    }
#endif
                    break;
                }

                loopDetector++;
            }

            shuffleAction?.Execute(grid, null);

#if BBB_LOG
            if (shuffleSuccess)
            {
                if (loopDetector > 1)
                {
                    if (loopDetector > 15)
                    {
                        // This should be very rare, so if it start occur, it will be easier to find issue.
                        UnityEngine.Debug.LogError("Shuffle found in loops " + loopDetector + " on level '" + grid.DebugCurrentLevelUid + "'");
                    }
                    else
                    {
                        M3Debug.Log("Shuffle found in loops " + loopDetector + " on level '" + grid.DebugCurrentLevelUid + "'");
                    }
                }
            }
            else
            {
                UnityEngine.Debug.LogError($"Shuffle failed. {loopDetector} attempts." + " on level '" + grid.DebugCurrentLevelUid + "'");
            }
#endif

            _groupedTileKindsCount.Clear();
            return shuffleSuccess;
        }


        private bool ShuffleAttempt(GameSimulation simulation, Grid grid, IRootSimulationHandler simHandler, bool byBooster, out ActionShuffle shuffleAction, out Grid gridClone)
        {
            gridClone = grid.CloneIntoUndefinedShuffleable();
            var cellsForShuffleOriginal = new List<Cell>(grid.Cells);
            GroupTileKindsCount(cellsForShuffleOriginal, _groupedTileKindsCount);
            var shuffleDefineResult = ShuffleDefine(gridClone, _groupedTileKindsCount, simHandler);

            if (!shuffleDefineResult)
            {
                shuffleAction = null;
                return false;
            }

            if (_groupedTileKindsCount.Count > 0)
            {
                var str = "Extra kinds left! ";
                foreach (var kvp in _groupedTileKindsCount)
                {
                    str += $"{kvp.Key} {kvp.Value} ";
                }

                throw new InvalidOperationException(str);
            }

            var oldGroupedTileKinds = GroupTileKinds(cellsForShuffleOriginal);
            var newGroupedTileKinds = GroupTileKinds(gridClone.Cells);

            var moves = new Dictionary<int, Coords>();
            for (var i = 0; i < oldGroupedTileKinds.Count; i++)
            {
                var groupType = default(TileKinds);
                var index = 0;

                foreach (var kvp in oldGroupedTileKinds)
                {
                    if (index == i)
                    {
                        groupType = kvp.Key;
                        break;
                    }
                    index++;
                }
                
                var oldGroup = oldGroupedTileKinds[groupType];
                var newGroup = newGroupedTileKinds[groupType];

                for (var j = 0; j < oldGroup.Count; j++)
                {
                    Cell oldGroupCell = null;
                    Cell newGroupCell = null;

                    var currentIndex = 0;
                    using (var oldGroupEnumerator = oldGroup.GetEnumerator())
                    {
                        while (oldGroupEnumerator.MoveNext())
                        {
                            if (currentIndex == j)
                            {
                                oldGroupCell = oldGroupEnumerator.Current;
                                break;
                            }
                            currentIndex++;
                        }
                    }

                    currentIndex = 0;
                    using (var newGroupEnumerator = newGroup.GetEnumerator())
                    {
                        while (newGroupEnumerator.MoveNext())
                        {
                            if (currentIndex == j)
                            {
                                newGroupCell = newGroupEnumerator.Current;
                                break;
                            }
                            currentIndex++;
                        }
                    }

                    if (oldGroupCell == null || newGroupCell == null) continue;
                    
                    var key = oldGroupCell.Tile.Id;
                    var value = newGroupCell.Coords;
                    moves.TryAdd(key, value);
                }
            }             

            var turnToApply = simulation.MaxTurn+1;
            shuffleAction = new ActionShuffle(moves, byBooster, simulation.DefinedTileKinds);
            simulation.ReplaceLogicalActionOfSameType(turnToApply, shuffleAction);

            var noMatches = SearchMatchesSystem.AreNoMatches(gridClone);
            var noPossibleMove = SearchMatchesSystem.SearchForAnyPossibleMove(gridClone, true);
            return noMatches && noPossibleMove;
        }

        private Dictionary<TileKinds, HashSet<Cell>> GroupTileKinds(IEnumerable<Cell> cellsForShuffle)
        {
            var groupedTileKinds = new Dictionary<TileKinds, HashSet<Cell>>();

            foreach (var cell in cellsForShuffle)
            {
                if (!cell.IsShuffable()) continue;

                var tile = cell.Tile;

                if (!groupedTileKinds.TryGetValue(tile.Kind, out var cells))
                {
                    cells = new HashSet<Cell>();
                    groupedTileKinds.Add(tile.Kind, cells);
                }
                cells.Add(cell);
            }
            
            var kvpList = new List<KeyValuePair<TileKinds, HashSet<Cell>>>(groupedTileKinds);

            kvpList.Sort((kvp1, kvp2) => kvp2.Value.Count.CompareTo(kvp1.Value.Count));
            
            foreach (var kvp in kvpList)
            {
                groupedTileKinds[kvp.Key] = kvp.Value;
            }
            
            return groupedTileKinds;
        }

        private void GroupTileKindsCount(IEnumerable<Cell> cellsForShuffle,  Dictionary<TileKinds, int> groupedTileKindsCount)
        {
            groupedTileKindsCount.Clear();

            foreach (var cell in cellsForShuffle)
            {
                if (!cell.IsShuffable()) continue;

                var tile = cell.Tile;

                if (tile is null)
                    continue;

                var tileKind = tile.Kind;
                if (!groupedTileKindsCount.TryAdd(tileKind, 1))
                {
                    groupedTileKindsCount[tileKind]++;
                }
            }
        }
    }
}

