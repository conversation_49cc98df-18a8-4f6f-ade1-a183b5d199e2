#if UNITY_EDITOR
using System;
using System.Collections;
using System.Collections.Generic;
using BBB.CellTypes;
using BBB.DI;
using BBB.M3Editor;
using BBB.Match3.Debug;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.SearchMatchesSystemTypes;
using BBB.Match3.Systems.Exceptions;
using BBB.Match3.Systems.GoalsService;
using BBB.Wallet;
using GameAssets.Scripts.Match3.Logic;
using GameAssets.Scripts.Match3.Settings;

namespace BBB.Match3.Systems
{
    public enum AutoBruteErrorCode
    {
        None = 0,
        Exception = 1,
        FailedToReach = 2
    }

    public struct AutoBruteParams
    {
        public LimitType Limit;
        public PickLogic PickLogic;
        public int Runs;
        public int LimitNumber;
        public string TestName;
        public bool AllowSnapshots;
        public bool ShowBruteProgress;
        public bool UseSuperBoost;
        public bool GoalSyncCheck;

        /// <summary>
        /// If set to some value, then bot will retry level until first win, or until limit tries count reached.
        /// </summary>
        /// <remarks>
        /// Used for situations, when main runs count is not enough for specific levels to win for bot at least once.
        /// We can't increase main runs count because this will affect all levels (and increase total time of test),
        /// when it is needed only for few levels.
        /// </remarks>
        public int ForceContinueTestUntilFirstWinLimit;

        public override string ToString()
        {
            return "LimitType: " + Limit + " PickLogic: " + PickLogic + "Runs: " + Runs;
        }
    }

    public class FailedToReachException : Exception
    {
        public FailedToReachException(string message)
            : base(message)
        {
        }
    }

    public sealed class AutoBruteSystem : IContextInitializable
    {
        private M3EditorProgressPopup _progressPopup;
        private GoalsSystem _goalSystem;
        private IAssistParamsProvider _assistParamsProvider;
        private GameController _gameController;
        private IEventDispatcher _eventDispatcher;

        private GoalState _originalGoals;
        private AssistState _originalAssistState;
        private AutoBruteParams _lastAutoBruteParams;
        private bool _shouldBreak;

        private SuperBoostSystem _superBoostSystem;
        private SpawnerSettingsManager _spawnerSettingsManager;
        private M3SpawnSystem _spawnSystem;
        private TileResourceSelector _tilesResources;
        private ILevelAnalyticsReporter _levelAnalyticsReporter;
        private IConfig _config;
        private ExtraBoostersHelper _extraBoostersHelper;

        public void InitializeByContext(IContext context)
        {
            _gameController = context.Resolve<GameController>();
            _progressPopup = context.Resolve<M3EditorProgressPopup>();
            _goalSystem = context.Resolve<GoalsSystem>();
            _assistParamsProvider = context.Resolve<IAssistParamsProvider>();
            _spawnerSettingsManager = context.Resolve<SpawnerSettingsManager>();
            _spawnSystem = context.Resolve<M3SpawnSystem>().Clone();
            _tilesResources = context.Resolve<TileResourceSelector>();

            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _eventDispatcher.RemoveListener<ProcessCancelledEvent>(OnProcessCancelled);
            _eventDispatcher.AddListener<ProcessCancelledEvent>(OnProcessCancelled);

            _levelAnalyticsReporter = context.Resolve<ILevelAnalyticsReporter>();
            _config = context.Resolve<IConfig>();

            _superBoostSystem = new SuperBoostSystem();
            _superBoostSystem.InitializeByContext(context);
            _superBoostSystem.TransitToDebugMode();

            _extraBoostersHelper = context.Resolve<ExtraBoostersHelper>();
        }

        private void OnProcessCancelled(ProcessCancelledEvent obj)
        {
            _shouldBreak = true;
        }

        private class GatheringStatisticsState
        {
            public int Runs;
            public bool IsForcedContinueRuns;
            public int TotalWins;
            public bool GoalDeSyncFoundForThisRun;
            public string History;
        }

        /// <summary>
        /// Brute simulation routine.
        /// </summary>
        /// <param name="shouldSkip">Boolean flag stored in reference type array so it can be changed outside coroutine.</param>
        public IEnumerator GatherStatistics(ILevel level, AutoBruteParams autoBruteParams, bool[] shouldSkip,
            Action<Match3Statistics, AutoBruteErrorCode, string> onComplete)
        {
            _progressPopup.ShowSecondBar($"Auto brute runs 0/{autoBruteParams.Runs}", 0f);
            yield return null;
            if (shouldSkip[0])
            {
                onComplete?.Invoke(new Match3Statistics(autoBruteParams.PickLogic), AutoBruteErrorCode.Exception,
                    "interrupted");
                yield break;
            }

            M3DebugInfoSystem.AllowSnapShots = autoBruteParams.AllowSnapshots;
            M3Debug.Disabled = true;
            UnityEngine.Debug.unityLogger.logEnabled = false;

            var errorCode = AutoBruteErrorCode.None;
            var message = string.Empty;
            var statistics = new Match3Statistics(autoBruteParams.PickLogic);
            var gatheringState = new GatheringStatisticsState();

            _originalAssistState = new AssistState(level.Goals.Clone(), level.Grid.Clone());
            _originalGoals = level.Goals.Clone();
            foreach (var (goalType, value) in _originalGoals)
            {
                if (value > 0)
                {
                    statistics.AddCeiling(goalType.ToNameString(), _originalGoals.GetGoalValue(goalType));
                }
            }

            if (autoBruteParams.Limit == LimitType.Turns)
            {
                var tileKindTypes = GoalTypeExtensions.TileKindTypes();
                for (var i = tileKindTypes.Length - 1; i >= 0; i--)
                {
                    if (level.Goals.GetGoalValue(tileKindTypes[i]) > 0)
                        level.Goals.SetGoalValue(tileKindTypes[i], int.MaxValue);
                }
                _goalSystem.RefreshForLevel(level);
            }
            _lastAutoBruteParams = autoBruteParams;
            _superBoostSystem.ResetProgress();
            
            AssistParams.AimAtWinningLevel = autoBruteParams.PickLogic != PickLogic.UsefulMovesLose;
            
            for (var runIndex = 0; runIndex < autoBruteParams.Runs || gatheringState.IsForcedContinueRuns; runIndex++)
            {
                RandomSystem.Reset();

                gatheringState.GoalDeSyncFoundForThisRun = false;
                if (_shouldBreak) break;
                gatheringState.Runs += 1;

                if (autoBruteParams.ShowBruteProgress)
                    _progressPopup.ShowSecondBar($"Auto brute runs {runIndex + 1}/{autoBruteParams.Runs}",
                        runIndex / (float)autoBruteParams.Runs);

                var gridToTest = level.Grid.Clone();
                _spawnSystem.Setup(level);
                gatheringState.History = $"Seed: {RandomSystem.Seed}";
                
                _levelAnalyticsReporter.LevelStarted(level, _config, string.Empty);

                try
                {
                    var simParams = new SimulationInputParams
                    {
                        UsedKinds = level.UsedKinds,
                        Settings = _gameController.Settings,
                        SimulateLoopException = _gameController.SimulateLoopException,
                        TurnsLimit = level.TurnsLimit,
                        OriginalGrid = gridToTest.Clone(),
                    };

                    var gs = new GravitySystem(simParams, _goalSystem, _tilesResources, _assistParamsProvider, _levelAnalyticsReporter);
                    var initialSim = gs.CreateSimulationSync(grid: gridToTest, playerInput: new PlayerInputNone(),
                        remainingMoves: level.TurnsLimit, assistParams: null, spawnSystem: _spawnSystem);

                    if (initialSim.Result == SimulationResult.SuccessShuffleLose)
                        statistics.ShuffleFailedCount++;

                    var initialSimulationDebugInfo = initialSim.DebugInfo;
                    if (initialSimulationDebugInfo is { MatchesHappened: > 0 })
                        statistics.AddInitialMatchesForRun(runIndex, initialSimulationDebugInfo.MatchesHappened);

                    switch (autoBruteParams.Limit)
                    {
                        case LimitType.Goal:
                            GatherStatisticsForGoals(level, autoBruteParams, gridToTest, simParams, statistics, gatheringState, runIndex);
                            break;

                        case LimitType.Turns:
                            GatherStatisticsForTurns(level, autoBruteParams, gridToTest, simParams, statistics, gatheringState);
                            break;

                        default:
                            throw new ArgumentOutOfRangeException();
                    }
                }
                catch (Exception e)
                {
                    UnityEngine.Debug.unityLogger.logEnabled = true;
                    M3Debug.Disabled = false;

                    switch (e)
                    {
                        case FailedToReachException:
                            UnityEngine.Debug.LogWarning(
                                $"Failed to reach goal for {autoBruteParams.TestName}, Message: {e.Message}");
                            errorCode = AutoBruteErrorCode.FailedToReach;
                            break;

                        case NoPossibleMovesException:
                            UnityEngine.Debug.LogError($"No possible moves found on grid. Level={level.Config.Uid}");
                            errorCode = AutoBruteErrorCode.FailedToReach;
                            statistics.NoPossibleMovesAfterShuffleCount++;

                            // Try to repeat up to 3 times.
                            gatheringState.IsForcedContinueRuns =
                                autoBruteParams.ForceContinueTestUntilFirstWinLimit > 0 &&
                                gatheringState.Runs < 3;
                            break;

                        default:
                            message += e.Message + "\n" + e.StackTrace;
                            UnityEngine.Debug.LogWarning(
                                $"Exception while running brute test {autoBruteParams.TestName}, Message: {message}\nhistory:{gatheringState.History}");
                            errorCode = AutoBruteErrorCode.Exception;
                            break;
                    }

                    M3Debug.Disabled = true;
                    UnityEngine.Debug.unityLogger.logEnabled = false;
                }

                _goalSystem.ResetGoalsProgressLeft();
                CalculateSpecialTilesSystem.Clear();
                statistics.Add(Match3Statistics.Replay, _levelAnalyticsReporter.GetReplayData());

                yield return null;
                if (shouldSkip[0]) break;
            }

            level.Goals = _originalGoals;

            _goalSystem.RefreshForLevel(_gameController.Level);

            M3Debug.Disabled = false;
            UnityEngine.Debug.unityLogger.logEnabled = true;

            if (autoBruteParams.ShowBruteProgress)
            {
                _progressPopup.Hide();
            }

            M3DebugInfoSystem.AllowSnapShots = false;
            _shouldBreak = false;
            onComplete.SafeInvoke(statistics, errorCode, message);
        }

        private void GatherStatisticsForGoals(
            ILevel level,
            AutoBruteParams autoBruteParams,
            Grid gridToTest,
            SimulationInputParams simParams,
            Match3Statistics statistics,
            GatheringStatisticsState routineState,
            int runIndex)
        {
            var turnCounter = 0;
            statistics.NonGoalTargetMoves = 0;

            var win = true;
            var superBoostUsedTimes = 0;
            var statsDict = new Dictionary<string, int>
            {
                { "LineBreaker", 0 },
                { "Bomb", 0 },
                { "Bolt", 0 },
                { "Propeller", 0 },
                { "Shuffles", 0 },
                { "EmptyMoves", 0 },
                { "Automatches", 0 },
                { "FLAutomatches", 0 },
                { "FLBombs", 0 },
            };
            

            while (!_goalSystem.HasCompletedGoals())
            {
                var randomState = RandomSystem.GetRandomGenerationValues();
                
                GetPossibleMove(level, gridToTest, out var pickedPossibleMatch);

                IPlayerInput playerInput;
                if (pickedPossibleMatch.FirstCell == null || pickedPossibleMatch.SecondCell == null)
                {
                    // throw new NoPossibleMovesException();
                    playerInput = new PlayerInputItemReshuffle();
                    statistics.ShuffleCount += 1;
                }
                else
                {
                    playerInput = SearchMatchesSystem.ToPlayerInput(pickedPossibleMatch);
                }
                
                _levelAnalyticsReporter.RegisterPlayerMove(randomState, playerInput);

                var totalMoves = level.TurnsLimit;
                var remainingMoves = totalMoves - turnCounter;

                var progressAchieved =
                    _originalAssistState - new AssistState(_goalSystem.GoalProgressLeft, gridToTest);

                var assistParams = _assistParamsProvider.GetAssistParams(remainingMoves, totalMoves,
                    progressAchieved, _originalAssistState);
                
                var winValue = assistParams.WinValue;
                var lossValue = assistParams.LossValue;
                var winLossDiff = assistParams.WinLossValueDiff();
                var diffValue = winValue - lossValue;

                statistics.Add(AssistValue.WinValue.ToString(), runIndex, winValue);
                statistics.Add(AssistValue.LossValue.ToString(), runIndex, lossValue);
                statistics.Add(AssistValue.DiffValue.ToString(), runIndex, diffValue);
                statistics.Add(AssistValue.WinLossValueDiff.ToString(), runIndex, winLossDiff);

                if (_goalSystem == null)
                    throw new Exception("Goal system is null");

                routineState.History += $" {playerInput}";

                var sim = new GravitySystem(simParams, _goalSystem, _tilesResources, _assistParamsProvider, _levelAnalyticsReporter)
                    .CreateSimulationSync(gridToTest, playerInput, remainingMoves: remainingMoves, assistParams, _spawnSystem);

                var newProgressAchieved =
                    _originalAssistState - new AssistState(_goalSystem.GoalProgressLeft, gridToTest);

                var newAssistParams = _assistParamsProvider.GetAssistParams(
                    remainingMoves - 1, totalMoves, newProgressAchieved, _originalAssistState);

                var winValueDelta = newAssistParams.WinValue - assistParams.WinValue;
                if (winValueDelta <= float.Epsilon)
                {
                    statistics.NonGoalTargetMoves += 1;
                }

                if (autoBruteParams.GoalSyncCheck && !routineState.GoalDeSyncFoundForThisRun)
                {
                    var deSyncStr = DoGoalSyncCheck(level, gridToTest, _goalSystem, turnCounter,
                        routineState.Runs, level.LevelUid);

                    if (deSyncStr != null)
                    {
                        statistics.GoalDeSyncStrList.Add(deSyncStr);
                        routineState.GoalDeSyncFoundForThisRun = true;
                    }
                }

                if (HandleSuperBoost(sim, simParams, gridToTest, autoBruteParams, assistParams, remainingMoves, ref routineState.History))
                {
                    superBoostUsedTimes++;
                }

                CountSpawns(sim, statsDict);
                CountRemoves(sim, statsDict);
                statsDict["Shuffles"] = statistics.ShuffleCount;
                statsDict["EmptyMoves"] = statistics.NonGoalTargetMoves;

                turnCounter++;

                if (turnCounter < autoBruteParams.LimitNumber && !sim.Result.IsLose())
                    continue;

                win = false;
                break;
            } // while (!_goalSystem.HasCompletedGoals())

            if (win)
            {
                routineState.IsForcedContinueRuns = false;
                routineState.TotalWins += 1;
            }
            else
            {
                routineState.IsForcedContinueRuns =
                    routineState.TotalWins == 0 && routineState.Runs < autoBruteParams.ForceContinueTestUntilFirstWinLimit;
            }

            statistics.Add("Turns", turnCounter, false);
            statistics.Add("IsWin", win ? 1 : 0, false);
            statistics.Add("SuperBoostUsed", superBoostUsedTimes, false);

            foreach (var (key, value) in statsDict)
                statistics.Add(key, value, false);

            foreach (var (goalType, value) in _originalGoals)
            {
                if (value <= 0) continue;
                statistics.Add(goalType.ToNameString(), _goalSystem.GetAchievedGoalCount(goalType),
                    true);
            }

            level.Goals = _originalGoals;
            _goalSystem.RefreshForLevel(level);

            if (turnCounter >= autoBruteParams.LimitNumber)
            {
                throw new FailedToReachException(
                    $"Failed to reach goal in {autoBruteParams.LimitNumber} " +
                    $"moves \n {_goalSystem.RemainingGoalsString}");
            }
        }

        private void GatherStatisticsForTurns(ILevel level, AutoBruteParams autoBruteParams, Grid gridToTest,
            SimulationInputParams simParams, Match3Statistics statistics, GatheringStatisticsState routineState)
        {
            var superBoostUsedTimes = 0;
            statistics.NonGoalTargetMoves = 0;
            var statsDict = new Dictionary<string, int>
            {
                { "LineBreaker", 0 },
                { "Bomb", 0 },
                { "Bolt", 0 },
                { "Propeller", 0 },
                { "Shuffles", 0 },
                { "EmptyMoves", 0 },
                { "Automatches", 0 },
                { "FLAutomatches", 0 },
                { "FLBombs", 0 },
                { "FLBolts", 0 },
            };

            var allGoalsReached = false;
            var remainingMovesOnEnd = 0;
            GoalState goalsLeft;

            var randomState = RandomSystem.GetRandomGenerationValues();

            for (var j = 0; j < autoBruteParams.LimitNumber; j++)
            {
                GetPossibleMove(level, gridToTest, out var pickedPossibleMatch);
                IPlayerInput playerInput;
                if (pickedPossibleMatch.FirstCell == null || pickedPossibleMatch.SecondCell == null)
                {
                    //throw new NoPossibleMovesException();
                    playerInput = new PlayerInputItemReshuffle();
                    statistics.ShuffleCount += 1;
                }
                else
                {
                    playerInput = SearchMatchesSystem.ToPlayerInput(pickedPossibleMatch);
                }
                
                _levelAnalyticsReporter.RegisterPlayerMove(randomState, playerInput);

                var spendMoves = autoBruteParams.LimitNumber - j;
                var totalMoves = autoBruteParams.LimitNumber;

                var progressAchieved =
                    _originalAssistState - new AssistState(_goalSystem.GoalProgressLeft, gridToTest);

                var assistParams = _assistParamsProvider.GetAssistParams(
                    spendMoves, totalMoves, progressAchieved, _originalAssistState);

                var remainingMoves = autoBruteParams.LimitNumber - j;

                routineState.History += " " + playerInput;

                var sim = new GravitySystem(simParams, _goalSystem, _tilesResources, _assistParamsProvider, _levelAnalyticsReporter)
                    .CreateSimulationSync(gridToTest, playerInput, remainingMoves, assistParams, _spawnSystem);

                if (autoBruteParams.GoalSyncCheck && !routineState.GoalDeSyncFoundForThisRun)
                {
                    var deSyncStr = DoGoalSyncCheck(level, gridToTest, _goalSystem, j, routineState.Runs, level.LevelUid);

                    if (deSyncStr != null)
                    {
                        statistics.GoalDeSyncStrList.Add(deSyncStr);
                        routineState.GoalDeSyncFoundForThisRun = true;
                    }
                }

                if (HandleSuperBoost(sim, simParams, gridToTest, autoBruteParams, assistParams, remainingMoves, ref routineState.History))
                    superBoostUsedTimes++;

                CountSpawns(sim, statsDict);
                CountRemoves(sim, statsDict);

                if (sim.Result is SimulationResult.SuccessShuffleLose)
                    break;

                var newProgressAchieved =
                    _originalAssistState - new AssistState(_goalSystem.GoalProgressLeft, gridToTest);

                var currentAssistParams = _assistParamsProvider.GetAssistParams(
                    spendMoves - 1, totalMoves, newProgressAchieved, _originalAssistState);

                var winValueDelta = currentAssistParams.WinValue - assistParams.WinValue;
                if (winValueDelta <= float.Epsilon)
                {
                    statistics.NonGoalTargetMoves += 1;
                }
                statsDict["Shuffles"] = statistics.ShuffleCount;
                statsDict["EmptyMoves"] = statistics.NonGoalTargetMoves;

                if (allGoalsReached) continue;
                goalsLeft = _originalGoals - _goalSystem.ProgressAchieved;

                if (goalsLeft.AnyGoalsLeft()) continue;
                allGoalsReached = true;
                remainingMovesOnEnd = remainingMoves - 1;
            } // for (var j = 0; j < autoBruteParams.LimitNumber; j++)

            foreach (var (goalType, value) in _originalGoals)
            {
                if (value > 0)
                {
                    statistics.Add(goalType.ToNameString(), _goalSystem.GetAchievedGoalCount(goalType),
                        true);
                }
            }

            statistics.Add("SuperBoostUsed", superBoostUsedTimes, false);

            if (remainingMovesOnEnd > 0)
                statistics.Add("RemainingMoves", remainingMovesOnEnd, false);

            foreach (var kvp in statsDict)
                statistics.Add(kvp.Key, kvp.Value, false);

            if (allGoalsReached)
                return;

            goalsLeft = _originalGoals - _goalSystem.ProgressAchieved;
            var highestGoal = goalsLeft.GetHighestValueGoal(_originalGoals);
            statistics.AddLossReason(highestGoal.ToString());
        }

        private static readonly HashSet<GoalType> MandatoryGoals = new()
        {
            GoalType.Stickers, // SK: this will spam a lot initially; but that's deliberately
            GoalType.ColorCrate,
            GoalType.MoneyBag,
            GoalType.Animal,
            GoalType.Backgrounds,
            GoalType.Ivy,
            GoalType.Tnt,
            GoalType.Watermelon,
            GoalType.Litters,
            GoalType.IceCubes,
            GoalType.Chains,
            GoalType.Sand,
            GoalType.Bird,
            GoalType.Egg,
            GoalType.Bee,
            GoalType.Sheep,
            GoalType.Banana,
            GoalType.Chicken,
            GoalType.Squid,
            GoalType.Mole,
            GoalType.Pinata,
            GoalType.Toad,
            GoalType.BowlingPin,
            GoalType.Bush,
            GoalType.SodaBottle,
            GoalType.MagicHat,
            GoalType.Safe,
            GoalType.FlowerPot,
            GoalType.Petal,
            GoalType.IceBar,
            GoalType.DynamiteStick,
            GoalType.GiantPinata,
            GoalType.MetalBar,
            GoalType.Shelf,
            GoalType.LightBulbs,
            GoalType.DestructibleWall,
            GoalType.GoldenScarab,
            GoalType.Gondola,
            GoalType.TukTuk,
            GoalType.FireWorks,
            GoalType.SlotMachine,
            GoalType.Stone,
            GoalType.Pouch,
            GoalType.SeaMine
        };

        private string DoGoalSyncCheck(ILevel level, Grid grid, GoalsSystem goalSystem, int turnIndex, int runIndex,
            string levelUid)
        {
            var tileSpawnSettings = new List<TileSpawnSettings>();
            foreach (var cell in grid.Cells)
            {
                if (!cell.IsAnyOf(CellState.Spawner)) continue;
                var spawnerSettings = SpawnerSettings.FindSpawnerByUid(_spawnerSettingsManager.SpawnerSettings, cell.SpawnerUid);
                if (spawnerSettings != null && spawnerSettings.Uid > 0)
                {
                    tileSpawnSettings.AddRange(spawnerSettings.TilesSettings);
                }
            }

            foreach (var goal in _originalGoals)
            {
                var goalType = goal.Key;
                if (!goalType.IsStrictGridGoal()) continue;
                var goalLeftCount = goalSystem.GetLeftGoalCount(goalType);
                var cellsRelatedToGoal = level.Goals.CountCellsRelatedToGoal(grid, goalType);
                if (goalLeftCount == cellsRelatedToGoal) continue;
                var canGoalBeSpawn = false;
                foreach (var tileSettings in tileSpawnSettings)
                {
                    var newTile = TileFactory.CreateTile(-1, tileSettings.Asset, default);
                    var related = newTile.IsRelatedToGoal(goalType);
                    if (related)
                    {
                        canGoalBeSpawn = true;
                        break;
                    }
                }

                if (!canGoalBeSpawn)
                {
                    return $"For goal {goalType} de-sync happened for level {levelUid} at turn " +
                           $"{turnIndex + 1} (run {runIndex + 1}): goal left = {goalLeftCount}; goal left in the grid =ß {cellsRelatedToGoal}";
                }
            }

            foreach (var goalType in MandatoryGoals)
            {
                var cellsRelatedToGoal = level.Goals.CountCellsRelatedToGoal(grid, goalType);

                // Egg-Bird is the only exception in matching between goals and tile speciality
                var relatedGoal = goalType;
                if (relatedGoal == GoalType.Egg)
                    relatedGoal = GoalType.Bird;

                if (cellsRelatedToGoal > 0 && goalSystem.GetLeftGoalCount(relatedGoal) == 0)
                {
                    return
                        $"Level {levelUid}.{_gameController.Level.Stage + 1} - There is {goalType} mechanic without corresponding goal.";
                }
            }

            return null;
        }

        private static void CountSpawns(GameSimulation sim, IDictionary<string, int> spawns)
        {
            spawns["LineBreaker"] += sim.CountSpawnsFor(TileSpeciality.ColumnBreaker);
            spawns["LineBreaker"] += sim.CountSpawnsFor(TileSpeciality.RowBreaker);
            spawns["Bomb"] += sim.CountSpawnsFor(TileSpeciality.Bomb);
            spawns["Propeller"] += sim.CountSpawnsFor(TileSpeciality.Propeller);
            spawns["Bolt"] += sim.CountSpawnsFor(TileSpeciality.ColorBomb);
        }

        private static void CountRemoves(GameSimulation sim, IDictionary<string, int> removes)
        {
            removes["Automatches"] += sim.CountRemovesFor(tile => tile.IsAutomatchProduct);
        }

        private bool HandleSuperBoost(
            GameSimulation sim,
            SimulationInputParams simParams,
            Grid gridToTest,
            AutoBruteParams autoBruteParams,
            AssistParams assistParams,
            int remainingMoves,
            ref string history)
        {
            if (!autoBruteParams.UseSuperBoost) return false;
            sim.VirtualApplyToSuperBoost(_superBoostSystem);
            if (_superBoostSystem.CurrentProgress < 1f) return false;
            
            var bolt = new AutoBoostInstance(InventoryBoosters.LightningStrikeBooster);
            var coords = _extraBoostersHelper.GetCellPosForBooster(bolt, remainingMoves);

            if (coords == null) return false;
                
            var superBoostInput = new PlayerInputItemCreateColorBomb(coords.Value);
            
            var randomState = RandomSystem.GetRandomGenerationValues();
            var superBoostInvokedEvent = _eventDispatcher.GetMessage<SuperBoostInvokedEvent>();
            superBoostInvokedEvent.Set(randomState, coords.Value);
            _eventDispatcher.TriggerEvent(superBoostInvokedEvent);
            
            history += " " + superBoostInput;
            new GravitySystem(simParams, _goalSystem, _tilesResources, _assistParamsProvider, _levelAnalyticsReporter).CreateSimulationSync(
                grid: gridToTest,
                playerInput: superBoostInput,
                remainingMoves: remainingMoves,
                assistParams: assistParams,
                spawnSystem: _spawnSystem);
            _superBoostSystem.ResetProgress();
            return true;
        }

        private void GetPossibleMove(ILevel level, Grid grid, out PossibleMove possibleMatch)
        {
            PossibleMove pickedPossibleMatch;
            switch (_lastAutoBruteParams.PickLogic)
            {
                case PickLogic.Random:
                {
                    var allPossibleMatches = SearchMatchesSystem.SearchForAllPossibleMoves(grid);
                    if (allPossibleMatches.Count == 0)
                    {
                        UnityEngine.Debug.LogError("No possible moves found");
                        possibleMatch = default;
                        return;
                    }

                    pickedPossibleMatch = allPossibleMatches.GetRandomItem();
                    break;
                }
                case PickLogic.UsefulMovesWin:
                    pickedPossibleMatch = UsefulMovesPicker.GetBestPossibleMove(level, grid, _goalSystem, _spawnSystem,
                        _gameController, _tilesResources);
                    break;
                case PickLogic.UsefulMovesLose:
                    pickedPossibleMatch = UsefulMovesPicker.GetBestPossibleMove(level, grid, _goalSystem, _spawnSystem,
                        _gameController, _tilesResources);
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }

            possibleMatch = pickedPossibleMatch;
        }
    }
}
#endif