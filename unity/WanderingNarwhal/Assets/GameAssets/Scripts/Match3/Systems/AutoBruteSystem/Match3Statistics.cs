using System.Collections.Generic;
using System.Text;
using UnityEngine;

namespace BBB.Match3.Systems
{
    public enum PickLogic
    {
        Random,
        UsefulMovesWin,
        UsefulMovesLose
    }

    public enum LimitType
    {
        Turns = 0,
        Goal = 2
    }

    public enum AssistValue
    {
        WinValue = 0,
        LossValue = 1,
        DiffValue = 2,
        WinLossValueDiff = 3
    }

    public class Match3Statistics
    {
        public readonly PickLogic PickLogic;

        private readonly Dictionary<string, List<int>> _resultsDict = new();
        private readonly Dictionary<string, List<string>> _replayDict = new();
        private readonly Dictionary<string, Dictionary<int, List<float>>> _assistSystemValues = new();
        private readonly Dictionary<string, bool> _isGoalResultFlagsDict = new();
        private readonly Dictionary<string, int> _ceilingsDict = new();

        private readonly Dictionary<string, float> _lossReasons = new();

        private readonly Dictionary<int, int> _runToInitialMatchCountMap = new();

        public int NonGoalTargetMoves;
        public int ShuffleCount;
        public int ShuffleFailedCount;
        public int NoPossibleMovesAfterShuffleCount;
        public const string Replay = "Replay";

        public IEnumerable<string> ResultTypes => _resultsDict.Keys;
        public IEnumerable<string> AssistSystemResultTypes => _assistSystemValues.Keys;
        public IEnumerable<string> ReplaySystemResultTypes => _replayDict.Keys;
        
        public IEnumerable<string> GoalResultTypes
        {
            get
            {
                foreach (var key in _resultsDict.Keys)
                {
                    if (_isGoalResultFlagsDict[key])
                    {
                        yield return key;
                    }
                }
            }
        }

        public List<string> GoalDeSyncStrList { get; }

        public Match3Statistics(PickLogic pickLogic)
        {
            PickLogic = pickLogic;
            GoalDeSyncStrList = new List<string>();
        }

        public override string ToString()
        {
            var sb = new StringBuilder();
            foreach (var kvp in _resultsDict)
            {
                sb.Append(kvp.Key);
                if (_isGoalResultFlagsDict.TryGetValue(kvp.Key, out var isGoal) && isGoal)
                {
                    const string goalString = "(Goal)";
                    sb.Append(goalString);
                }

                const string leftSquareBracket = " : [ ";
                sb.Append(leftSquareBracket);
                foreach (var number in kvp.Value)
                {
                    const string space = " ";
                    sb.Append(number).Append(space);
                }

                const string rightSquareBracket = "]";
                sb.AppendLine(rightSquareBracket);
            }

            return sb.ToString();
        }
        
        public void Add(string resultType, string replayData)
        {
            if (!_replayDict.TryGetValue(resultType, out var replayList))
            {
                replayList = new List<string>();
                _replayDict[resultType] = replayList;
            }
       
            replayList.Add(replayData);
        }
        
        public void Add(string resultType, int simulationRunIndex, float assistValue)
        {
            if (!_assistSystemValues.TryGetValue(resultType, out var simulationDict))
            {
                simulationDict = new Dictionary<int, List<float>>();
                _assistSystemValues[resultType] = simulationDict;
            }
            
            if (!simulationDict.TryGetValue(simulationRunIndex, out var moveList))
            {
                moveList = new List<float>();
                simulationDict[simulationRunIndex] = moveList;
            }
            
            moveList.Add(assistValue);
        }
        
        public void Add(string resultType, int number, bool isGoal)
        {
            if (_resultsDict.TryGetValue(resultType, out var results))
            {
                results.Add(number);
            }
            else
            {
                var newList = new List<int> {number};
                _resultsDict.Add(resultType, newList);
            }

            _isGoalResultFlagsDict[resultType] = isGoal;
        }

        public void AddLossReason(string lossReasonGoalType)
        {
            if (!_lossReasons.TryAdd(lossReasonGoalType, 1f))
            {
                _lossReasons[lossReasonGoalType] += 1f;
            }
        }

        public string GetLossReasonString()
        {
            var sum = 0f;

            foreach (var value in _lossReasons.Values)
            {
                sum += value;
            }
            
            var sb = new StringBuilder();

            foreach (var lossReasonEntry in _lossReasons)
            {
                var percentage = Mathf.RoundToInt(lossReasonEntry.Value / sum * 100f);
                sb.Append(lossReasonEntry.Key).Append("=").Append(percentage).Append("% ");
            }

            if (sb.Length > 0)
            {
                sb.Length--;
            }

            return sb.ToString();
        }

        public void AddCeiling(string resultType, int number)
        {
            _ceilingsDict.Add(resultType, number);
        }

        public int CountAllEqualOrGreaterThenCeiling()
        {
            var count = 0;

            if (_ceilingsDict.Count == 0)
                return -1;

            var sampleSize = 0;

            if (_resultsDict.Values.Count > 0)
            {
                var enumerator = _resultsDict.Values.GetEnumerator();
                if (enumerator.MoveNext())
                {
                    if (enumerator.Current != null)
                    {
                        sampleSize = enumerator.Current.Count;
                    }
                }
            }

            for (var i = 0; i < sampleSize; i++)
            {
                var allMeetCriteria = true;

                foreach (var resultType in _resultsDict.Keys)
                {
                    if (!_ceilingsDict.TryGetValue(resultType, out var ceiling)) continue;
                    
                    var list = _resultsDict[resultType];
                    var result = list[i];

                    if (result >= ceiling) continue;
                    
                    allMeetCriteria = false;
                    break;
                }

                if (allMeetCriteria)
                {
                    count++;
                }
            }

            return count;
        }

        public int CountEqualOrGreaterThenCeiling(string resultType)
        {
            if (!_ceilingsDict.TryGetValue(resultType, out var ceiling))
                return 0;

            var list = _resultsDict[resultType];
            var result = 0;
            foreach (var number in list)
            {
                if (number >= ceiling)
                {
                    result++;
                }
            }

            return result;
        }

        public int Count(string resultType)
        {
            return _resultsDict.TryGetValue(resultType, out var list) ? list.Count : 0;
        }

        /// <summary>
        /// / This method returns value of result R for which (ratio*100) percent of results is less or equal than R
        /// </summary>
        public int GetThresholdForRatio(string resultType, float ratio)
        {
            if (!_resultsDict.TryGetValue(resultType, out var resultList)) return 0;

            var ratioCount = Mathf.RoundToInt(resultList.Count * ratio);
            var number = 0;
            // Sort the resultList manually
            var sortedResultList = new List<int>(resultList);
            sortedResultList.Sort();

            foreach (var result in sortedResultList)
            {
                number++;

                if (number >= ratioCount)
                {
                    return result;
                }
            }

            UnityEngine.Debug.LogError("Not enough results to find threshold");
            return -1;
        }

        public int GetWinsCount()
        {
            var results = GetResults("IsWin");
            if (results == null)
            {
                return 0;
            }

            var sum = 0;
            foreach (var result in results)
            {
                sum += result;
            }

            return sum;
        }

        public int ResultsSum(string resultType)
        {
            if (!_resultsDict.TryGetValue(resultType, out var value)) return 0;
            
            var sum = 0;

            foreach (var item in value)
            {
                sum += item;
            }

            return sum;
        }

        public int MaxCount()
        {
            var result = 0;
            foreach (var kvp in _resultsDict)
            {
                if (kvp.Value.Count > result)
                {
                    result = kvp.Value.Count;
                }
            }

            return result;
        }

        public List<int> GetResults(string resultType)
        {
            return _resultsDict.GetValueOrDefault(resultType);
        }
        
        public Dictionary<int, List<float>> GetAssistSystemResults(string resultType)
        {
            return _assistSystemValues.GetValueOrDefault(resultType);
        }
        
        public List<string> GetReplaySystemResults(string resultType)
        {
            return _replayDict.GetValueOrDefault(resultType);
        }
        
        public float GetStandardDeviation(string resultType)
        {
            var results = _resultsDict[resultType];

            var mean = 0f;

            if (results is { Count: > 0 })
            {
                var allSum = 0;

                foreach (var result in results)
                {
                    allSum += result;
                }

                mean = (float)allSum / results.Count;
            }
            
            var sum = 0f;

            foreach (var number in results)
            {
                var dif = number - mean;
                sum += dif * dif;
            }

            return Mathf.Sqrt(sum / (results.Count - 1));
        }

        public void Merge(Match3Statistics stats)
        {
            foreach (var kvp in stats._resultsDict)
            {
                if (_resultsDict.TryGetValue(kvp.Key, out var existingList))
                {
                    existingList.AddRange(kvp.Value);
                }
                else
                {
                    _resultsDict.Add(kvp.Key, new List<int>(kvp.Value));
                }
            }
        }

        public void AddInitialMatchesForRun(int runIndex, int matchesCount)
        {
            _runToInitialMatchCountMap[runIndex] = matchesCount;
        }

        /// <summary>
        /// The fewer results are similar, the higher return value going to be
        /// </summary>
        public static IEnumerable<float> CompareByAverage(IEnumerable<string> resultTypes, Match3Statistics firstInstance, Match3Statistics secondInstance)
        {
            var deltaRatios = new List<float>();
            foreach (var resultType in resultTypes)
            {
                if (!firstInstance._resultsDict.ContainsKey(resultType) || !secondInstance._resultsDict.ContainsKey(resultType))
                    continue;

                var firstAverageResult = 0f;
                var secondAverageResult = 0f;

                if (firstInstance._resultsDict.TryGetValue(resultType, out var firstResults) && firstResults.Count > 0)
                {
                    var sum = 0;
                    foreach (var result in firstResults)
                    {
                        sum += result;
                    }
                    firstAverageResult = (float)sum / firstResults.Count;
                }

                if (secondInstance._resultsDict.TryGetValue(resultType, out var secondResults) && secondResults.Count > 0)
                {
                    var sum = 0;
                    foreach (var result in secondResults)
                    {
                        sum += result;
                    }
                    secondAverageResult = (float)sum / secondResults.Count;
                }

                var delta = firstAverageResult - secondAverageResult;
                var deltaRatio = Mathf.Abs(delta / Mathf.Max(firstAverageResult,secondAverageResult));
                deltaRatios.Add(deltaRatio);
            }

            return deltaRatios;
        }

        public (int runIndex, int matchesCount)? GetFirstOccurrenceOfInitialMatches()
        {
            if (_runToInitialMatchCountMap.Count == 0)
            {
                return null;
            }

            var minKey = int.MaxValue;
            var matchesCount = 0;
            var found = false;

            foreach (var kvp in _runToInitialMatchCountMap)
            {
                if (kvp.Key >= minKey) continue;
                
                minKey = kvp.Key;
                matchesCount = kvp.Value;
                found = true;
            }

            if (found)
            {
                return (minKey, matchesCount);
            }

            return null;
        }
    }
}