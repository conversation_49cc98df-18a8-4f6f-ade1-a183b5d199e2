using System.Collections.Generic;

namespace BBB.Match3.Systems
{
    public class PredeterminedSpawner
    {
        private readonly Stack<Tile> _tilesToSpawn;
        private readonly SpawnerSettings _spawnerSettings;

        public PredeterminedSpawner(SpawnerSettings spawnerSettings)
        {
            _spawnerSettings = spawnerSettings;
            _tilesToSpawn = new Stack<Tile>();
        }

        public void Refill(Grid grid)
        {
            if (_spawnerSettings.FixedTilesToPreSpawn != null)
            {
                _tilesToSpawn.Clear();
                for(int j = _spawnerSettings.FixedTilesToPreSpawn.Count-1; j >= 0; j--)
                {
                    var preSpawnTile = _spawnerSettings.FixedTilesToPreSpawn[j];
                    for (int i = 0; i < preSpawnTile.Count; i++)
                    {
                        switch (preSpawnTile.Speciality)
                        {
                            case TileSpeciality.None:
                            {
                                var tile = TileFactory.CreateTile(grid.TilesSpawnedCount++, preSpawnTile.TileKind.ToSimpleTileAsset(), TileOrigin.Default());
                                _tilesToSpawn.Push(tile);
                                break;
                            }
                            case TileSpeciality.DropItem:
                            {
                                var tile = TileFactory.CreateTile(grid.TilesSpawnedCount++, TileAsset.DropItem, TileOrigin.Default());
                                _tilesToSpawn.Push(tile);
                                break;
                            }
                        }

                       
                    }
                }
            }
        }

        public bool HasAnyTileToSpawn()
        {
            return _tilesToSpawn is {Count: > 0};
        }
        
        public Tile CreateTileFor(TileOrigin origin)
        {
            if (_tilesToSpawn is {Count: > 0})
            {
                var tile = _tilesToSpawn.Pop();
                tile.Origin = origin;
                return tile;
            }

            return null;
        }
    }
}