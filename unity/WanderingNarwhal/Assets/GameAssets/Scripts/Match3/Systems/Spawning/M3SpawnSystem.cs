using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Core;
using BBB.DI;
using BBB.Match3.Debug;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.GoalsService;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems
{
    public class M3SpawnSystem : IContextInitializable
    {
        private Grid _currentGrid;

        private readonly List<TileAsset> _fallbackTilesToUseOnWin = new()
        {
            TileAsset.Green,
            TileAsset.Yellow,
            TileAsset.Blue,
            TileAsset.Red,
        };

        private List<TileKinds> _usedKinds;
        private string _levelUid;
        private bool _isPreStartSpawn;

        private GoalsSystem _goals;
        private GameEventMatch3ManagersCollection _completionMatch3ManagerCollection;
        private SpawnerSettingsManager _spawnerSettingsManager;
        private ILevelAnalyticsReporter _levelAnalyticsReporter;

        private readonly List<TileSpawnSettings> _allowedTilesCache = new ();
        private readonly Dictionary<SpawnerSettings, List<Cell>> _spawnersOnGrid = new ();
        private readonly Dictionary<Coords, PredeterminedSpawner> _predeterminedSpaners = new ();

        /// <summary>
        /// If spawn system should spawn only undefined tiles;
        /// </summary>
        /// <remarks>
        /// Undefined tiles used to prefill empty grid on level start.
        /// </remarks>
        public bool IsPreStartSpawnStage
        {
            set => _isPreStartSpawn = value;
        }

        public void InitializeByContext(IContext context)
        {
            _goals = context.Resolve<GoalsSystem>();
            _completionMatch3ManagerCollection = context.Resolve<GameEventMatch3ManagersCollection>();
            _spawnerSettingsManager = context.Resolve<SpawnerSettingsManager>();
            _levelAnalyticsReporter = context.Resolve<ILevelAnalyticsReporter>();
            
            _allowedTilesCache.Clear();
            _spawnersOnGrid.Clear();
        }

        public void ResetDefaults()
        {
            _usedKinds = default;
            _levelUid = default;
            _isPreStartSpawn = default;
        }

        public M3SpawnSystem Clone()
        {
            return new M3SpawnSystem
            {
                _goals = _goals,
                _completionMatch3ManagerCollection = _completionMatch3ManagerCollection,
                _spawnerSettingsManager = _spawnerSettingsManager,
                _levelAnalyticsReporter = _levelAnalyticsReporter
            };
        }

        public void Setup(ILevel level)
        {
            _usedKinds = level.UsedKinds;
            _levelUid = level.Config.Uid;
        }

        public void ReplaceSpawnerSettingsManager(SpawnerSettingsManager spawnerSettingsManager)
        {
            _spawnerSettingsManager = spawnerSettingsManager;
        }

        public void OnLevelWin()
        {
        }

        /// <summary>
        /// Refresh spawners positions list before turn simulations step.
        /// </summary>
        public void UpdateSpawnerCache(Grid grid, bool initialLoop)
        {
            foreach (var spawnerSettingsOnGrid in _spawnersOnGrid)
            {
                spawnerSettingsOnGrid.Value.Clear();
            }

            _currentGrid = grid;

            int lastErrorSpawnerIndex = -1;

            var spawnerSettings = _spawnerSettingsManager.SpawnerSettings;
            if (spawnerSettings != null && spawnerSettings.Length > 0)
            {
                foreach (var cell in _currentGrid.Cells)
                {
                    if (cell.IsAnyOf(CellState.Spawner))
                    {
                        var spawnerSetting  = SpawnerSettings.FindSpawnerByUid(spawnerSettings, cell.SpawnerUid);
                        if (spawnerSetting != null && spawnerSetting.Uid != cell.SpawnerUid)
                        {
                            // Fallback to default.
                            if (lastErrorSpawnerIndex != cell.SpawnerUid)
                            {
                                BDebug.LogError(LogCat.Match3,
                                    $"Spawner couldn't find spawn config with index {cell.SpawnerUid}. Performed fallback to default spawner config. LevelUid = '{_levelUid}', cell: {cell.Coords.ToString()}, max spawner id = {(spawnerSettings == null ? "NULL" : spawnerSettings.Length.ToString())}");
                                lastErrorSpawnerIndex = cell.SpawnerUid;
                            }
                        }

                        if (spawnerSetting == null)
                        {
                            UnityEngine.Debug.LogError("No spawners found");
                            break;
                        }

                        if (!_spawnersOnGrid.ContainsKey(spawnerSetting))
                        {
                            _spawnersOnGrid[spawnerSetting] = new List<Cell>();
                        }
                        
                        if(!_predeterminedSpaners.ContainsKey(cell.Coords))
                            _predeterminedSpaners[cell.Coords] = new PredeterminedSpawner(spawnerSetting);

                        _spawnersOnGrid[spawnerSetting].Add(cell);
                    }
                }

                foreach (var cells in _spawnersOnGrid)
                {
                    cells.Value.ShuffleRandomSystem();
                }
            }
            else
            {
                // If spawners config doesn't exist, then fallback to old config-less spawning logic.

                var emptyConfig = new SpawnerSettings();
                _spawnersOnGrid[emptyConfig] = new List<Cell>();
                foreach (var cell in _currentGrid.Cells)
                {
                    if (cell.IsAnyOf(CellState.Spawner))
                    {
                        _spawnersOnGrid[emptyConfig].Add(cell);
                    }
                }
                _spawnersOnGrid[emptyConfig].ShuffleRandomSystem();
            }

            if(initialLoop)    
                foreach (var predetermineSpawner in _predeterminedSpaners.Values)
                {
                    predetermineSpawner.Refill(grid);
                }
        }

        /// <summary>
        /// Spawn tiles from spawners for this turn.
        /// UpdateSpawnerCellsCache must be called before this.
        /// </summary>
        /// <returns>True if any tile was spawned</returns>
        public bool TrySpawnNewTilesFromSpawners(IRootSimulationHandler handler, 
            TileHitReactionHandler reactionHandler,
            Dictionary<int, TileKinds> definedTileKinds, 
            bool tweakSpecificSpawnersOnWin)
        {
            var result = false;
            
            foreach (var spawner in _spawnersOnGrid)
            {
                var spawnerSetting = spawner.Key;
                foreach (var cell in spawner.Value)
                {
                    if (!cell.CanAcceptTile()) continue;

                    Tile newTile;
                    var origin = new TileOrigin(Creator.Spawner, cell);
                    if (_isPreStartSpawn)
                    {
                        newTile = OldFallbackTileCreationLogic(origin);
                    }
                    else if (_predeterminedSpaners.TryGetValue(cell.Coords, out var predeterminedSpawner) 
                             && predeterminedSpawner.HasAnyTileToSpawn())
                    {
                        newTile = predeterminedSpawner.CreateTileFor(origin);
                    }
                    else if (spawnerSetting.TilesSettings.Length == 0)
                    {
                        newTile = OldFallbackTileCreationLogic(origin);
                    }
                    else
                    {
                        // This prevents endless booster formation from spawners with single tile option
                        if (tweakSpecificSpawnersOnWin)
                        {
                            var usedTiles = _fallbackTilesToUseOnWin;
                            var tileAsset = usedTiles.DeterministicRandomInSelf();
                            newTile = TileFactory.CreateUnspecifiedTile(_currentGrid.TilesSpawnedCount++, origin, tileAsset, TileState.None, null);
                        }
                        else
                        {
                            newTile = GenerateWeightedRandomTileForSpawning(spawnerSetting, reactionHandler, origin);
                        }
                    }

                    if (newTile != null)
                    {
                        // It is possible that spawner could have tile spawn setting, which can't be spawned at this time,
                        // and if no other tiles configs exist, then nothing will be spawned from cell generator.
                        // This is rare possibility, and it can be avoided by correct level-designing. -VK
                        cell.AddTile(newTile);
                        handler.AddAction(new ActionSpawnFromSpawner(cell.Coords, newTile, definedTileKinds));
                        handler.HandleMovementAction(newTile, cell.Coords);
                        result = true;
                    }
                }
            }

            return result;
        }

        private Tile OldFallbackTileCreationLogic(TileOrigin origin)
        {
            // Spawn undefined tile, which will be later changed to defined kind with help of Assist system.
            var newTile = TileFactory.CreateTile(_currentGrid.TilesSpawnedCount++, TileAsset.Undefined, origin);
            return newTile;
        }

        private Tile GenerateWeightedRandomTileForSpawning(SpawnerSettings spawner, TileHitReactionHandler reactionHandler, TileOrigin origin)
        {
            _allowedTilesCache.Clear();
            foreach (var tileSetting in spawner.TilesSettings)
            {
                bool isAllowedByKind = tileSetting.Asset != TileAsset.Simple || tileSetting.Kind == TileKinds.Undefined || _usedKinds.Contains(tileSetting.Kind);

                bool isAllowedByGoal = true;
                if (isAllowedByKind && tileSetting.SpawnUntilGoalReached)
                {
                    var assetGoalType = tileSetting.GetCorrespondingTileAssetGoalType();
                    var modGoalType = tileSetting.GetCorrespondingModGoalType();

                    var remainingByAssetGoalCount = assetGoalType != GoalType.None && _goals.IsGoalExist(assetGoalType) ? _goals.GetLeftGoalCount(assetGoalType) : 0;
                    var remainingByModGoalCount = modGoalType != GoalType.None && _goals.IsGoalExist(modGoalType) ? _goals.GetLeftGoalCount(modGoalType) : 0;

                    if (remainingByAssetGoalCount > 0 || remainingByModGoalCount > 0)
                    {
                        int assetGoalCount = 0;
                        int modGoalCount = 0;
                        foreach (var cell in _currentGrid.Cells)
                        {
                            if (remainingByAssetGoalCount > 0 && tileSetting.IsTileCorrespondingSpawnSetting(cell.Tile))
                            {
                                assetGoalCount++;
                                if (assetGoalCount >= remainingByAssetGoalCount)
                                {
                                    break;
                                }
                            }

                            if (remainingByModGoalCount > 0 && tileSetting.IsTileCorrespondingSpawnSetting(cell.Tile))
                            {
                                modGoalCount++;
                                if (modGoalCount >= remainingByModGoalCount)
                                {
                                    break;
                                }
                            }
                        }

                        if (remainingByAssetGoalCount > 0)
                        {
                            assetGoalCount += reactionHandler.GetPendingGoalCollectionReactionsForGoal(assetGoalType);
                        }
                        else
                        {
                            modGoalCount += reactionHandler.GetPendingGoalCollectionReactionsForGoal(modGoalType);
                        }

                        isAllowedByGoal = (remainingByAssetGoalCount == 0 || assetGoalCount < remainingByAssetGoalCount)
                            && (remainingByModGoalCount == 0 || modGoalCount < remainingByModGoalCount);
                    }
                    else
                    {
                        isAllowedByGoal = false;
                    }
                }

                bool isAllowedByGridLimit = true;
                if (isAllowedByKind && isAllowedByGoal && tileSetting.MaxAllowedItemsOnGrid > 0)
                {
                    if (tileSetting.MaxAllowedItemsOnGrid <= 100)
                    {
                        int count = 0;
                        foreach (var cell in _currentGrid.Cells)
                        {
                            if (tileSetting.IsTileCorrespondingSpawnSetting(cell.Tile))
                            {
                                count++;
                                if (count >= tileSetting.MaxAllowedItemsOnGrid)
                                {
                                    isAllowedByGridLimit = false;
                                    break;
                                }
                            }
                        }
                    }
                }

                if (isAllowedByKind && isAllowedByGoal && isAllowedByGridLimit)
                {
                    _allowedTilesCache.Add(tileSetting);
                }
            }

            float weightSum = 0;
            if (_allowedTilesCache.Count > 1)
            {
                for (var i = 0; i < _allowedTilesCache.Count; i++)
                {
                    weightSum += _allowedTilesCache[i].ProbabilityWeight;
                }
            }
            else
            if (_allowedTilesCache.Count == 0)
            {
                UnityEngine.Debug.LogError($"Failed to find tile for spawning! SpawnerId={spawner.Uid}, coord={(origin.Cell == null ? "null" : origin.Cell.Coords.ToString())}");
                return null;
            }

            TileSpawnSettings selectedTile = null;
            if (weightSum > 0)
            {
                // Weighted setting random select.
                float randomValue = RandomSystem.Next(weightSum);
                var partialSum = 0f;

                foreach (var tileSetting in _allowedTilesCache)
                {
                    partialSum += tileSetting.ProbabilityWeight;
                    if (partialSum >= randomValue)
                    {
                        selectedTile = tileSetting;
                        break;
                    }
                }
            }
            else
            {
                var index = _allowedTilesCache.Count > 1 ? RandomSystem.Next(_allowedTilesCache.Count) : 0;
                selectedTile = _allowedTilesCache[index];
            }

            // Migrate from old TileAsset
            if (selectedTile.Asset == TileAsset.Simple)
            {
                selectedTile.Asset = selectedTile.Kind.ToSimpleTileAsset();
            }

            return TileFactory.CreateUnspecifiedTile(_currentGrid.TilesSpawnedCount++, origin, selectedTile.Asset, (TileState)selectedTile.Mods, selectedTile.Params);
        }

        public bool SpawnPendingSpecialTiles(IRootSimulationHandler handler,
            Grid grid,
            TileHitReactionHandler reactionHandler,
            SpecialTileCalculationResult specialTileCalculationResult, bool inputMatches)
        {
            if (specialTileCalculationResult.IsEmpty())
                return false;

            bool result = false;

            foreach (var coords in specialTileCalculationResult.CoordsToSpawn)
            {
                result = true;
                var newTile = specialTileCalculationResult.GetTileToSpawn(coords);
                var cellToSpawnIn = grid.GetCell(coords);


                if (newTile.IsNull())
                {
#if BBB_LOG
                    M3Debug.LogError("[PopSystem]:\n Trying to spawn Null tile in " + cellToSpawnIn);
#endif
                    continue;
                }


                if (cellToSpawnIn == null)
                {
                    continue;
                }

                var damageSource = inputMatches ? DamageSource.InputMatch : DamageSource.AutoMatch;
                var cellHitParams = new HitWaitParams(cellToSpawnIn.Coords, damageSource);
                if (cellToSpawnIn.HasTile())
                {
                    var oldTile = cellToSpawnIn.Tile;
                    cellToSpawnIn.HardRemoveTile(0, cellHitParams);
                    reactionHandler.RegisterTileShouldReactOnDie(grid, handler, cellHitParams, cellToSpawnIn.Coords, oldTile);
                    handler.AddAction(new ActionNotifyNearCellAboutMatch(cellToSpawnIn.Coords, cellHitParams));
                    handler.AddAction(new ActionVisualizeTileDestroy(cellToSpawnIn.Coords, oldTile, cellHitParams));

                    if(oldTile.IsInTransition)
                        handler.FinalHandleMovementAction(oldTile, cellToSpawnIn.Coords);

                    handler.AddAction(new ActionRemoveTile(target: cellToSpawnIn.Coords, tile: oldTile, cellHitParams));
                }

                cellToSpawnIn.AddTile(newTile);
                _levelAnalyticsReporter.RegisterPowerUpCreatedDuringGame();
                handler.AddAction(new ActionSpawn(coords, newTile, hitWaitParams: cellHitParams));
            }

            return result;
        }
    }
}
