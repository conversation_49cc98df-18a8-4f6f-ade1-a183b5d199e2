using UnityEngine;

namespace BBB
{
    public class TileDestroyEffect : B<PERSON>M<PERSON>Behaviour, IPoolItem
    {
        [SerializeField] private SpriteRenderer _splashImage;
        [SerializeField] private Animation _animation;
        [SerializeField] private ParticleSystemRenderer _psWithVariableMaterialRenderer;
        [SerializeField] private ParticleSystem _ps;

        public void SetExplosionPieceMaterial(Material material)
        {
            _psWithVariableMaterialRenderer.material = material;
        }

        void IPoolItem.OnInstantiate()
        {
            gameObject.SetActive(false);
            if (_animation)
            {
                _animation.Rewind();
            }
        }

        void IPoolItem.OnSpawn()
        {
            gameObject.SetActive(true);
            _ps.Play();
        }

        void IPoolItem.OnRelease()
        {
            gameObject.SetActive(false);
            if (_animation)
            {
                _animation.Rewind();
            }
            
            _ps.Clear();
        }
    }
}
