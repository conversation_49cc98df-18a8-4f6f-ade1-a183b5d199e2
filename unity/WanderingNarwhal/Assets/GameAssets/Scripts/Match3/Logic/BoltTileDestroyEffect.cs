using System.Collections.Generic;
using UnityEngine;
using BBB.Core;

namespace BBB
{
    public class BoltTileDestroyEffect : ActivePoolItem
    {
        [SerializeField] private List<SpriteRenderer> _images;
        [SerializeField] private List<ParticleSystemRenderer> _psWithVariableMaterialRenderers;
        public float destroyTime;
        
        public void SetExplosionPieceMaterial(Material material)
        {
            foreach (var psRenderer in _psWithVariableMaterialRenderers)
            {
                psRenderer.material = material;
            }
        }
        
        public void SetColor(Color color)
        {
            foreach (var image in _images)
                image.color = color;
        }
    }
}
