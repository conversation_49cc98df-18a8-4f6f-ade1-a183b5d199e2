using System.Collections.Generic;
using BBB;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    public class SingleAnimationEffectWithColoredParticles : SingleAnimationEffect
    {
        [SerializeField]
        public List<ParticleSystem> _particleSystems;

        public override void ApplyParameters(FxOptionalParameters prm)
        {
            if (_particleSystems.Count == 0)
            {
                return;
            }

            foreach (var ps in _particleSystems)
            {
                if (ps == null)
                    continue;
                
                var mainModule = ps.main;
                mainModule.startColor = prm.col;
            }
        }
    }
}