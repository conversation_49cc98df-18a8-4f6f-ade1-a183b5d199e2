using BBB;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using System.Collections.Generic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class BananaTile : Tile
    {
        private const int BananaHp = 1;
        private static readonly (long, int) AssistState = ((long)GoalType.Banana, BananaHp);
        private const string GeneralizedLayer = "Banana";
        private const string TilePrefabName = "Banana";
        public override string PrefabName => TilePrefabName;

        public static TileResourceInfo ResourceInfo => new()
        {
            State = TileLayerState.Banana,
            PrefabName = TilePrefabName,
            DefaultOrder = 20
        };

        public BananaTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.Banana;
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            yield return AssistState;
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }

        public override bool IsRelatedToGoal(GoalType goal)
        {
            return goal == GoalType.Banana || base.IsRelatedToGoal(goal);
        }

        public override IEnumerable<FxType> GetFxTypes()
        {
            foreach (var fx in base.GetFxTypes())
            {
                yield return fx;
            }

            yield return FxType.BananaAppear;
            yield return FxType.BananaDestroy;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }

            yield return ResourceInfo;
        }

        protected override DamageSource GetAllowedDamage()
        {
            return DamageSource.AdjacentWithPowerUp;
        }
    }
}