using BBB;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public abstract class ColorCrateTile : Tile
    {
        private (long, int value) _assistState = ((long) GoalType.ColorCrate, DefaultHp);
        private const string ColorCrateOne = "ColorCrateOne";
        private const string ColorCrateTwo = "ColorCrateTwo";
        private const string ColorCrateThree = "ColorCrateThree";

        private const string TilePrefabName = "ColorCrate";
        public override string PrefabName => TilePrefabName;

        public override bool IsZeroGravity => true;

        private static TileResourceInfo ResourceInfo => new ()
        {
            State = TileLayerState.ColorCrate,
            PrefabName = TilePrefabName,
            DefaultOrder = 30
        };
        
        protected ColorCrateTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.ColorCrate;
            BoostersApplicability = BoosterItem.Shovel | BoosterItem.Balloon | BoosterItem.Wind | BoosterItem.Rain |
                                    BoosterItem.Vertical | BoosterItem.Horizontal;
        }

        protected override DamageSource GetAllowedDamage()
        {
            return DamageSource.AdjacentWithPowerUp;
        }

        protected override bool CanBeDamagedByTileKind(DamageSource damageSource, TileKinds damageTileKind)
        {
            if ((damageSource & DamageSource.TileKindDependent) != 0)
            {
                return Kind == damageTileKind;
            }

            return base.CanBeDamagedByTileKind(damageSource, damageTileKind);
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            var adjacentHp = GetParam(TileParamEnum.AdjacentHp);

            if (adjacentHp >= 1)
            {
                yield return ColorCrateOne;
            }

            if (adjacentHp >= 2)
            {
                yield return ColorCrateTwo;
            }

            if (adjacentHp >= 3)
            {
                yield return ColorCrateThree;
            }
        }

        public override bool IsRelatedToGoal(GoalType goal)
        {
            return goal == GoalType.ColorCrate || base.IsRelatedToGoal(goal);
        }

        public override IEnumerable<FxType> GetFxTypes()
        {
            foreach (var fx in base.GetFxTypes())
            {
                yield return fx;
            }
            
            yield return FxType.ColorCrateDestroy;
            yield return FxType.ColorCrateSecondLayerRemove;
            yield return FxType.ColorCrateThirdLayerRemove;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }

            yield return ResourceInfo;
        }
    }
}