using BBB;
using System.Collections.Generic;
using BBB.Match3.Renderer;
using BBB.Core;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;
using BBB.Match3.Logic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class SkunkTile : Tile
    {
        private const string GeneralizedLayer = "Skunk";
        private const string TilePrefabName = "Skunk";
        public override string PrefabName => TilePrefabName;

        private static TileResourceInfo ResourceInfo => new()
        {
            State = TileLayerState.Skunk,
            PrefabName = TilePrefabName,
            DefaultOrder = 40
        };
        
        public override bool IsZeroGravity => true;

        public SkunkTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.Skunk;
        }
        
        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            if (simulationContext.CellsToDamageQueue == null)
            {
                return true;
            }

            var removeRandomTileQueue = SpecialTileSystem.RemoveWithSkunkHit(this, hitContext.Hit, hitContext.Cell,
                hitContext.HitWaitParams, simulationContext.SettleTileSystem, simulationContext.Handler);

            if (removeRandomTileQueue != null)
            {
                simulationContext.CellsToDamageQueue.Append(removeRandomTileQueue);
            }
            return true;
        }

        protected override bool CanDieRepeatedly()
        {
            return true;
        }
        
        public override bool DisallowCellBackgroundSpawn()
        {
            return true;
        }

        public override bool PreventBackgroundInteraction()
        {
            return true;
        }
        
        public override bool IsIndestructibleBlocker()
        {
            return true;
        }

        public override void CheckInvalidStates(ILevel level, Cell cell)
        {
            base.CheckInvalidStates(level, cell);

            if (HasParam(TileParamEnum.RestoresCount))
            {
                BDebug.LogError(LogCat.Match3,
                    $"Level '{level.Config.Uid}' contains Skunk with {GetParam(TileParamEnum.RestoresCount)} HP parameter");
            }
        }

        public override IEnumerable<FxType> GetFxTypes()
        {
            foreach (var fx in base.GetFxTypes())
            {
                yield return fx;
            }
            
            yield return FxType.SkunkAttack;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }

            yield return ResourceInfo;
        }

        protected override DamageSource GetAllowedDamage()
        {
            return DamageSource.AdjacentWithPowerUp;
        }
    }
}