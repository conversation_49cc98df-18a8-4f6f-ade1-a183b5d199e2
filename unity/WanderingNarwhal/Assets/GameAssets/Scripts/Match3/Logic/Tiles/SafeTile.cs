using BBB;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class SafeTile : Tile
    {
        private (long, int value) _assistState = ((long)GoalType.Safe, DefaultHp);
        private const string GeneralizedLayer = "Safe";
        private const string TilePrefabName = "SafeTile";
        public override string PrefabName => TilePrefabName;

        private static TileResourceInfo ResourceInfo => new()
        {
            State = TileLayerState.Safe,
            PrefabName = TilePrefabName,
            DefaultOrder = 109
        };

        public override bool IsZeroGravity => true;

        public SafeTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.Safe;
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }

        public override bool IsRelatedToGoal(GoalType goal)
        {
            return goal == GoalType.Safe || base.IsRelatedToGoal(goal);
        }

        public override IEnumerable<FxType> GetFxTypes()
        {
            foreach (var fx in base.GetFxTypes())
            {
                yield return fx;
            }

            yield return FxType.SafeTileHideLayer1;
            yield return FxType.SafeTileHideLayer2;
            yield return FxType.SafeTileHideLayer3;
            yield return FxType.SafeTileHideLayer4;
            yield return FxType.SafeTileHideLayer5;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }

            yield return ResourceInfo;
        }

        protected override DamageSource GetAllowedDamage()
        {
            return DamageSource.PowerUp;
        }
    }
}