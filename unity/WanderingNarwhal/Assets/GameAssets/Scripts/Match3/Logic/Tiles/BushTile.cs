using BBB;
using BBB.CellTypes;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using System.Collections.Generic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class BushTile : Tile
    {
        private const int BushGrassSpawnCount = 16; //4x4 grass
        private static readonly (long, int) GrassAssistState = ((long)GoalType.Backgrounds, BushGrassSpawnCount);
        private (long, int value) _assistState = ((long)GoalType.Bush, DefaultHp);
        private const string GeneralizedLayer = "Bush";
        private const string TilePrefabName = "BushTile";
        public override string PrefabName => TilePrefabName;

        private static TileResourceInfo ResourceInfo => new()
        {
            State = TileLayerState.Bush,
            PrefabName = TilePrefabName,
            DefaultOrder = 109
        };

        private static readonly int[] BushSpreadPositions =
        {
            8, 6, 5, 11,
            12, 3, 0, 15,
            14, 1, 2, 13,
            9, 7, 4, 10
        };

        public override bool IsZeroGravity => true;

        public BushTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.Bush;
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
            yield return GrassAssistState;
        }

        public override bool SpawnsCellBackgrounds()
        {
            return true;
        }

        public override bool HasReactionAndGoalCollection()
        {
            return true;
        }

        public override (CellState cellState, GoalType goalType, FxType fxType, int[] positionList, int positionCount,
            Coords minOffset, Coords maxOffset) GetSpawnConfiguration()
        {
            return (CellState.BackOne, GoalType.Backgrounds, FxType.GrassAnticipation, BushSpreadPositions,
                BushGrassSpawnCount, new Coords(-1, -1), new Coords(2, 2));
        }

        public override bool IsRelatedToGoal(GoalType goal)
        {
            return goal == GoalType.Bush || base.IsRelatedToGoal(goal);
        }

        public override IEnumerable<FxType> GetFxTypes()
        {
            foreach (var fx in base.GetFxTypes())
            {
                yield return fx;
            }

            yield return FxType.BushTileHideLayer1;
            yield return FxType.BushTileHideLayer2;
            yield return FxType.BushTileHideLayer3;
            yield return FxType.BushTileHideLayer4;
            yield return FxType.BushTileHideLayer5;
            yield return FxType.BackgroundRemove;
            yield return FxType.GrassAnticipation;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }

            yield return ResourceInfo;
        }

        protected override DamageSource GetAllowedDamage()
        {
            return DamageSource.AdjacentWithPowerUp;
        }
    }
}