using BBB;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes.AdditionalHitInfos;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;
using GameAssets.Scripts.Match3.Settings;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class DynamiteBoxTile : Tile
    {
        private const int NumberOfSticks = 8;
        private const int BottomRowStickLimit = 4;
        private (long, int value) _assistState = ((long)GoalType.DynamiteStick, DefaultHp);
        private const string GeneralizedLayer = "DynamiteStick";
        private const string TilePrefabName = "DynamiteBoxTile";
        public override string PrefabName => TilePrefabName;
        private static int _dynamiteBoxId;

        private static TileResourceInfo ResourceInfo => new()
        {
            State = TileLayerState.DynamiteBox,
            PrefabName = TilePrefabName,
            DefaultOrder = 109
        };

        public override bool IsZeroGravity => true;

        public DynamiteBoxTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.DynamiteBox;
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }

        public override bool HasDieReaction()
        {
            return false;
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            if (TryApplyDynamiteBoxTileDamage(simulationContext.InputParams, hitContext.MainCell, hitContext.Hit,
                    hitContext.HitWaitParams.DamageSource, simulationContext.Handler))
            {
                return base.TryApplyAdjacentDamage(simulationContext, hitContext);
            }

            var reducedAdjacentHp = GetParam(TileParamEnum.DynamiteSticksCount);
            simulationContext.Handler.AddAction(new ActionChangeTileParam(Id, hitContext.MainCell.Coords,
                new List<(TileParamEnum, int)> { new(TileParamEnum.AdjacentHp, reducedAdjacentHp) },
                hitContext.HitWaitParams));

            return true;
        }

        private static void RemoveRandomDynamiteStick(ref int state, ref int count)
        {
            var indices = new List<int>();
            for (var i = 0; i < NumberOfSticks; i++)
            {
                var subState = DynamiteBoxTileLayer.GetColorNumFromState(state, i);
                if (subState != 0 && i < BottomRowStickLimit)
                {
                    indices.Add(i);
                }
            }

            if (indices.Count == 0)
            {
                for (var i = BottomRowStickLimit; i < NumberOfSticks; i++)
                {
                    var subState = DynamiteBoxTileLayer.GetColorNumFromState(state, i);
                    if (subState != 0)
                    {
                        indices.Add(i);
                    }
                }
            }

            if (indices.Count == 0) return;

            var chosenIndex = indices[RandomSystem.Next(indices.Count)];
            var colorNum = DynamiteBoxTileLayer.GetColorNumFromState(state, chosenIndex);
            var newState = DynamiteBoxTileLayer.RemoveColorFromState(state, colorNum);

            if (newState == state) return;

            state = newState;
            count--;
        }

        private bool TryApplyDynamiteBoxTileDamage(SimulationInputParams inputParams, Cell cell, Hit hit,
            DamageSource damageSource, IRootSimulationHandler handler)
        {
            if (inputParams.InitialLoop) return false;
            if (!HasParam(TileParamEnum.DynamiteBoxColors)) return false;

            var dynamiteSticksCount = GetParam(TileParamEnum.DynamiteSticksCount);
            var count = dynamiteSticksCount;
            var state = GetParam(TileParamEnum.DynamiteBoxColors);
            var damageColorNum = DynamiteBoxTileLayer.ColorToEncodedNum(hit.SourceKind);

            if ((damageSource & DamageSource.AdjacentGeneral) != 0 && damageColorNum != 0)
            {
                var newState = DynamiteBoxTileLayer.RemoveColorFromState(state, damageColorNum);
                if (newState != state)
                {
                    state = newState;
                    count--;
                }
            }
            else
            {
                RemoveRandomDynamiteStick(ref state, ref count);
            }

            if (count >= dynamiteSticksCount) return false;
            SetParam(TileParamEnum.DynamiteBoxColors, state);
            SetParam(TileParamEnum.DynamiteSticksCount, count);

            var tileParamList = new List<(TileParamEnum, int)>
            {
                new(TileParamEnum.AdjacentHp, count),
                new(TileParamEnum.DynamiteSticksCount, count),
                new(TileParamEnum.DynamiteBoxColors, state)
            };

            handler.AddAction(new ActionChangeTileParam(Id, cell.Coords, tileParamList, hit.GetHitParams()));
            return true;
        }

        protected override DamageSource GetAllowedDamage()
        {
            return DamageSource.AdjacentWithPowerUp;
        }

        protected override bool CanBeDamagedByTileKind(DamageSource damageSource, TileKinds damageTileKind)
        {
            if ((damageSource & DamageSource.TileKindDependent) != 0 &&
                HasParam(TileParamEnum.DynamiteBoxColors))
            {
                var damageColorNum = DynamiteBoxTileLayer.ColorToEncodedNum(damageTileKind);
                var state = GetParam(TileParamEnum.DynamiteBoxColors);
                return DynamiteBoxTileLayer.IsColorStateExistInState(state, damageColorNum);
            }

            return base.CanBeDamagedByTileKind(damageSource, damageTileKind);
        }

        public override bool IsAffectingCellState(DamageSource damageSource, bool tileRemoved = false)
        {
            return tileRemoved
                    ? damageSource == DamageSource.MultiBomb
                    : damageSource != DamageSource.MultiBomb;
        }

        public override bool IsRelatedToGoal(GoalType goal)
        {
            return goal == GoalType.DynamiteStick || base.IsRelatedToGoal(goal);
        }

        public override IEnumerable<FxType> GetFxTypes()
        {
            foreach (var fx in base.GetFxTypes())
            {
                yield return fx;
            }

            yield return FxType.DynamiteBoxTileDestroy;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }

            yield return ResourceInfo;
        }

        public override Queue ExecuteTileDamageEffect(IRootSimulationHandler events, Grid grid, Cell sourceCell,
            HitWaitParams hitWaitParams)
        {
            var q = new Queue(GeneralizedLayer);

            var dynamiteId = _dynamiteBoxId++;
            var sourceCoords = sourceCell.Coords;

            var coordsList = new List<Coords>();

            foreach (var cell in grid.Cells)
            {
                var coord = cell.Coords;
                var mainCell = cell.GetMainCellReference(out _);

                if (mainCell != null && sourceCell != mainCell)
                {
                    coordsList.Add(coord);
                }
            }

            coordsList.Sort((a, b) =>
                a.DistanceFrom(sourceCoords.ToUnityVector2())
                    .CompareTo(b.DistanceFrom(sourceCoords.ToUnityVector2()))
            );

            foreach (var coord in coordsList)
            {
                if (grid.TryGetCell(coord, out var cell))
                {
                    var whirlpoolInfo = new WhirlpoolInfo(dynamiteId);
                    var newHit = new Hit<Cell>(
                        immuneTiles: null,
                        target: cell,
                        busyWait: M3Constants.BusyTimeWhirlpool,
                        damageSource: DamageSource.Dynamite,
                        boostInfo: whirlpoolInfo,
                        coords: cell.Coords,
                        hitSourceUid: dynamiteId
                    );

                    q.AppendHit(1, newHit);
                }
            }

            events.AddAction(new ActionDynamiteBlast(dynamiteId, coordsList, sourceCell.Coords));

            return q;
        }

        public override bool GetExpectedGoalTargetCountFor(GoalType goal, ref int count)
        {
            count += GetParam(TileParamEnum.AdjacentHp);
            return false;
        }
    }
}