using System.Collections.Generic;
using BBB;
using BBB.Match3.Renderer;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class RowBreakerTile : LineBreakerTile
    {
        private const string GeneralizedLayer = "HorizontalLb";

        public static TileResourceInfo ResourceInfo => new ()
        {
            State = TileLayerState.HorizontalLb,
            PrefabName = TilePrefabName,
            DefaultOrder = 20
        };

        public RowBreakerTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.RowBreaker;
        }
        
        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }

        public override int GetSpawnPriority()
        {
            return 7;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }

            yield return ResourceInfo;
        }
    }
}