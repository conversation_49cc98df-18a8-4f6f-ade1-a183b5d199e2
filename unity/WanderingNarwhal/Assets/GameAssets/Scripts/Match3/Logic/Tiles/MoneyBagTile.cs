using BBB;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using System.Collections.Generic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class MoneyBagTile : Tile
    {
        private const int MoneyBagHp = 1;
        private static readonly (long, int) AssistState = ((long)GoalType.MoneyBag, MoneyBagHp);

        private const string MoneyBag = "MoneyBag";
        private const string VaseOne = "VaseOne";
        private const string VaseTwo = "VaseTwo";
        private const string VaseThree = "VaseThree";
        private const string TilePrefabName = "MoneyBag";
        public override string PrefabName => TilePrefabName;

        private static TileResourceInfo ResourceInfo => new()
        {
            State = TileLayerState.MoneyBag,
            PrefabName = TilePrefabName,
            DefaultOrder = 20
        };

        public MoneyBagTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.MoneyBag;
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            yield return AssistState;
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            if (IsNoneOf(TileState.VaseMod))
            {
                yield return MoneyBag;
            }

            if (IsAnyOf(TileState.VaseMod))
            {
                var vaseLayerCount = GetParam(TileParamEnum.VaseLayerCount);

                if (vaseLayerCount >= 1)
                {
                    yield return VaseOne;
                }

                if (vaseLayerCount >= 2)
                {
                    yield return VaseTwo;
                }

                if (vaseLayerCount >= 3)
                {
                    yield return VaseThree;
                }
            }
        }

        public override bool IsRelatedToGoal(GoalType goal)
        {
            return goal == GoalType.MoneyBag || base.IsRelatedToGoal(goal);
        }

        public override IEnumerable<FxType> GetFxTypes()
        {
            foreach (var fx in base.GetFxTypes())
            {
                yield return fx;
            }
            
            yield return FxType.MoneyBagDestroy;
            yield return FxType.MoneyBagGoal;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }

            yield return ResourceInfo;
        }

        protected override DamageSource GetAllowedDamage()
        {
            return DamageSource.AdjacentWithPowerUp;
        }
    }
}