using BBB;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class FrameTile : Tile
    {
        private (long, int value) _assistState = ((long) GoalType.Animal, DefaultHp);
        private const string FrameOne = "FrameOne";
        private const string FrameTwo = "FrameTwo";
        private const string FrameThree = "FrameThree";
        private const string Animal = "Animal";
        private const string TilePrefabName = "Frame";
        public override string PrefabName => TilePrefabName;

        public override bool IsZeroGravity => true;

        private static TileResourceInfo ResourceInfo => new()
        {
            State = TileLayerState.Frame,
            PrefabName = TilePrefabName,
            DefaultOrder = 30
        };
        
        public FrameTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.Frame;
            BoostersApplicability = BoosterItem.Shovel | BoosterItem.Balloon | BoosterItem.Wind | BoosterItem.Rain |
                                    BoosterItem.Vertical | BoosterItem.Horizontal;
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            if (IsAnyOf(TileState.AnimalMod))
            {
                var tileClone = Clone();
                
                Remove(TileState.AnimalMod);
                simulationContext.Handler.AddAction(new ActionRemoveState(Id, hitContext.MainCell.Coords,
                    TileState.AnimalMod, hitContext.HitWaitParams));
                simulationContext.PopSystem.MarkAnimalReceivedDamage(hitContext.MainCell.Coords, Id, tileClone, hitContext.HitWaitParams);
            }

            return base.TryApplyAdjacentDamage(simulationContext, hitContext);
        }

        public override bool HasDieReaction()
        {
            return IsAnyOf(TileState.AnimalMod);
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            var adjacentHp = GetParam(TileParamEnum.AdjacentHp);

            if (adjacentHp >= 1)
            {
                yield return FrameOne;
            }

            if (adjacentHp >= 2)
            {
                yield return FrameTwo;
            }

            if (adjacentHp >= 3)
            {
                yield return FrameThree;
            }
            
            if (IsAnyOf(TileState.AnimalMod))
            {
                yield return Animal;
            }
        }

        public override IEnumerable<FxType> GetFxTypes()
        {
            foreach (var fx in base.GetFxTypes())
            {
                yield return fx;
            }
            
            yield return FxType.FrameRemoval;
            yield return FxType.FrameLayerRemoval;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }

            yield return ResourceInfo;
        }

        protected override DamageSource GetAllowedDamage()
        {
            return DamageSource.AdjacentWithPowerUp;
        }
    }
}