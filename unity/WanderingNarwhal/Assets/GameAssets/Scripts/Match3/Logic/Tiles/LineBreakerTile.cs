using System.Collections.Generic;
using BBB;
using BBB.Match3;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public abstract class LineBreakerTile : BoosterTile
    {
        public LineBreakerTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileParams)
        {
            State |= TileState.LineBreaker;
        }
    }
}