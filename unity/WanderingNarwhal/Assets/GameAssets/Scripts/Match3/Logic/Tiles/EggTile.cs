using BBB;
using System.Collections.Generic;
using BBB.Core;
using BBB.Match3.Renderer;
using BBB.Match3.Logic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class EggTile : Tile
    {
        private const int BirdSpawnCount = 1; //1 Bird
        private (long, int value) _assistState = ((long)GoalType.Bird, DefaultHp);
        private const string EggOne = "EggOne";
        private const string EggTwo = "EggTwo";
        private const string TilePrefabName = "Egg";
        public override string PrefabName => TilePrefabName;

        private static TileResourceInfo ResourceInfo => new()
        {
            State = TileLayerState.Egg,
            PrefabName = TilePrefabName,
            DefaultOrder = 20
        };

        public EggTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.Egg;
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.EggLayerCount) + BirdSpawnCount;
            yield return _assistState;
        }

        protected override void AddMandatoryParamsTile()
        {
            if (GetParam(TileParamEnum.EggLayerCount) <= 0)
            {
                BDebug.LogError(LogCat.Match3, "Spawned egg tile doesn't have egg layer parameter!");
                SetParam(TileParamEnum.EggLayerCount, 1);
            }

            SetParam(TileParamEnum.TileToSpawnFromReaction, (int)TileAsset.Bird);
            SetParam(TileParamEnum.TileCreateCountForReaction, BirdSpawnCount);
            base.AddMandatoryParamsTile();
        }

        public override bool SpawnsTileAtRandomPosition()
        {
            return true;
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            var eggLayerCount = GetParam(TileParamEnum.EggLayerCount);

            if (eggLayerCount >= 1)
            {
                yield return EggOne;
            }

            if (eggLayerCount >= 2)
            {
                yield return EggTwo;
            }
        }

        public override bool IsRelatedToGoal(GoalType goal)
        {
            return goal == GoalType.Egg || goal == GoalType.Bird || base.IsRelatedToGoal(goal);
        }

        public override IEnumerable<FxType> GetFxTypes()
        {
            foreach (var fx in base.GetFxTypes())
            {
                yield return fx;
            }

            yield return FxType.EggDestroy;
            yield return FxType.EggLayerRemove;
            yield return FxType.BirdAppear;
            yield return FxType.BirdDestroy;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }

            yield return ResourceInfo;
        }

        public override IEnumerable<TileResourceInfo> GetRelatedResourceInfo()
        {
            yield return BirdTile.ResourceInfo;
        }

        protected override DamageSource GetAllowedDamage()
        {
            return DamageSource.AdjacentWithPowerUp;
        }

        public override IEnumerable<(TileParamEnum, TileState)> GetArmorParams()
        {
            foreach (var (armorParam, modState) in base.GetArmorParams())
            {
                yield return (armorParam, modState);
            }

            yield return (TileParamEnum.EggLayerCount, TileState.None);
        }

        protected override void ArmorDestroyed(TileState modState)
        {
            base.ArmorDestroyed(modState);

            if (modState == TileState.None)
            {
                _hp--;
            }
        }
    }
}