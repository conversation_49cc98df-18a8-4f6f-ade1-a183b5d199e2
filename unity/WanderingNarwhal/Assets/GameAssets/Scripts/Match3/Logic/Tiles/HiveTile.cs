using BBB;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.GoalsService;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class HiveTile : Tile
    {
        private static readonly List<Coords> AllBeeHives = new();
        private const int BeeSpawnCount = 1;
        private const string ExhaustedHive = "ExhaustedHive";
        private const string Hive = "Hive";
        private const string TilePrefabName = "HiveTile";
        public override string PrefabName => TilePrefabName;

        private static TileResourceInfo ResourceInfo => new()
        {
            State = TileLayerState.Hive,
            PrefabName = TilePrefabName,
            DefaultOrder = 40
        };

        public override bool IsZeroGravity => true;

        public HiveTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.Hive;
        }

        protected override void AddMandatoryParamsTile()
        {
            SetParam(TileParamEnum.TileToSpawnFromReaction, (int)TileAsset.Bee);
            SetParam(TileParamEnum.TileCreateCountForReaction, BeeSpawnCount);
            base.AddMandatoryParamsTile();
        }

        public override bool CheckApplicability(BoosterItem boosterItem, Cell cell, Grid grid)
        {
            if ((State & (TileState.ChainMod | TileState.IceCubeMod | TileState.SandMod)) != 0) return true;
            return GetParam(TileParamEnum.BeeHiveOutOfBeesFlag) <= 0;
        }

        public override bool SpawnsTileAtRandomPosition()
        {
            return true;
        }

        protected override bool CanDieRepeatedly()
        {
            return true;
        }

        public override bool DisallowCellBackgroundSpawn()
        {
            return true;
        }

        public override bool PreventBackgroundInteraction()
        {
            return true;
        }

        public override bool IsIndestructibleBlocker()
        {
            return true;
        }

        public static int CanHitBeehive(Coords coords, Grid grid, GoalsSystem goalSystem,
            TileHitReactionHandler reactionHandler)
        {
            var beeHiveCell = grid.GetCell(coords);

            if (beeHiveCell != null && beeHiveCell.Tile != null &&
                beeHiveCell.Tile.GetParam(TileParamEnum.BeeHiveOutOfBeesFlag) == 1)
            {
                return 0;
            }

            var count = goalSystem.GetLeftGoalCount(GoalType.Bee);
            if (count <= 0) return 0;

            foreach (var action in TileSpawnHandler.DelayedSpawnReactions)
            {
                if (action.Tile is BeeTile)
                {
                    count--;
                }
            }

            foreach (var action in reactionHandler.DieReactionsCache)
            {
                if (action.Tile is BeeTile)
                {
                    count--;
                }
            }

            foreach (var cell in grid.Cells)
            {
                if (!cell.Tile.IsNull() && cell.Tile.Speciality == TileSpeciality.Bee)
                {
                    count--;
                }
            }

            return count;
        }

        public static void MarkHivesOnGridOutOfBeesIfNeeded(Grid grid, IRootSimulationHandler events,
            GoalsSystem goalSystem, TileHitReactionHandler reactionHandler)
        {
            var isOutOfBees = false;
            var targetLeft = goalSystem.GetLeftGoalCount(GoalType.Bee);

            if (targetLeft > 0)
            {
                var spawnedCount = 0;

                foreach (var m in TileSpawnHandler.DelayedSpawnReactions)
                {
                    if (m.Tile is BeeTile)
                    {
                        spawnedCount++;
                    }
                }

                foreach (var cell in grid.Cells)
                {
                    if (!cell.Tile.IsNull() && cell.Tile is BeeTile)
                    {
                        spawnedCount++;
                    }
                }

                if (spawnedCount >= targetLeft)
                {
                    isOutOfBees = true;
                }
            }
            else
            {
                isOutOfBees = true;
            }

            if (isOutOfBees)
            {
                foreach (var cell in grid.Cells)
                {
                    if (!cell.Tile.IsNull() && cell.Tile is HiveTile)
                    {
                        AllBeeHives.Add(cell.Coords);
                    }
                }
            }

            if (AllBeeHives.Count == 0) return;
            reactionHandler.NewBusyCells ??= new List<Cell>(AllBeeHives.Count);
            events.AddAction(new ActionSyncCoords(new List<Coords>(AllBeeHives)));

            foreach (var beeHiveCoords in AllBeeHives)
            {
                var cell = grid.GetCell(beeHiveCoords);
                cell.Tile.SetParam(TileParamEnum.BeeHiveOutOfBeesFlag, 1);
                events.AddAction(new ActionChangeTileParam(cell.Tile.Id, cell.Coords,
                    new List<(TileParamEnum, int)> { new(TileParamEnum.BeeHiveOutOfBeesFlag, 1) }));
                reactionHandler.NewBusyCells.Add(cell);
            }

            AllBeeHives.Clear();
        }

        public override int GetMaxHits(Cell cell, Grid grid, GoalsSystem goalSystem, TileHitReactionHandler reactionHandler, DamageSource damageSource)
        {
            return CanHitBeehive(cell.Coords, grid, goalSystem, reactionHandler);
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            if (GetParam(TileParamEnum.BeeHiveOutOfBeesFlag) == 1)
            {
                yield return ExhaustedHive;
            }

            yield return Hive;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }

            yield return ResourceInfo;
        }

        public override IEnumerable<TileResourceInfo> GetRelatedResourceInfo()
        {
            yield return BeeTile.ResourceInfo;
        }

        public override bool IsRelatedToGoal(GoalType goal)
        {
            return goal == GoalType.Bee || base.IsRelatedToGoal(goal);
        }

        public override IEnumerable<FxType> GetFxTypes()
        {
            foreach (var fx in base.GetFxTypes())
            {
                yield return fx;
            }

            yield return FxType.BeeAppear;
            yield return FxType.BeeDestroy;
        }

        protected override DamageSource GetAllowedDamage()
        {
            return DamageSource.AdjacentWithPowerUp;
        }

        public override bool GetExpectedGoalTargetCountFor(GoalType goal, ref int count)
        {
            return true;
        }
    }
}