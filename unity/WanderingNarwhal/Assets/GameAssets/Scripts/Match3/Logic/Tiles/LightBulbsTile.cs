using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class LightBulbsTile : Tile
    {
        private (long, int value) _assistState = ((long) GoalType.LightBulbs, DefaultHp);
        private const string GeneralizedLayer = "LightBulbs";

        public LightBulbsTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileParams)
        {
            Speciality = TileSpeciality.LightBulbs;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.AdjacentGeneral;
            State |= TileState.LightBulbs;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }
        
        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }
        
        public override bool HasDieReaction()
        {
            return false;
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            if (GetParam(TileParamEnum.AdjacentHp) <= 0)
            {
                return false;
            }
            
            var currentColor = GetParam(TileParamEnum.LightBulbsColor);
            int randomColor;
            do
            {
                randomColor = (int)simulationContext.InputParams.UsedKinds.DeterministicRandomInSelf();
            } 
            while (randomColor == currentColor);
            
            SetParam(TileParamEnum.LightBulbsColor, randomColor);

            var tileParamList = new List<(TileParamEnum, int)>
            {
                new(TileParamEnum.LightBulbsColor, randomColor)
            };

            simulationContext.Handler.AddAction(new ActionChangeTileParam(Id, hitContext.MainCell.Coords,
                tileParamList, hitContext.Hit.GetHitParams()));

            return base.TryApplyAdjacentDamage(simulationContext, hitContext);
        }
        
        protected override bool CanBeDamagedBy(DamageSource damageSource, TileKinds damageTileKind, DamageSource totalAllowedDamageSource)
        {
            if (IsSameTileKindDamage(damageSource, totalAllowedDamageSource) && HasParam(TileParamEnum.LightBulbsColor))
            {
                var state = GetParam(TileParamEnum.LightBulbsColor);
                if (damageTileKind != (TileKinds)state)
                {
                    return false;
                }
            }
            return base.CanBeDamagedBy(damageSource, damageTileKind, totalAllowedDamageSource);
        }
    }
}