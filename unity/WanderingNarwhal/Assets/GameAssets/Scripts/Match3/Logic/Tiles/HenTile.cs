using BBB;
using BBB.Match3;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using System.Collections.Generic;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class HenTile : Tile
    {
        private const int ChickenSpawnCount = 3;
        private const int MaxChickenSkinIndex = 2;
        private (long, int value) _assistState = ((long)GoalType.Chicken, DefaultHp);
        private const string HenOne = "HenOne";
        private const string HenTwo = "HenTwo";

        private const string TilePrefabName = "HenTile";
        public override string PrefabName => TilePrefabName;

        private static TileResourceInfo ResourceInfo => new()
        {
            State = TileLayerState.Hen,
            PrefabName = TilePrefabName,
            DefaultOrder = 40
        };

        public override bool IsZeroGravity => true;

        public HenTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.Hen;
        }

        protected override void AddMandatoryParamsTile()
        {
            SetParam(TileParamEnum.TileToSpawnFromReaction, (int)TileAsset.Chicken);
            SetParam(TileParamEnum.TileCreateCountForReaction, ChickenSpawnCount);
            base.AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp) + ChickenSpawnCount;
            yield return _assistState;
        }

        public override bool SpawnsTileAtRandomPosition()
        {
            return true;
        }

        public override (Tile, TileAsset, GoalType) CreateTileFromReaction(int id, Cell cell, int index)
        {
            if (HasParam(TileParamEnum.TileToSpawnFromReaction))
            {
                var tileAsset = (TileAsset)GetParam(TileParamEnum.TileToSpawnFromReaction);
                var tileParams = new List<TileParam>
                {
                    new()
                    {
                        Param = TileParamEnum.Skin,
                        Value = Mathf.Clamp(index, 0, MaxChickenSkinIndex)
                    }
                };

                var newTile = TileFactory.CreateTile(id, tileAsset, new TileOrigin(Creator.Item, cell));
                newTile.SetTileParams(tileParams);
                return (newTile, tileAsset, GoalType.None);
            }

            return base.CreateTileFromReaction(id, cell, index);
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            var adjacentHp = GetParam(TileParamEnum.AdjacentHp);

            if (adjacentHp >= 1)
            {
                yield return HenOne;
            }

            if (adjacentHp >= 2)
            {
                yield return HenTwo;
            }
        }

        public override bool IsRelatedToGoal(GoalType goal)
        {
            return goal == GoalType.Chicken || base.IsRelatedToGoal(goal);
        }

        public override IEnumerable<FxType> GetFxTypes()
        {
            foreach (var fx in base.GetFxTypes())
            {
                yield return fx;
            }

            yield return FxType.ChickenAppear;
            yield return FxType.ChickenDestroy;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }

            yield return ResourceInfo;
        }

        public override IEnumerable<TileResourceInfo> GetRelatedResourceInfo()
        {
            yield return ChickenTile.ResourceInfo;
        }

        protected override DamageSource GetAllowedDamage()
        {
            return DamageSource.AdjacentWithPowerUp;
        }

        public override bool GetExpectedGoalTargetCountFor(GoalType goal, ref int count)
        {
            count += ChickenSpawnCount;
            return false;
        }
    }
}