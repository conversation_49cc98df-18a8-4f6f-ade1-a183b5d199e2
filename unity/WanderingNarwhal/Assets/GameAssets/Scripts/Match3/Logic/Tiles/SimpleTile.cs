using System;
using System.Collections.Generic;
using BBB;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using BBB.Match3.Systems;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public abstract class SimpleTile : Tile
    {
        private const string Simple = "Simple";

        private const string TilePrefabName = "TileInstance";
        public override string PrefabName => TilePrefabName;

        public override bool IsRegularTile => State == TileState.None;
        public override bool IsColorBombTargetable => IsNoneOf(TileState.SandMod);
        public override bool IsSuperBoostable => true;
        public override bool IsShufflable => IsRegularTile;
        public override bool AllowsSand => IsRegularTile;
        public override bool IsMatchable => !IsInTransition;
        public override bool IsMatchableSimpleTile => !IsInTransition && Kind.IsColored();
        public override bool IsUndefined => !Kind.IsColored();

        public static TileResourceInfo ResourceInfo => new()
        {
            State = TileLayerState.Normal,
            PrefabName = TilePrefabName,
            DefaultOrder = 10
        };

        public SimpleTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            if (Speciality == TileSpeciality.None && Kind.IsColored())
            {
                yield return Simple;
            }
        }

        public override bool MatchesTntTarget(TntTargetType tntTarget, TileKinds tileKinds)
        {
            return tntTarget == TntTargetType.Simple && tileKinds == Kind;
        }

        public override bool MatchesTukTukTarget(TileKinds tileKinds)
        {
            return tileKinds == Kind;
        }

        public override bool IsAffectingCellState(DamageSource damageSource, bool tileRemoved = false)
        {
            return tileRemoved
                    ? damageSource == DamageSource.MultiBomb
                    : damageSource != DamageSource.MultiBomb;
        }

        public override PreDestroyTweener VisualizeTileDestroy(PlaySimulationActionProxy proxy, TileView view, Action OnTweenerComplete)
        {
            var matchSetting = proxy.Settings.TileDestroyByBooster;
            proxy.TileTickPlayer.BoardObjectFactory.CreateStaticOccupier(matchSetting.BusyTime, view.Coords.ToUnityVector2());
            var tweener = view.ShrinkScaleTo(matchSetting.Duration, matchSetting.TargetScale, matchSetting.ScaleCurve);

            return new PreDestroyTweener
            {
                Tweener = tweener,
                OnTweenerComplete = OnTweenerComplete
            };
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }

            yield return ResourceInfo;
        }

        protected override DamageSource GetAllowedDamage()
        {
            return DamageSource.AllBase;
        }

        protected override bool CanBeDamagedByTileKind(DamageSource damageSource, TileKinds damageTileKind)
        {
            if ((damageSource & DamageSource.Match) != 0)
            {
                return Kind == damageTileKind;
            }

            return base.CanBeDamagedByTileKind(damageSource, damageTileKind);
        }
    }
}