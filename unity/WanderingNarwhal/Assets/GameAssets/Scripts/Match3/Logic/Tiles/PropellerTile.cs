using System.Collections.Generic;
using BBB;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class PropellerTile : BoosterTile
    {
        private const string GeneralizedLayer = "Propeller";

        private const string TilePrefabName = "PropellerBoost";
        public override string PrefabName => TilePrefabName;

        public override bool IsMatchable => !IsInTransition;

        public static TileResourceInfo ResourceInfo => new ()
        {
            State = TileLayerState.Propeller,
            PrefabName = TilePrefabName,
            DefaultOrder = 40
        };

        public PropellerTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.Propeller;
        }
        
        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }

        public override bool ShouldTileSkipDestroyAnimOnGoalCollect(DamageSource damageSource)
        {
            return true;
        }

        public override int GetSpawnPriority()
        {
            return 9;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }

            yield return ResourceInfo;
        }
    }
}