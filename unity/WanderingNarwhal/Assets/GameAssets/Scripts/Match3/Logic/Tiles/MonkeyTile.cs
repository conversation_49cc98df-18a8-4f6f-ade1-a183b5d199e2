using BBB;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using BBB.Match3.Systems;
using System;
using System.Collections.Generic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public class MonkeyTile : Tile
    {
        private const int BananaSpawnCount = 1;
        private (long, int value) _assistState = ((long)GoalType.Banana, DefaultHp);
        private const string GeneralizedLayer = "Monkey";
        private const string TilePrefabName = "Monkey";
        public override string PrefabName => TilePrefabName;

        public override bool IsZeroGravity => true;

        private static TileResourceInfo ResourceInfo => new()
        {
            State = TileLayerState.Monkey,
            PrefabName = TilePrefabName,
            DefaultOrder = 102
        };

        public MonkeyTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.Monkey;
        }

        protected override void AddMandatoryParamsTile()
        {
            SetParam(TileParamEnum.TileToSpawnFromReaction, (int)TileAsset.Banana);
            SetParam(TileParamEnum.TileCreateCountForReaction, BananaSpawnCount);
            base.AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.RestoresCount);
            yield return _assistState;
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }

        protected override bool CanDieRepeatedly()
        {
            return true;
        }

        public override bool SpawnsTileAtRandomPosition()
        {
            return true;
        }

        public override PreDestroyTweener VisualizeTileDestroy(PlaySimulationActionProxy proxy, TileView view, Action OnTweenerComplete)
        {
            var monkeyDestroyBusyTime = proxy.Settings.MonkeyDestroyBusyTime;
            proxy.TileTickPlayer.BoardObjectFactory.CreateStaticOccupier(monkeyDestroyBusyTime, view.Coords.ToUnityVector2());
            return default;
        }

        public override IEnumerable<FxType> GetFxTypes()
        {
            foreach (var fx in base.GetFxTypes())
            {
                yield return fx;
            }

            yield return FxType.BananaAppear;
            yield return FxType.BananaDestroy;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }

            yield return ResourceInfo;
        }

        public override IEnumerable<TileResourceInfo> GetRelatedResourceInfo()
        {
            yield return BananaTile.ResourceInfo;
        }

        protected override DamageSource GetAllowedDamage()
        {
            return DamageSource.AdjacentWithPowerUp;
        }

        public override bool IsRelatedToGoal(GoalType goal)
        {
            return goal == GoalType.Banana || base.IsRelatedToGoal(goal);
        }

        public override bool GetExpectedGoalTargetCountFor(GoalType goal, ref int count)
        {
            count += GetParam(TileParamEnum.RestoresCount);
            return false;
        }
    }
}