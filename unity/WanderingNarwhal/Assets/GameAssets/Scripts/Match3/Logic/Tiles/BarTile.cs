using BBB;
using System.Collections.Generic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public abstract class BarTile : Tile
    {
        protected TileParamEnum BarOrientation = TileParamEnum.IceBarOrientation;

        protected BarTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            var adjacentHp = GetParam(TileParamEnum.AdjacentHp);
            if (adjacentHp <= 0)
            {
                return false;
            }
            var reducedAdjacentHp = adjacentHp - 1;

            if (TryApplyBarTileDamage(simulationContext.Grid, hitContext.MainCell, hitContext.HitWaitParams,
                    adjacentHp, reducedAdjacentHp, simulationContext.Handler))

            {
                return true;
            }

            return base.TryApplyAdjacentDamage(simulationContext, hitContext);
        }

        private bool TryApplyBarTileDamage(Grid grid, Cell cell, HitWaitParams hitWaitParams,
            int adjacentHp, int reducedAdjacentHp, IRootSimulationHandler handler)
        {
            if (reducedAdjacentHp <= 0) return false;

            var tileSizeX = GetParam(TileParamEnum.SizeX);
            var tileSizeY = GetParam(TileParamEnum.SizeY);
            var orientation = GetParam(BarOrientation);

            var tileSizeToReduce = TileParamEnum.SizeX;

            var cellMelting = cell.Coords;
            if (tileSizeX > 1)
            {
                tileSizeToReduce = TileParamEnum.SizeX;
                cellMelting.X = orientation switch
                {
                    0 => cell.Coords.X + tileSizeX - 1,
                    180 => cell.Coords.X - tileSizeX + 1,
                    _ => cellMelting.X
                };
            }

            if (tileSizeY > 1)
            {
                tileSizeToReduce = TileParamEnum.SizeY;

                cellMelting.Y = orientation switch
                {
                    -90 => cell.Coords.Y - tileSizeY + 1,
                    90 => cell.Coords.Y + tileSizeY - 1,
                    _ => cellMelting.Y
                };
            }

            SetParam(tileSizeToReduce, adjacentHp);
            SetParam(TileParamEnum.AdjacentHp, reducedAdjacentHp);
            grid.RefrehsAllCellsMultisizeCaches();

            var tileParamList = new List<(TileParamEnum, int)>
            {
                new(tileSizeToReduce, adjacentHp),
                new(TileParamEnum.AdjacentHp, reducedAdjacentHp)
            };

            handler.AddAction(new ActionAddBarTileOccupier(cellMelting, Speciality));
            handler.AddAction(new ActionChangeTileParam(Id, cell.Coords, tileParamList, hitWaitParams));

            return true;
        }
    }
}