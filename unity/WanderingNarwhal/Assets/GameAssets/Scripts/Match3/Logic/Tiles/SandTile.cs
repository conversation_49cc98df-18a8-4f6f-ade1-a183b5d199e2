using BBB;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using System.Collections.Generic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class SandTile : Tile
    {
        private const string GeneralizedLayer = "Sand";
        public const string TilePrefabName = "Sand";
        public override string PrefabName => TilePrefabName;

        public override bool IsZeroGravity => true;

        private static TileResourceInfo ResourceInfo => new()
        {
            State = TileLayerState.Sand,
            PrefabName = TilePrefabName,
            DefaultOrder = 122
        };

        public SandTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.Sand;
            State |= TileState.SandMod;
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }

        public override bool DisallowCellBackgroundSpawn()
        {
            return true;
        }

        public override bool IsRelatedToGoal(GoalType goal)
        {
            return goal == GoalType.Sand || base.IsRelatedToGoal(goal);
        }

        public override IEnumerable<FxType> GetFxTypes()
        {
            foreach (var fx in base.GetFxTypes())
            {
                yield return fx;
            }

            yield return FxType.SandRemove;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }

            yield return ResourceInfo;
        }

        protected override DamageSource GetAllowedDamage()
        {
            return DamageSource.AdjacentWithPowerUp;
        }

        protected override void RemoveSand()
        {
            base.RemoveSand();

            _hp--;
        }
    }
}