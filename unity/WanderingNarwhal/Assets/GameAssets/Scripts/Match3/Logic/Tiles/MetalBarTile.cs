using BBB;
using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Renderer;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class MetalBarTile : BarTile
    {
        private (long, int value) _assistState = ((long) GoalType.MetalBar, DefaultHp);
        private const string GeneralizedLayer = "MetalBar";
        private const string TilePrefabName = "MetalBarTile";
        public override string PrefabName => TilePrefabName;

        private static TileResourceInfo ResourceInfo => new()
        {
            State = TileLayerState.MetalBar,
            PrefabName = TilePrefabName,
            DefaultOrder = 109
        };
        
        public override bool IsZeroGravity => true;
        
        public MetalBarTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.MetalBar;
            BarOrientation = TileParamEnum.MetalBarOrientation;
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }
        
        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }

        public override bool IsRelatedToGoal(GoalType goal)
        {
            return goal == GoalType.MetalBar;
        }
        
        public static void HandleMetalBar(Grid grid, IRootSimulationHandler events)
        {
            foreach (var c in grid.Cells)
            {
                var mainCell = c.GetMainCellReference(out _);
                if (!mainCell.Tile.IsNull() && mainCell.Tile.Speciality == TileSpeciality.MetalBar) continue;

                if (!c.IsAnyOf(CellState.MetalBar) && !c.MetalBarStatus) continue;
                c.MetalBarStatus = false;
                c.Remove(CellState.MetalBar);
                events.AddAction(new ActionFreeUpMetalBarCells(c.Coords));
            }
        }

        public override IEnumerable<FxType> GetFxTypes()
        {
            foreach (var fx in base.GetFxTypes())
            {
                yield return fx;
            }

            yield return FxType.MetalBarLayerRemove;
            yield return FxType.MetalBarTileDestroy;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }

            yield return ResourceInfo;
        }

        protected override DamageSource GetAllowedDamage()
        {
            return DamageSource.PowerUp;
        }
    }
}