using BBB;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;
using BBB.Match3.Renderer;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class BowlingTile : Tile
    {
        private const int BowlingTileClosedHp = 1;
        private static readonly (long, int) ClosedAssistState = ((long)GoalType.BowlingPin, BowlingTileClosedHp);
        private (long, int value) _assistState = ((long)GoalType.BowlingPin, DefaultHp);
        private const string BowlingCurtain = "BowlingCurtain";
        private const string Bowling = "Bowling";
        private const string TilePrefabName = "BowlingTile";
        public override string PrefabName => TilePrefabName;

        private static TileResourceInfo ResourceInfo => new()
        {
            State = TileLayerState.Bowling,
            PrefabName = TilePrefabName,
            DefaultOrder = 109
        };

        public override bool IsZeroGravity => true;

        public BowlingTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.Bowling;
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            if (GetParam(TileParamEnum.BowlingOpened) == 0)
            {
                yield return ClosedAssistState;
            }

            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }

        public override bool HasDieReaction()
        {
            return false;
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            var bowlingPinClosed = GetParam(TileParamEnum.BowlingOpened) == 0;
            if (bowlingPinClosed)
            {
                SetParam(TileParamEnum.BowlingOpened, 1);
                simulationContext.Handler.AddAction(new ActionChangeTileParam(Id, hitContext.Cell.Coords,
                    new List<(TileParamEnum, int)> { new(TileParamEnum.BowlingOpened, 1) }, hitContext.HitWaitParams));
                return true;
            }

            return base.TryApplyAdjacentDamage(simulationContext, hitContext);
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            if (GetParam(TileParamEnum.BowlingOpened) == 0)
            {
                yield return BowlingCurtain;
            }

            yield return Bowling;
        }

        public override bool IsRelatedToGoal(GoalType goal)
        {
            return goal == GoalType.BowlingPin || base.IsRelatedToGoal(goal);
        }

        public override IEnumerable<FxType> GetFxTypes()
        {
            foreach (var fx in base.GetFxTypes())
            {
                yield return fx;
            }

            yield return FxType.BowlingPin1;
            yield return FxType.BowlingPin2;
            yield return FxType.BowlingPin3;
            yield return FxType.BowlingPin4;
            yield return FxType.BowlingPin5;
            yield return FxType.BowlingPin6;
            yield return FxType.BowlingTileDestroy;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }

            yield return ResourceInfo;
        }

        protected override DamageSource GetAllowedDamage()
        {
            return DamageSource.AdjacentWithPowerUp;
        }

        public override bool GetExpectedGoalTargetCountFor(GoalType goal, ref int count)
        {
            count += GetParam(TileParamEnum.AdjacentHp);
            return false;
        }
    }
}