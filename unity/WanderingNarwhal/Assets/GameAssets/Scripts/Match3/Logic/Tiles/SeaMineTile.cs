using System.Collections.Generic;
using BBB;
using BBB.Match3;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes.AdditionalHitInfos;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;
using GameAssets.Scripts.Match3.Settings;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class SeaMineTile : Tile
    {
        private (long, int value) _assistState = ((long)GoalType.SeaMine, DefaultHp);
        private const string GeneralizedLayer = "SeaMine";
        private static int _seaMineId;
        private static readonly HashSet<Coords> DamageCoords = new();
        private static readonly Coords MinOffset = new(-1, -1);
        private static readonly Coords MaxOffset = new(1, 1);

        public SeaMineTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileParams)
        {
            Speciality = TileSpeciality.SeaMine;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.AdjacentGeneral;
            State |= TileState.SeaMine;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }

        public override Queue ExecuteTileDamageEffect(IRootSimulationHandler events, Grid grid, Cell sourceCell,
            HitWaitParams hitWaitParams)
        {
            var q = new Queue(context: GeneralizedLayer);

            var sourceCoords = sourceCell.Coords;

            var min = new Coords(sourceCoords.X + MinOffset.X, sourceCoords.Y + MinOffset.Y);
            var max = new Coords(sourceCoords.X + MaxOffset.X, sourceCoords.Y + MaxOffset.Y);

            var seaMineId = _seaMineId++;
            DamageCoords.Clear();

            for (var x = min.X; x <= max.X; x++)
            {
                for (var y = min.Y; y <= max.Y; y++)
                {
                    var coords = new Coords(x, y);

                    if (!grid.TryGetCell(coords, out var newCell))
                    {
                        continue;
                    }

                    DamageCoords.Add(coords);

                    var hit = new Hit<Cell>(
                        immuneTiles: null,
                        target: newCell,
                        damageSource: DamageSource.SeaMine,
                        boostInfo: new SeaMineInfo(seaMineId),
                        busyWait: M3Constants.BusyTimeSeaMine,
                        coords: newCell.Coords,
                        hitSourceUid: seaMineId);
                    q.AppendHit(0, hit);
                }
            }

            events.AddAction(new ActionSeaMine(seaMineId, sourceCoords, DamageCoords, hitWaitParams));

            return q;
        }
    }
}