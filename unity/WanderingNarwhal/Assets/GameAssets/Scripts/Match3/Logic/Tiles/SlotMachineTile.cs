using BBB;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;
using BBB.Match3.Systems;
using BBB.Match3.Renderer;
using System;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class SlotMachineTile : Tile
    {
        private const int SlotMachineHp = 4;
        private (long, int value) _assistState = ((long) GoalType.SlotMachine, DefaultHp);
        private const string SlotMachine = "SlotMachine";
        private const string SlotMachineCover = "SlotMachineCover";
        private const string TilePrefabName = "SlotMachineTile";
        public override string PrefabName => TilePrefabName;

        private static TileResourceInfo ResourceInfo => new ()
        {
            State = TileLayerState.SlotMachine,
            PrefabName = TilePrefabName,
            DefaultOrder = 109
        };

        public override bool IsZeroGravity => true;

        public SlotMachineTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.SlotMachine;
            ShouldDelaySimulationOnReaction = true;
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }
        
        public override bool HasReactionAndGoalCollection()
        {
            return true;
        }
        
        public override bool SpawnsTileAtRandomPosition()
        {
            return true;
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            if (GetParam(TileParamEnum.TileCreateCountForReaction) == 0)
            {
                var m3Settings = simulationContext.InputParams.Settings;
                var configuration = m3Settings.PowerUpSpawnOutcomeConfiguration;
                var rewardConfiguration = configuration.PowerUpSpawnOutcomeConfigurations[Asset];

                var rewardConfigurations = rewardConfiguration.RewardConfigurations;
                var keysToRemove = new List<TileAsset>();

                foreach (var kvp in rewardConfigurations)
                {
                    if (simulationContext.Grid.GetSlotMachineRewardCount(kvp.Key) >=
                        m3Settings.SlotMachineMaxSameSpawnsPerLevel)
                    {
                        keysToRemove.Add(kvp.Key);
                    }
                }

                foreach (var key in keysToRemove)
                {
                    rewardConfigurations.Remove(key);
                }

                TileSpawnHandler.HandlePowerUpSpawnOutcome(this, simulationContext, hitContext, rewardConfiguration);

                var tileAssets = TileSpawnHandler.UnpackTileAssetsDynamic(this);

                foreach (var tileAsset in tileAssets)
                {
                    simulationContext.Handler.AddAction(new ActionUpdateSlotRewards(tileAsset));
                }
            }

            return base.TryApplyAdjacentDamage(simulationContext, hitContext);
        }
        
        public override (Tile, TileAsset, GoalType) CreateTileFromReaction(int id, Cell cell, int index)
        {
            var tileAssets = TileSpawnHandler.UnpackTileAssetsDynamic(this);
            var tileAsset = tileAssets[index];
            var newTile = TileFactory.CreateTile(id, tileAsset, new TileOrigin(Creator.Item, cell));

            return (newTile, tileAsset, GoalType.None);
        }

        protected override DamageSource GetAllowedDamage()
        {
            var allowedDamage = DamageSource.AdjacentWithPowerUp;
            if (GetParam(TileParamEnum.AdjacentHp) == SlotMachineHp)
            {
                allowedDamage &= ~(DamageSource.AdjacentGeneral | DamageSource.RemoveColorTiles);
            }

            return allowedDamage;
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            var adjacentHp = GetParam(TileParamEnum.AdjacentHp);
            yield return adjacentHp >= SlotMachineHp ? SlotMachineCover : SlotMachine;
        }

        public override IEnumerable<FxType> GetFxTypes()
        {
            foreach (var fx in base.GetFxTypes())
            {
                yield return fx;
            }

            yield return FxType.SlotMachineTileHideLayer1;
            yield return FxType.SlotMachineTileHideLayer2;
            yield return FxType.SlotMachineTileHideLayer3;
            yield return FxType.SlotMachineTileHideLayer4;
            yield return FxType.SpawnLineBreakerAppear;
            yield return FxType.SpawnColumnBreakerAppear;
            yield return FxType.SpawnColorBombAppear;
            yield return FxType.SpawnBombAppear;
            yield return FxType.SpawnPropellerAppear;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }
            
            yield return ResourceInfo;
        }

        public override bool IsRelatedToGoal(GoalType goal)
        {
            return goal == GoalType.SlotMachine || base.IsRelatedToGoal(goal);
        }

        public override PreDestroyTweener VisualizeTileDestroy(PlaySimulationActionProxy proxy, TileView view, Action OnTweenerComplete)
        {
            var sizeX = GetParam(TileParamEnum.SizeX);
            var sizeY = GetParam(TileParamEnum.SizeY);
            proxy.TileTickPlayer.BoardObjectFactory.CreateOccupiersOver(proxy.Settings.SlotMachineDestroyBusyTime, view.Coords,
                view.Coords + new Coords(sizeX - 1, sizeY - 1));
            return default;
        }
    }
}