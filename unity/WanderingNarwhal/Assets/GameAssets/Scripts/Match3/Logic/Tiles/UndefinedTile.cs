using System.Collections.Generic;
using BBB;
using BBB.Match3;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class UndefinedTile : SimpleTile
    {
        public UndefinedTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileParams)
        {
            Kind = TileKinds.Undefined;
        }

        public override bool IsUndefined()
        {
            return true;
        }
    }
}
