using BBB;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class StickerTile : Tile
    {
        private (long, int value) _assistState = ((long)GoalType.Stickers, DefaultHp);
        private const string CrateOne = "CrateOne";
        private const string CrateTwo = "CrateTwo";
        private const string CrateThree = "CrateThree";

        private const string TilePrefabName = "StickerBlocker";
        public override string PrefabName => TilePrefabName;

        public override bool IsZeroGravity => true;

        private static TileResourceInfo ResourceInfo => new()
        {
            State = TileLayerState.Sticker,
            PrefabName = TilePrefabName,
            DefaultOrder = 30
        };

        public StickerTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.Sticker;
            BoostersApplicability = BoosterItem.Shovel | BoosterItem.Balloon | BoosterItem.Wind | BoosterItem.Rain |
                                    BoosterItem.Vertical | BoosterItem.Horizontal;
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            var adjacentHp = GetParam(TileParamEnum.AdjacentHp);

            if (adjacentHp >= 1)
            {
                yield return CrateOne;
            }

            if (adjacentHp >= 2)
            {
                yield return CrateTwo;
            }

            if (adjacentHp >= 3)
            {
                yield return CrateThree;
            }
        }

        public override bool IsRelatedToGoal(GoalType goal)
        {
            return goal == GoalType.Stickers;
        }

        public override IEnumerable<FxType> GetFxTypes()
        {
            foreach (var fx in base.GetFxTypes())
            {
                yield return fx;
            }
            
            yield return FxType.StickerRemove;
            yield return FxType.StickerSecondLayerRemove;
            yield return FxType.StickerThirdLayerRemove;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }

            yield return ResourceInfo;
        }

        protected override DamageSource GetAllowedDamage()
        {
            return DamageSource.AdjacentWithPowerUp;
        }
    }
}