using System.Collections.Generic;
using BBB;
using BBB.Match3.Renderer;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class ColumnBreakerTile : LineBreakerTile
    {
        private const string GeneralizedLayer = "VerticalLb";

        public static TileResourceInfo ResourceInfo => new ()
        {
            State = TileLayerState.VerticalLb,
            PrefabName = TilePrefabName,
            DefaultOrder = 20
        };

        public ColumnBreakerTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.ColumnBreaker;
        }
        
        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }

        public override int GetSpawnPriority()
        {
            return 6;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }

            yield return ResourceInfo;
        }
    }
}