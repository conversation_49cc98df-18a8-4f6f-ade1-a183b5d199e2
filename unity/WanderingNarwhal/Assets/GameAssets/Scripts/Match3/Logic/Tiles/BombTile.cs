using System.Collections.Generic;
using BBB;
using BBB.Match3.Renderer;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class BombTile : BoosterTile
    {
        private const string GeneralizedLayer = "Bomb";

        private const string TilePrefabName = "BombBoost";
        public override string PrefabName => TilePrefabName;

        public override bool IsMatchable => !IsInTransition;

        public static TileResourceInfo ResourceInfo => new()
        {
            State = TileLayerState.Bomb,
            PrefabName = TilePrefabName,
            DefaultOrder = 40
        };

        public BombTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.Bomb;
        }
        
        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }

        public override int GetSpawnPriority()
        {
            return 8;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }

            yield return ResourceInfo;
        }
    }
}