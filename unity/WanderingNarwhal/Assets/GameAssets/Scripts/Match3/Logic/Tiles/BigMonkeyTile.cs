using BBB;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using BBB.Match3.Systems;
using System;
using System.Collections.Generic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class BigMonkeyTile : MonkeyTile
    {
        private const string GeneralizedLayer = "BigMonkey";
        private const string TilePrefabName = "BigMonkey";
        public override string PrefabName => TilePrefabName;

        private static TileResourceInfo ResourceInfo => new()
        {
            State = TileLayerState.BigMonkey,
            PrefabName = TilePrefabName,
            DefaultOrder = 102
        };

        public override bool IsZeroGravity => true;

        public BigMonkeyTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.BigMonkey;
        }

        protected override bool CanDieRepeatedly()
        {
            return true;
        }

        public override bool SpawnsTileAtRandomPosition()
        {
            return true;
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }

            yield return ResourceInfo;
        }

        public override IEnumerable<TileResourceInfo> GetRelatedResourceInfo()
        {
            yield return BananaTile.ResourceInfo;
        }

        public override PreDestroyTweener VisualizeTileDestroy(PlaySimulationActionProxy proxy, TileView view, Action OnTweenerComplete)
        {
            var sizeX = GetParam(TileParamEnum.SizeX);
            var sizeY = GetParam(TileParamEnum.SizeY);
            proxy.TileTickPlayer.BoardObjectFactory.CreateOccupiersOver(proxy.Settings.MonkeyDestroyBusyTime, view.Coords,
                view.Coords + new Coords(sizeX - 1, sizeY - 1));
            return default;
        }

        public override IEnumerable<FxType> GetFxTypes()
        {
            foreach (var fx in base.GetFxTypes())
            {
                yield return fx;
            }

            yield return FxType.BananaAppear;
            yield return FxType.BananaDestroy;
        }

        protected override DamageSource GetAllowedDamage()
        {
            return DamageSource.AdjacentWithPowerUp;
        }

        public override bool GetExpectedGoalTargetCountFor(GoalType goal, ref int count)
        {
            count += GetParam(TileParamEnum.RestoresCount);
            return true;
        }
    }
}