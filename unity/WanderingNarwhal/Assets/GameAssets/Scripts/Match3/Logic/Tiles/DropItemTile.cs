using BBB;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Renderer;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class DropItemTile : Tile
    {
        private const string GeneralizedLayer = "DropItem";

        private const string TilePrefabName = "DropItem";
        public override string PrefabName => TilePrefabName;

        private static TileResourceInfo ResourceInfo => new()
        {
            State = TileLayerState.DropItem,
            PrefabName = TilePrefabName,
            DefaultOrder = 50
        };

        public DropItemTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.DropItem;
            BoostersApplicability = BoosterItem.None;
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }

        public override bool ShouldTileSkipDestroyAnimOnGoalCollect(DamageSource damageSource)
        {
            return true;
        }

        public override bool IsRelatedToGoal(GoalType goal)
        {
            return goal == GoalType.DropItems;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }

            yield return ResourceInfo;
        }
    }
}