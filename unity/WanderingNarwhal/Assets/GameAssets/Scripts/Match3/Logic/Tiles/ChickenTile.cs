using BBB;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using System.Collections.Generic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class ChickenTile : Tile
    {
        private const int ChickenHp = 1;
        private static readonly (long, int) AssistState = ((long) GoalType.Chicken, ChickenHp);
        private const string GeneralizedLayer = "Chicken";
        private const string TilePrefabName = "ChickenTile";
        public override string PrefabName => TilePrefabName;

        public static TileResourceInfo ResourceInfo => new ()
        {
            State = TileLayerState.Chicken,
            PrefabName = TilePrefabName,
            DefaultOrder = 20
        };

        public ChickenTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.Chicken;
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            yield return AssistState;
        }
        
        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }

        public override bool IsRelatedToGoal(GoalType goal)
        {
            return goal == GoalType.Chicken || base.IsRelatedToGoal(goal);
        }

        public override IEnumerable<FxType> GetFxTypes()
        {
            foreach (var fx in base.GetFxTypes())
            {
                yield return fx;
            }
            
            yield return FxType.ChickenAppear;
            yield return FxType.ChickenDestroy;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }

            yield return ResourceInfo;
        }

        protected override DamageSource GetAllowedDamage()
        {
            return DamageSource.AdjacentWithPowerUp;
        }
    }
}