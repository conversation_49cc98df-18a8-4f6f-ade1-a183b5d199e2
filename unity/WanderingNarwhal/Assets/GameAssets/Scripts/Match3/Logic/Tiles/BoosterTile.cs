using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.Match3;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public abstract class BoosterTile : Tile
    {
        protected BoosterTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileParams)
        {
            Kind = TileKinds.None;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.TapOrSwap;
            BoostersApplicability = BoosterItem.All & ~BoosterItem.CreateBomb;
            AddMandatoryParamsTile();
        }

        public override void CheckInvalidStates(ILevel level, Cell cell)
        {
            base.CheckInvalidStates(level, cell);

            if (IsAnyOf(TileState.ArmorMod))
            {
                BDebug.LogError(LogCat.Match3, $"Chained booster found on level '{level.Config.Uid}'");
            }
        }

        public override bool IsAffectingCellState(DamageSource damageSource, bool tileRemoved = false)
        {
            return tileRemoved
                ? damageSource == DamageSource.RemoveColorTiles
                : damageSource != DamageSource.RemoveColorTiles;
        }
    }
}