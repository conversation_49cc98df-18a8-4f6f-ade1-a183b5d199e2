using BBB;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;
using BBB.Match3.Renderer;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class ShelfTile : Tile
    {
        public const int ShelfHp = 2;

        private readonly HashSet<Coords> _connectedShelfCoords = new();
        private static readonly Queue<Cell> TempCellQueue = new();
        private static readonly Coords[] NeighborCoords = new Coords[8];

        private (long type, int value) _assistState = ((long)GoalType.Shelf, DefaultHp);
        private const string Shelf = "Shelf";
        private const string ShelfEmpty = "ShelfEmpty";
        private const string TilePrefabName = "ShelfTile";
        public override string PrefabName => TilePrefabName;

        private static TileResourceInfo ResourceInfo => new()
        {
            State = TileLayerState.Shelf,
            PrefabName = TilePrefabName,
            DefaultOrder = 109
        };

        public override bool IsZeroGravity => true;

        public ShelfTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.Shelf;
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            if (GetParam(TileParamEnum.AdjacentHp) < ShelfHp)
            {
                return false;
            }

            var hasItemOnTheShelf = HasAnyItemOnTheShelf(simulationContext.Grid, hitContext.MainCell);

            SetParam(TileParamEnum.AdjacentHp, 1);

            simulationContext.Handler.AddAction(new ActionChangeTileParam(hitContext.MainCell.Tile.Id,
                hitContext.MainCell.Coords, new List<(TileParamEnum, int)>
                {
                    new(TileParamEnum.AdjacentHp, 1)
                }, hitContext.HitWaitParams));

            simulationContext.GoalSystem.TryReduceGoalIfNeeded(GoalType.Shelf);
            simulationContext.Handler.AddAction(new ActionDestroyShelfItem(hitContext.MainCell.Coords,
                _connectedShelfCoords, hitContext.HitWaitParams));

            if (!hasItemOnTheShelf)
            {
                simulationContext.Handler.AddAction(new ActionSyncCoords(_connectedShelfCoords));
                foreach (var shelfCoords in _connectedShelfCoords)
                {
                    if (simulationContext.Grid.TryGetCell(shelfCoords, out var cell))
                    {
                        simulationContext.PopSystem.KillTile(simulationContext.Grid, simulationContext.ReactionHandler,
                            simulationContext.InputParams, simulationContext.GoalSystem, simulationContext.Queue,
                            simulationContext.CellsToDamageQueue, cell, hitContext.HitWaitParams, false);
                    }
                }
            }

            return true;
        }

        private bool HasAnyItemOnTheShelf(Grid grid, Cell startCell)
        {
            _connectedShelfCoords.Clear();
            TempCellQueue.Clear();

            var shelfGroupId = GetParam(TileParamEnum.ShelfGroupIdentifier);

            _connectedShelfCoords.Add(startCell.Coords);
            TempCellQueue.Enqueue(startCell);

            var hasItem = false;

            while (TempCellQueue.Count > 0)
            {
                var currentCell = TempCellQueue.Dequeue();
                var coords = currentCell.Coords;

                CardinalDirectionsHelper.PopulateNeighborCoords(NeighborCoords, coords.X, coords.Y);

                for (var i = 0; i < 8; i++)
                {
                    var neighborCoords = NeighborCoords[i];

                    if (!grid.TryGetCell(neighborCoords, out var neighborCell))
                        continue;

                    if (_connectedShelfCoords.Contains(neighborCell.Coords) || !neighborCell.HasTile())
                        continue;

                    var neighborTile = neighborCell.Tile;
                    if (neighborTile.GetParam(TileParamEnum.ShelfGroupIdentifier) != shelfGroupId)
                        continue;

                    if (neighborTile.GetParam(TileParamEnum.AdjacentHp) == ShelfHp)
                    {
                        hasItem = true;
                    }

                    _connectedShelfCoords.Add(neighborCell.Coords);
                    TempCellQueue.Enqueue(neighborCell);
                }
            }

            return hasItem;
        }

        public override bool CheckApplicability(BoosterItem boosterItem, Cell cell, Grid grid)
        {
            return (State & (TileState.ChainMod | TileState.IceCubeMod | TileState.SandMod)) != 0
                   || GetParam(TileParamEnum.AdjacentHp) == ShelfHp;
        }

        protected override bool CanDieRepeatedly()
        {
            return true;
        }

        public override bool HasDieReaction()
        {
            return false;
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            var adjacentHp = GetParam(TileParamEnum.AdjacentHp);

            if (adjacentHp == ShelfHp)
            {
                yield return Shelf;
            }

            yield return ShelfEmpty;
        }

        public override bool IsRelatedToGoal(GoalType goal)
        {
            return goal == GoalType.Shelf || base.IsRelatedToGoal(goal);
        }

        public override IEnumerable<FxType> GetFxTypes()
        {
            foreach (var fx in base.GetFxTypes())
            {
                yield return fx;
            }

            yield return FxType.ShelfItemDestroy;
            yield return FxType.ShelfTileDestroy;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }

            yield return ResourceInfo;
        }

        protected override DamageSource GetAllowedDamage()
        {
            return DamageSource.AdjacentWithPowerUp;
        }
    }
}