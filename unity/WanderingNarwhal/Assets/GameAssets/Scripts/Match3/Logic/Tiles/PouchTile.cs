using BBB;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class PouchTile : Tile
    {
        private (long, int value) _assistState = ((long)GoalType.Pouch, DefaultHp);
        private const string GeneralizedLayer = "Pouch";
        private const string TilePrefabName = "PouchTile";
        public override string PrefabName => TilePrefabName;

        private static TileResourceInfo ResourceInfo => new ()
        {
            State = TileLayerState.Pouch,
            PrefabName = TilePrefabName,
            DefaultOrder = 30
        };

        public PouchTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.Pouch;
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            var configuration = simulationContext.InputParams.Settings.PowerUpSpawnOutcomeConfiguration;
            var rewardConfiguration = configuration.PowerUpSpawnOutcomeConfigurations[Asset];
            TileSpawnHandler.HandlePowerUpSpawnOutcome(this, simulationContext, hitContext, rewardConfiguration);
            return base.TryApplyAdjacentDamage(simulationContext, hitContext);
        }

        public override (Tile, TileAsset, GoalType) CreateTileFromReaction(int id, Cell cell, int index)
        {
            var tileAssets = TileSpawnHandler.UnpackTileAssetsDynamic(this);
            var tileAsset = tileAssets[index];
            var newTile = TileFactory.CreateTile(id, tileAsset, new TileOrigin(Creator.Item, cell));

            return (newTile, tileAsset, GoalType.None);
        }
        
        public override bool HasSpawnsTileStartDelay()
        {
            return false;
        }
        
        public override bool HasReactionAndGoalCollection()
        {
            return true;
        }
        
        public override bool SpawnsTileAtRandomPosition()
        {
            return true;
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            yield return GeneralizedLayer;
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }
            
            yield return ResourceInfo;
        }

        public override bool IsRelatedToGoal(GoalType goal)
        {
            return goal == GoalType.Pouch;
        }

        public override IEnumerable<FxType> GetFxTypes()
        {
            foreach (var fx in base.GetFxTypes())
            {
                yield return fx;
            }
            
            yield return FxType.PouchThirdLayerRemove;
            yield return FxType.PouchSecondLayerRemove;
            yield return FxType.PouchDestroy;
            yield return FxType.SpawnLineBreakerAppear;
            yield return FxType.SpawnColumnBreakerAppear;
            yield return FxType.SpawnColorBombAppear;
            yield return FxType.SpawnBombAppear;
            yield return FxType.SpawnPropellerAppear;
        }

        protected override DamageSource GetAllowedDamage()
        {
            return DamageSource.AdjacentWithPowerUp;
        }
    }
}