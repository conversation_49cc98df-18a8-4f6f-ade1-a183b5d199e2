using BBB;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class StoneTile : Tile
    {
        public const int MinStoneHp = 1;
        public const int MaxStoneHp = 3;
        
        private (long, int value) _assistState = ((long) GoalType.Stone, DefaultHp);
        private const string StoneOne = "StoneOne";
        private const string StoneTwo = "StoneTwo";
        private const string StoneThree = "StoneThree";
        private const string TilePrefabName = "StoneTile";
        public override string PrefabName => TilePrefabName;

        private static TileResourceInfo ResourceInfo => new ()
        {
            State = TileLayerState.Stone,
            PrefabName = TilePrefabName,
            DefaultOrder = 30
        };
        
        public override bool IsZeroGravity => true;
        
        public StoneTile(int id, TileAsset tileAsset, TileOrigin tileOrigin)
            : base(id, tileAsset, tileOrigin)
        {
            Speciality = TileSpeciality.Stone;
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            var adjacentHp = GetParam(TileParamEnum.AdjacentHp);

            if (adjacentHp >= 1)
            {
                yield return StoneOne;
            }

            if (adjacentHp >= 2)
            {
                yield return StoneTwo;
            }

            if (adjacentHp >= 3)
            {
                yield return StoneThree;
            }
        }

        public override IEnumerable<TileResourceInfo> GetResourceInfo()
        {
            foreach (var info in base.GetResourceInfo())
            {
                yield return info;
            }
            
            yield return ResourceInfo;
        }

        public override bool IsRelatedToGoal(GoalType goal)
        {
            return goal == GoalType.Stone;
        }

        public override IEnumerable<FxType> GetFxTypes()
        {
            foreach (var fx in base.GetFxTypes())
            {
                yield return fx;
            }
            
            yield return FxType.StoneThirdLayerRemove;
            yield return FxType.StoneSecondLayerRemove;
            yield return FxType.StoneDestroy;
        }

        protected override DamageSource GetAllowedDamage()
        {
            return DamageSource.PowerUp;
        }
    }
}