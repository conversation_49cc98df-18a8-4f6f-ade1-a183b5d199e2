using BBB.Core;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.Match3.Logic
{
    public class SpawnColorBombAppearEffect : ActivePoolItem
    {
        [SerializeField] private Image _discoBallImage;
        [SerializeField] private Sprite _regularDiscoBallSprite;
        [SerializeField] private Sprite _superDiscoBallSprite;
        
        public void SetDiscoBallType(bool isSuperDiscoBall)
        {
            _discoBallImage.sprite = isSuperDiscoBall ? _superDiscoBallSprite : _regularDiscoBallSprite;
        }
    }
}