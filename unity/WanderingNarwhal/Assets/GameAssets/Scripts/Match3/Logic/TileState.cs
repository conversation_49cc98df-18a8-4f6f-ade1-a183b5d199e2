using System;

namespace BBB
{
    public static class TileStateExtensions
    {
        public static bool IsNull(this Tile tile)
        {
            return ReferenceEquals(tile, null);
        }
    }

    [Flags]
    public enum TileState : ulong
    {
        None                    = 0,

        //MODS. DO NOT modify these without proper level files migration
        ChainMod                = 1L << 0, //1
        IceCubeMod              = 1L << 1, //2
        AnimalMod               = 1L << 3, //8
        SandMod                 = 1L << 4, //16
        VaseMod                 = 1L << 7, //128
        
        ArmorMods                = ChainMod | IceCubeMod | SandMod | VaseMod,
        
        UniversalMods           = IceCubeMod | ChainMod | SandMod | VaseMod | AnimalMod,
    }
}