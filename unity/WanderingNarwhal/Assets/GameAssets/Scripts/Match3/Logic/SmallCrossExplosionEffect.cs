using BBB.Core;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    public class SmallCrossExplosionEffect : ActivePoolItem
    {
        [SerializeField] private ActivePoolItem _topObj;
        [SerializeField] private ActivePoolItem _leftObj;
        [SerializeField] private ActivePoolItem _rightObj;
        [SerializeField] private ActivePoolItem _bottomObj;

        public void Setup(CardinalDirections dir)
        {
            SetActive(_topObj, (dir & CardinalDirections.N) != 0);
            SetActive(_leftObj, (dir & CardinalDirections.W) != 0);
            SetActive(_rightObj, (dir & CardinalDirections.E) != 0);
            SetActive(_bottomObj, (dir & CardinalDirections.S) != 0);
        }

        public override void OnInstantiate()
        {
            base.OnInstantiate();

            _topObj.OnInstantiate();
            _leftObj.OnInstantiate();
            _rightObj.OnInstantiate();
            _bottomObj.OnInstantiate();
        }

        public override void OnRelease()
        {
            base.OnRelease();

            _topObj.OnRelease();
            _leftObj.OnRelease();
            _rightObj.OnRelease();
            _bottomObj.OnRelease();
        }

        private static void SetActive(ActivePoolItem item, bool state)
        {
            if (state)
                item.OnSpawn();
            else
                item.OnRelease();
        }
    }
}