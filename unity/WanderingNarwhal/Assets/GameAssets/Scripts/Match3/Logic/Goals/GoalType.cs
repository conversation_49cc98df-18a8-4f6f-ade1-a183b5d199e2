using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;

namespace GameAssets.Scripts.Match3.Logic
{
    public static class GoalTypeExtensions
    {
        private static GoalType[] _cachedGridBaseTypes;
        private static GoalType[] _cachedTileKindTypes;

        public static IEnumerable<GoalType> GetAllGoals()
        {
            foreach (GoalType goalType in Enum.GetValues(typeof(GoalType)))
            {
                yield return goalType;
            }
        }

        public static bool IsAssistRedefined(this GoalType goal)
        {
            return goal is GoalType.Animal or GoalType.Banana or GoalType.Backgrounds or 
                           GoalType.Bird or GoalType.BowlingPin or GoalType.Bush or 
                           GoalType.Chains or GoalType.Chicken or GoalType.ColorCrate or
                           GoalType.DynamiteStick or GoalType.Egg or GoalType.FireWorks or 
                           GoalType.FlowerPot or GoalType.GiantPinata or GoalType.GoldenScarab or 
                           GoalType.IceBar or GoalType.IceCubes or GoalType.LightBulbs or 
                           GoalType.Litters or GoalType.MetalBar or GoalType.MoneyBag or 
                           GoalType.Pinata or GoalType.Petal or GoalType.Safe or 
                           GoalType.Shelf or GoalType.Sheep or GoalType.SlotMachine or 
                           GoalType.SodaBottle or GoalType.Squid or GoalType.Stickers or 
                           GoalType.Vase or GoalType.Watermelon or GoalType.Stone or
                           GoalType.Pouch or GoalType.SeaMine;
        }

        public static bool IsGoalTileHaveSkins(this GoalType goal)
        {
            return goal is GoalType.Chicken or GoalType.Bee or GoalType.Mole or GoalType.Squid or
                           GoalType.BowlingPin or GoalType.SodaBottle or GoalType.DynamiteStick or
                           GoalType.Shelf or GoalType.LightBulbs;
        }

        public static string ToShortString(this GoalType goalType)
        {
            if (goalType.IsTileKind())
                return goalType.ToNameString().Substring(0, 2);

            var str = goalType.ToNameString();

            var result = string.Empty;
            foreach(var ch in str)
                if (char.IsUpper(ch))
                    result += ch;

            return result;
        }

        public static string ToNameString(this GoalType goalType)
        {
            return goalType.ToString();
        }

        /// <summary>
        /// Check if goal has associated tile, which can possibly be spawned from cell spawners.
        /// </summary>
        /// <remarks>
        /// Non-spawnable goals can refresh remaining counter simply by checking all cells on grid,
        /// while spawnable goals can not, and they need more complicated logic, which will also check all spawners if they have required tile to spawn.
        /// </remarks>
        public static bool IsSpawnable(this GoalType goal)
        {
            return goal.IsTileKind() ||
                        goal is GoalType.DropItems or GoalType.Litters or 
                        GoalType.Pinata or GoalType.Watermelon or GoalType.Vase or 
                        GoalType.MoneyBag or GoalType.Penguin;
        }

        /// <summary>
        /// Is goal can be replenished at runtime (total count increase during gameplay).
        /// </summary>
        /// <remarks>
        /// Currently only Sand goal have counter, which may increase total remaining items (as sand can grow on grid).
        /// Also sand's total counter is same as remaining counter.
        /// </remarks>
        public static bool CanReplenish(this GoalType goal)
        {
            return goal is GoalType.Sand or GoalType.Backgrounds or GoalType.Petal;
        }

        public static GoalType[] GridBasedTypes()
        {
            if (_cachedGridBaseTypes == null)
            {
                int count = 0;
                var types = GetAllGoals();
                foreach (GoalType type in types)
                {
                    if (type.IsGridBased())
                    {
                        count++;
                    }
                }

                _cachedGridBaseTypes = new GoalType[count];
                int index = 0;
                foreach (GoalType type in types)
                {
                    if (type.IsGridBased())
                    {
                        _cachedGridBaseTypes[index] = type;
                        index++;
                    }
                }
            }

            return _cachedGridBaseTypes;
        }

        public static GoalType[] TileKindTypes()
        {
            if (_cachedTileKindTypes == null)
            {
                int count = 0;
                var types =  GetAllGoals();
                foreach (GoalType type in types)
                {
                    if (type.IsTileKind())
                    {
                        count++;
                    }
                }

                _cachedTileKindTypes = new GoalType[count];
                int index = 0;
                foreach (GoalType type in types)
                {
                    if (type.IsTileKind())
                    {
                        _cachedTileKindTypes[index] = type;
                        index++;
                    }
                }
            }

            return _cachedTileKindTypes;
        }

        /// <summary>
        /// Is goal visual state should not have current and total counters,
        /// instead only one counter should be displayed.
        /// </summary>
        /// <remarks>
        /// For sand we display only one counter (not two, like for all others),
        /// because sand can grow on grid and therefore change max remaining count.
        /// </remarks>
        public static bool IsDisplayOnlyRemainingCount(this GoalType goalType)
        {
            return goalType == GoalType.Sand;
        }

        public static bool IsGridBased(this GoalType goalType)
        {
            return goalType != GoalType.None && goalType != GoalType.Score && !goalType.IsTileKind();
        }

        public static bool IsTileKind(this GoalType goalType)
        {
            return goalType is GoalType.Green or GoalType.Yellow or GoalType.Blue or 
                               GoalType.Red or GoalType.Purple or GoalType.Orange or 
                               GoalType.White;
        }

        public static bool IsStrictGridGoal(this GoalType goalType)
        {
            return goalType is GoalType.Stickers or GoalType.Litters or GoalType.Backgrounds or
                               GoalType.DropItems or GoalType.IceCubes or GoalType.Pinata or
                               GoalType.ColorCrate or GoalType.Watermelon or GoalType.Squid or
                               GoalType.Mole or GoalType.BowlingPin or GoalType.Bush or
                               GoalType.SodaBottle or GoalType.Safe or GoalType.Chains or
                               GoalType.IceBar or GoalType.DynamiteStick or GoalType.GiantPinata or
                               GoalType.MetalBar or GoalType.Shelf or GoalType.LightBulbs or
                               GoalType.GoldenScarab or GoalType.FireWorks or GoalType.SlotMachine or
                               GoalType.Stone or GoalType.Pouch or GoalType.SeaMine;
        }

        public static TileKinds ToTileKind(this GoalType goalType)
        {
            return goalType switch
            {
                GoalType.Green => TileKinds.Green,
                GoalType.Yellow => TileKinds.Yellow,
                GoalType.Blue => TileKinds.Blue,
                GoalType.Red => TileKinds.Red,
                GoalType.Purple => TileKinds.Purple,
                GoalType.Orange => TileKinds.Orange,
                GoalType.White => TileKinds.White,
                _ => throw new Exception($"goalType goal type has no conversion to tile kind"),
            };
        }

        public static void DebugValidateGoalIsSingular(this GoalType goal)
        {
            DebugValidateGoalIsSingular((long)goal);
        }

        public static void DebugValidateGoalIsSingular(this long goalNum)
        {
            // GoalType is serialized in scriptable object as long typed value (this was required
            // because enum serialization doesn't support long base type in Unity).
            // In combination with the fact that the goal enum is BitFlag means
            // that there is possible new kind of human-mistake in serialized settings.
            // This method helps detect possible mistake by counting how many bits flipped inside integer. -VK
            int count = 0;
            for (int i = 0; i < 64; i++)
            {
                var num = 1L << i;
                if ((goalNum & num) != 0)
                {
                    count++;
                    if (count > 1)
                    {
                        BDebug.LogError(LogCat.Match3,"Goal is not singular");
                        break;
                    }
                }
            }
        }
    }

    public enum GoalType : long
    {
        None           = 0,
        Score          = 1,

        //grid based

        /// <summary>
        /// Grass.
        /// </summary>
        /// <remarks>
        /// Corresponding to cells with CellState BackOne or BackDouble.
        /// And also cell Background property, which is hp value.
        /// </remarks>
        Backgrounds    = 2,
        DropItems      = 4,
        Stickers       = 8,
        Litters        = 16,
        IceCubes       = 32,
        Pinata         = 64,
        Animal         = 128,
        Sand           = 256,

        /// <summary>
        /// Overlay grass, which hides tiles under itself.
        /// </summary>
        /// <remarks>
        /// Uses cell Background property as hp value.
        /// </remarks>
        Ivy            = 512,
        //tile kind
        Green          = 1024,
        Yellow         = 2048,
        Blue           = 4096,
        Red            = 8192,
        Purple         = 16384,
        Orange         = 32768,
        White          = 65536,
        ColorCrate     = 131072,
        Watermelon     = 262144,
        Vase           = 524288,
        MoneyBag       = 1048576,
        Penguin        = 2097152,
        Egg            = 4194304,
        Bird           = 8388608,
        Banana         = 16777216,
        Sheep          = 33554432,
        Skunk          = 67108864,
        Chicken        = 268435456,
        Bee            = 536870912,
        Mole           = 1073741824,
        Squid          = 2147483648,
        Toad           = 4294967296,
        BowlingPin     = 17179869184,
        Bush           = 34359738368,
        SodaBottle     = 68719476736,
        MagicHat       = 137438953472,
        Safe           = ************,
        FlowerPot      = ************,
        Petal          = 1099511627776,
        Chains         = 2199023255552,
        Tnt            = 4398046511104,
        IceBar         = 8796093022208,
        DynamiteStick  = 17592186044416,
        GiantPinata    = 35184372088832,
        MetalBar       = 70368744177664,
        Shelf          = 140737488355328,
        LightBulbs     = 281474976710656,
        DestructibleWall = 562949953421312,
        GoldenScarab   = 1125899906842624,
        Gondola        = 2251799813685248,
        TukTuk         = 4503599627370496,
        FireWorks      = 9007199254740992,
        SlotMachine    = 18014398509481984,
        Stone          = 36028797018963968,
        Pouch          = 72057594037927936,
        SeaMine        = 144115188075855872,
    }

    /// <summary>
    /// Attribute that should be applied to serialized GoalType fields of type 'long'.
    /// This attribute will make goal name to appear in Unity Inspector window for this field.
    /// </summary>
    /// <remarks>
    /// Goal type enum can't be serialized directly, because it is a long-base-type enum that is not supported by Unity editor.
    /// Because of this limitation all serialized GoalType fields must be declared as 'long' type fields,
    /// and then converted to enum in runtime.
    ///
    /// This attribute helps visualize the actual goal type name in unity editor for integer fields.
    /// </remarks>
    [AttributeUsage(AttributeTargets.Field)]
    public class GoalTypeNameAttribute : UnityEngine.PropertyAttribute
    {
    }

#if UNITY_EDITOR

    [UnityEditor.CustomPropertyDrawer(typeof(GoalTypeNameAttribute))]
    public class GoalTypeNameDrawer : UnityEditor.PropertyDrawer
    {
        public override float GetPropertyHeight(UnityEditor.SerializedProperty property, UnityEngine.GUIContent label)
        {
            return UnityEditor.EditorGUI.GetPropertyHeight(property, label, includeChildren: true);
        }

        public override void OnGUI(UnityEngine.Rect position, UnityEditor.SerializedProperty property, UnityEngine.GUIContent label)
        {
            UnityEditor.EditorGUI.PropertyField(position, property, label);
            GoalType layerStateValue = (GoalType)property.longValue;
            const int width = 120;
            var labelPosition = new UnityEngine.Rect(new UnityEngine.Vector2(position.xMax - width - 10f, position.yMin), new UnityEngine.Vector2(width, 20f));
            UnityEditor.EditorGUI.LabelField(labelPosition, layerStateValue.ToString(), UnityEngine.GUI.skin.GetStyle("miniLabel"));
        }
    }
#endif
}
