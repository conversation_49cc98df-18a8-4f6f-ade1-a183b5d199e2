using UnityEngine;
using UnityEngine.UI.Extensions;

namespace BBB
{
    public class ParticlesEffect : BbbMonoBehaviour, IPoolItem
    {
        private UIParticleSystem[] _particleSystems;
        
        public void OnInstantiate()
        {
            _particleSystems = GetComponentsInChildren<UIParticleSystem>();
           
        }

        public void OnSpawn()
        {
            foreach (var ps in _particleSystems)
            {
                ps.StartParticleEmission();
            }
        }

        public void OnRelease()
        {
            foreach (var ps in _particleSystems)
                ps.StopParticleEmission();
        }
    }
}