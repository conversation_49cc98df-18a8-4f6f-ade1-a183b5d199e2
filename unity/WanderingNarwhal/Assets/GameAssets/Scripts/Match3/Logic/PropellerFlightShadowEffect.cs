using BBB;
using BBB.Core;
using DG.Tweening;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    public class PropellerFlightShadowEffect : ActivePoolItem
    {
        [SerializeField] private RectTransform _scaleContainer;
        [SerializeField] private RectTransform _propellerShadow;

        private RectTransform _transform;
        private Vector3 _initialPosition;
        private Vector3 _initialScale;
        private RectTransform _followTransform;
        private Tweener _tweener;

        private void Awake()
        {
            _transform = transform as RectTransform;
            _initialPosition = _transform.localPosition;
            _initialScale = _scaleContainer.localScale;
        }

        public void LaunchFollowTransform(RectTransform followTransform, float duration, Vector3 scale, Vector3 shadowDistance, AnimationCurve scaleEase)
        {
            _scaleContainer.localScale = Vector3.Scale(_scaleContainer.localScale, scale);

            _followTransform = followTransform;
            _tweener = _propellerShadow.DOLocalMove(shadowDistance, duration)
                .OnUpdate(() =>
                {
                    if (_followTransform != null)
                    {
                        _transform.position = _followTransform.position;
                    }
                })
                .SetUpdate(UpdateType.Late)
                .OnComplete(CompleteAction)
                .SetEase(scaleEase)
                .SetRelative();
        }

        public override void OnSpawn()
        {
            if (this == null) return;
            base.OnSpawn();
        }

        public override void OnRelease()
        {
            if (this == null) return;
            _tweener?.Kill();
            _tweener = null;

            _scaleContainer.localScale = _initialScale;
            _transform.localPosition = _initialPosition;
            _followTransform = null;

            base.OnRelease();
        }

        private void CompleteAction()
        {
            gameObject.Release();
        }
    }
}