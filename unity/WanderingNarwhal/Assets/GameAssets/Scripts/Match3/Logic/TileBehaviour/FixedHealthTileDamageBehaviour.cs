using System.Collections.Generic;
using BBB;
using BBB.Match3.Renderer;
using BebopBee.Core.Audio;
using Bebopbee.Core.Extensions.Unity;
using UnityEngine;

public class FixedHealthTileDamageBehaviour : TileLayerViewBase, ICounteredTile
{
    protected TileLayerRendererBase Renderer;
    private int _initialHealth;
    protected bool OverrideSortingLayer = false;
    private List<SpriteRenderer> _spriteRenderers;
    private List<SpriteMask> _spriteMasks;
    private List<ParticleSystemRenderer> _psRenderers;
    
    public override int SortingLayer
    {
        set
        {
            if (OverrideSortingLayer)
            {
                UpdateSortingLayer(value);
            }
        }
    }

    protected FixedHealthTileDamageBehaviour(ITileLayer layer) : base(layer)
    {
    }

    protected override void OnInstantiateViewUpdated(GameObject instance, Tile tile, Vector2 cellSize)
    {
        Renderer = instance.GetComponent<TileLayerRendererBase>();
        _initialHealth = tile.GetParam(TileParamEnum.AdjacentHp);

        Renderer.LayerIndex = Animator.StringToHash(Renderer.LayerIndexName);
        Renderer.AnimationTrigger = Animator.StringToHash(Renderer.AnimationTriggerName);
        Renderer.Content.UpdateRectForTile(Renderer.TileBaseMinOffset, Renderer.TileBaseMaxOffset);
        Renderer.Init(tile);
        UpdateRendererLayers(_initialHealth);
        
        if (OverrideSortingLayer)
        {
            CacheRenderers();
        }
    }
    
    private void UpdateRendererLayers(int health)
    {
        if (Renderer.Layers is { Count: > 0 })
        {
            for (var i = 0; i < Renderer.Layers.Count; i++)
            {
                Renderer.Layers[i].SetActive(i < health);
            }
        }
    }

    private void CacheRenderers()
    {
        foreach (var spriteRenderer in Renderer.GetComponentsInChildren<SpriteRenderer>(true))
        {
            _spriteRenderers ??= new List<SpriteRenderer>();
            _spriteRenderers.Add(spriteRenderer);
        }

        foreach (var spriteMask in Renderer.GetComponentsInChildren<SpriteMask>(true))
        {
            _spriteMasks ??= new List<SpriteMask>();
            _spriteMasks.Add(spriteMask);
        }

        foreach (var psRenderer in Renderer.GetComponentsInChildren<ParticleSystemRenderer>(true))
        {
            _psRenderers ??= new List<ParticleSystemRenderer>();
            _psRenderers.Add(psRenderer);
        }
    }

    private void UpdateSortingLayer(int sortingLayer)
    {
        if (_spriteRenderers != null)
        {
            foreach (var spriteRenderer in _spriteRenderers)
            {
                spriteRenderer.sortingLayerID = sortingLayer;
            }
        }

        if (_spriteMasks != null)
        {
            foreach (var spriteMask in _spriteMasks)
            {
                spriteMask.frontSortingLayerID = sortingLayer;
            }
        }

        if (_psRenderers != null)
        {
            foreach (var psRenderer in _psRenderers)
            {
                psRenderer.sortingLayerID = sortingLayer;
            }
        }
    }

    public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList,
        bool isVisible)
    {
        base.Apply(tile, coords, container, viewsList, isVisible);
        var count = SelectSheetsCountValue(tile);
        ApplyDamage(count, tile, coords);
    }

    public void ChangeCount(Cell cell)
    {
        ApplyDamage(SelectSheetsCountValue(cell.Tile), cell.Tile, cell.Coords);
    }

    private static int SelectSheetsCountValue(Tile tile)
    {
        return tile.GetParam(TileParamEnum.AdjacentHp);
    }

    protected virtual void ApplyDamage(int newLevel, Tile tile, Coords? coords = null)
    {
        if (newLevel == _initialHealth)
        {
            return;
        }

        var tileBehaviorContext = new TileBehaviorContext
        {
            FxContext = new PlayFxContext
            {
                FxType = Renderer.FXTypes[newLevel],
                FxRenderer = FxRenderer,
                Position = coords ?? Coords.Zero,
                Duration = Renderer.FXDestroyDurations[newLevel]
            },
            SoundContext = new PlaySoundContext
            {
                SoundId = Renderer.SoundIds[newLevel]
            }
        };

        UpdateRendererLayers(newLevel);

        // All our animations are indexed from 1
        newLevel += 1;

        if (Renderer.Animator != null)
        {
            tileBehaviorContext.AnimationContext = new PlayAnimationContext
            {
                Animator = Renderer.Animator,
                LayerIndex = Renderer.LayerIndex,
                AnimationTrigger = Renderer.AnimationTrigger,
                UpdatedValue = newLevel
            };
        }

        if (Renderer.SpineAnimator != null)
        {
            tileBehaviorContext.SpineContext = new PlaySpineContext
            {
                SkeletonGraphic = Renderer.SpineAnimator,
                LayerIndexName = Renderer.LayerIndexName,
                UpdatedValue = newLevel
            };
        }

        ApplyDamage(tileBehaviorContext);
    }

    private static void ApplyDamage(TileBehaviorContext tileBehaviorContext)
    {
        var fxContext = tileBehaviorContext.FxContext;
        var soundContext = tileBehaviorContext.SoundContext;
        var animationContext = tileBehaviorContext.AnimationContext;
        var spineContext = tileBehaviorContext.SpineContext;

        AudioProxy.PlaySound(soundContext.HasValue ? soundContext.Value.SoundId : string.Empty);

        fxContext?.FxRenderer.SpawnSingleAnimatorEffect(fxContext.Value.Position, fxContext.Value.FxType,
            fxContext.Value.Duration);

        animationContext?.Animator.SetInteger(animationContext.Value.LayerIndex, animationContext.Value.UpdatedValue);
        animationContext?.Animator.SetTrigger(animationContext.Value.AnimationTrigger);

        spineContext?.SkeletonGraphic.AnimationState.SetAnimation(0,
            spineContext.Value.LayerIndexName + spineContext.Value.UpdatedValue, false);
    }

    public override void Animate(Coords coords, TileLayerViewAnims anim,
        TileLayerViewAnimParams animParams = TileLayerViewAnimParams.None)
    {
        switch (anim)
        {
            case TileLayerViewAnims.CustomAppear:
                Renderer.PlayAppear();
                break;

            case TileLayerViewAnims.Preview:
                Renderer.Content.UpdateRectForTile(Renderer.TileHelpMinOffset, Renderer.TileHelpMaxOffset);
                Renderer.Show();
                break;

            case TileLayerViewAnims.TapFeedback:
                Renderer.PlayTapFeedback(this);
                break;
        }
    }
}