using BBB;
using UnityEngine;
using System.Collections.Generic;

namespace GameAssets.Scripts.Match3.Logic
{
    public class SingleAnimatorEffectSticker: SingleAnimatorEffect
    {
        public List<GameObject> _crateParticles;
        public List<GameObject> _slateParticles;

        public override void ApplyParameters(FxOptionalParameters prm)
        {
            base.ApplyParameters(prm);
            var skinPrm = (StickerSkin)prm.skin;
            if (skinPrm == StickerSkin.Slate)
            {
                foreach (var vCrateParticle in _crateParticles)
                {
                    vCrateParticle.gameObject.SetActive(false);
                }
                foreach (var vSlateParticle in _slateParticles)
                {
                    vSlateParticle.gameObject.SetActive(true);
                }
            }
            else
            {
                foreach (var vCrateParticle in _crateParticles)
                {
                    vCrateParticle.gameObject.SetActive(true);
                }
                foreach (var vSlateParticle in _slateParticles)
                {
                    vSlateParticle.gameObject.SetActive(false);
                }
            }
        }
    }
}