using System;
using BBB;
using BBB.Core;
using BBB.Match3.Renderer;
using GameAssets.Scripts.Match3.Settings;
using JetBrains.Annotations;
using Spine.Unity;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    public class BoltBoltCombineEffect : ActivePoolItem
    {
        [SerializeField] private Transform _firstTileHolder;
        [SerializeField] private Transform _secondTileHolder;
        [SerializeField] private SkeletonGraphic[] _skeletonGraphics;

        private Transform _initialFirstTileParent;
        private Transform _initialSecondTileParent;

        private Transform _firstTileViewTf;
        private Transform _secondTileViewTf;

        private TileView _firstTileView;
        private TileView _secondTileView;

        private Vector3 _initialLocalPositionFirst;
        private Vector3 _initialLocalPositionSecond;

        private Quaternion _initialRotation;
        private Action _onExplosion;

        public void Setup(TileView firstTileView, TileView secondTileView, M3Settings settings, Action onExplosion)
        {
            ApplySkin(settings);
            
            _firstTileView = firstTileView;
            _secondTileView = secondTileView;

            _firstTileView.RunningBoltBoltAnimation = true;
            _secondTileView.RunningBoltBoltAnimation = true;

            _firstTileViewTf = firstTileView.transform;
            _secondTileViewTf = secondTileView.transform;

            _initialLocalPositionFirst = _firstTileViewTf.localPosition;
            _initialLocalPositionSecond = _secondTileViewTf.localPosition;

            _initialRotation = transform.rotation;

            var localPositionDelta = _initialLocalPositionFirst - _initialLocalPositionSecond;
            localPositionDelta.x = Mathf.Abs(localPositionDelta.x);
            localPositionDelta.y = Mathf.Abs(localPositionDelta.y);

            _initialFirstTileParent = _firstTileViewTf.parent;
            _initialSecondTileParent = _secondTileViewTf.parent;

            firstTileView.Animator.SetAnimatorEnabled(false);
            secondTileView.Animator.SetAnimatorEnabled(false);

            _firstTileViewTf.SetParent(_firstTileHolder, true);
            _secondTileViewTf.SetParent(_secondTileHolder, true);

            _onExplosion = onExplosion;
        }
        
        private void ApplySkin(M3Settings m3Settings)
        {
            if (_skeletonGraphics != null)
            {
                foreach (var skeletonGraphic in _skeletonGraphics)
                {
                    if (skeletonGraphic != null)
                    {
                        skeletonGraphic.Skeleton.SetSkin(m3Settings.GetDiscoBallSkin());
                    }
                }
            }
        }

        public override void OnRelease()
        {
            base.OnRelease();
            ReleaseViews();
        }

        [UsedImplicitly]
        private void OnAnimationFinished()
        {
            _onExplosion.SafeInvoke();
            _onExplosion = null;
            ReleaseViews();
            gameObject.Release();
        }

        private void ReleaseViews()
        {
            if (_firstTileViewTf)
            {
                _firstTileViewTf.SetParent(_initialFirstTileParent);
                _firstTileViewTf.localPosition = _initialLocalPositionFirst;
                _firstTileViewTf.localScale = new Vector3(1f, 1f, 1f);
                _firstTileViewTf = null;
            }

            if (_secondTileViewTf)
            {
                _secondTileViewTf.SetParent(_initialSecondTileParent);
                _secondTileViewTf.localPosition = _initialLocalPositionSecond;
                _secondTileViewTf.localScale = new Vector3(1f, 1f, 1f);
                _secondTileViewTf = null;
            }

            _initialFirstTileParent = null;
            _initialSecondTileParent = null;

            if (_firstTileView)
            {
                _firstTileView.RunningBoltBoltAnimation = false;
                _firstTileView.Animator.SetAnimatorEnabled(true);
                _firstTileView.Hide();
                _firstTileView = null;
            }

            if (_secondTileView)
            {
                _secondTileView.Animator.SetAnimatorEnabled(true);
                _secondTileView.Hide();
                _secondTileView.RunningBoltBoltAnimation = false;
                _secondTileView = null;
            }

            var selfTransform = transform;
            if (selfTransform)
                selfTransform.rotation = _initialRotation;
        }
    }
}