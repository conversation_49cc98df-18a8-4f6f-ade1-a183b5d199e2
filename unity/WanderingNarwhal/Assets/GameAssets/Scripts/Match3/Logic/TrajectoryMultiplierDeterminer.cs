using UnityEngine;
using Grid = BBB.Grid;

namespace GameAssets.Scripts.Match3.Logic
{
    public class TrajectoryMultiplierDeterminer
    {
        private readonly int _gridMinX;
        private readonly int _gridMaxX;

        private readonly int _gridMinY;
        private readonly int _gridMaxY;

        public TrajectoryMultiplierDeterminer(Grid grid)
        {
            int minX = int.MaxValue;
            int maxX = int.MinValue;
            int minY = int.MaxValue;
            int maxY = int.MinValue;
            foreach (var cell in grid.Cells)
            {
                var x = cell.Coords.X;
                if (x < minX)
                {
                    minX = x;
                }

                if (x > maxX)
                {
                    maxX = x;
                }

                var y = cell.Coords.Y;
                if (y < minY)
                {
                    minY = y;
                }

                if (y > maxY)
                {
                    maxY = y;
                }
            }
            
            _gridMinX = minX;
            _gridMaxX = maxX;
            _gridMinY = minY;
            _gridMaxY = maxY;
        }

        public bool IsPointOutsideBoardRegion(Vector2 point, int offsetX, int offsetY)
        {
            return point.x < _gridMinX+offsetX || point.x > _gridMaxX+1-offsetX || point.y < _gridMinY+offsetY || point.y > _gridMaxY+1-offsetY;
        }

        public int GetTrajectoryMultiplier(params int[] sourceCoordXValues)
        {
            int minX = int.MaxValue;
            int maxX = int.MinValue;
            
            foreach (var x in sourceCoordXValues)
            {
                if (x < minX)
                {
                    minX = x;
                }

                if (x > maxX)
                {
                    maxX = x;
                }
            }

            if (minX <= _gridMinX + 1)
                return -1;

            if (maxX >= _gridMaxX - 1)
                return 1;

            return 0;
        }
    }
}