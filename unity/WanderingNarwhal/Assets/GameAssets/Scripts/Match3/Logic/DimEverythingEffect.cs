using UnityEngine;
using BBB.Core;

namespace BBB
{
    public class DimEverythingEffect : ActivePoolItem
    {
        private static  DimEverythingEffect _activeInstance;
        private static readonly int ExitLoopHash = Animator.StringToHash("ExitLoop");

        [SerializeField] private Animator _animator;

        public void Play()
        {
            _animator.Rebind();
			
            _activeInstance = this;
        }

        public static void StopDim()
        {
            if (_activeInstance != null)
            {
                _activeInstance._animator.SetTrigger(ExitLoopHash);
                _activeInstance = null;
            }
        }

        //DO NOT REMOVE THIS ANIMATOR CALLBACK
        private void OnOutroCallback()
        {
            gameObject.Release();
        }
    }
}
