using System.Collections.Generic;

namespace BBB.GameAssets.Scripts.Match3.Logic
{
    public class StashedDropItem
    {
        public HashSet<Coords> Spawners { get; private set; }
        public int Moves { get; private set; }

        private StashedDropItem() {}
            
        public StashedDropItem(HashSet<Coords> spawners, int moves)
        {
            Spawners = spawners ?? new HashSet<Coords>();
            Moves = moves;
        }

        public StashedDropItem Clone()
        {
            var result = new StashedDropItem();
            result.Spawners = new HashSet<Coords>(Spawners);
            result.Moves = Moves;

            return result;

        }
    }
}