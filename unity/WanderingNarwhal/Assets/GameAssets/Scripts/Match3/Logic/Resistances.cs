using System;
using BBB.Match3.Renderer;

namespace BBB.Match3.Logic
{
    [Flags]
    public enum DamageSource : ulong
    {
        None                                            = 0, //0,
        Adjacent                                        = 1L, //1,
        Gondola                                         = 1L << 2,
        LineBreakerArrowHor                             = 1L << 6, //64,
        Bomb                                            = 1L << 7, //128,
        Skunk                                           = 1L << 8, //256,
        FireWorks                                       = 1L << 9, //512
        Dynamite                                        = 1L << 10, //1024,
        Whirlpool                                       = 1L << 11, //2048,
        RemoveColorTiles                                = 1L << 12, //4096,
        TukTuk                                          = 1L << 13, //8192,
        AutoMatch                                       = 1L << 14, //16384,
        UsableBoost                                     = 1L << 18, //262144,
        EndGame                                         = 1L << 19, //524288,
        MultiBomb                                       = 1L << 20, //1048576,
        LineBreakerArrowVer                             = 1L << 21, //2097152,

        Swap                                            = 1L << 23,

        /// <summary>
        /// Double tap input, only for boosters.
        /// </summary>
        DTap                                           = 1L << 24,
        BoostClear                                     = 1L << 25,
        SuperBoost                                     = 1L << 26,
        AdjacentFromBoost                              = 1L << 27,
        SmallCross                                     = 1L << 28,
        Propeller                                      = 1L << 29,
        PropellerCombo                                 = 1L << 30,
        InputMatch                                     = 1L << 31,
        SeaMine                                        = 1L << 32,
        
        TileKindDependent = Match | Adjacent | RemoveColorTiles,
        AnyBomb = Bomb | MultiBomb,
        Match = AutoMatch | InputMatch,
        PropellerOrPropellerCombo = Propeller | PropellerCombo,
        AdjacentGeneral = Adjacent | AdjacentFromBoost,
        ForceSkipDestroyAnim = TapOrSwap | BoostClear | Match,
        LineBreakerArrow = LineBreakerArrowHor | LineBreakerArrowVer,
        PowerUp = BoostClear | LineBreakerArrow | AnyBomb | SmallCross | TargetingMechanics | TukTuk | Whirlpool | Dynamite | SuperBoost | UsableBoost | SeaMine,
        AllBase = PowerUp | RemoveColorTiles | Match | EndGame,
        AddsToSuperBoost = DTap | UsableBoost | BoostClear | LineBreakerArrow | AnyBomb | SmallCross | PropellerOrPropellerCombo,
        TapOrSwap = DTap | Swap,
        TargetingMechanics = PropellerOrPropellerCombo | Skunk | FireWorks,
        AdjacentWithPowerUp = AdjacentGeneral | PowerUp,
    }

    public static class ResistanceDamageHelper
    {
        public static TileLayerViewAnimParams ToAnimParams(this DamageSource damageSource)
        {
            var animParams = TileLayerViewAnimParams.None;
            
            switch (damageSource)
            {
                case DamageSource.Whirlpool:
                {
                    animParams |= TileLayerViewAnimParams.ByWhirlpool;
                    break;
                }
                case DamageSource.RemoveColorTiles:
                {
                    animParams |= TileLayerViewAnimParams.ByRemoveColorTiles;
                    break;
                }
            }

            return animParams;
        }
        
        public static bool CanDamage(this DamageSource damage, DamageSource allowedDamage)
        {
            // Can not be destroyed by anything:
            if (allowedDamage == DamageSource.None) return false;

            return (damage & allowedDamage) != 0;
        }

        public static bool IsAnyOf(this DamageSource damage, DamageSource allowedDamage)
        {
            // Can not be destroyed by anything:
            if (allowedDamage == DamageSource.None) return false;

            return (damage & allowedDamage) != 0;
        }

        public static bool Is(this DamageSource damage, DamageSource allowedDamage)
        {
            // Can not be destroyed by anything:
            if (allowedDamage == DamageSource.None) return false;

            return (damage & allowedDamage) == allowedDamage;
        }
    }
}
