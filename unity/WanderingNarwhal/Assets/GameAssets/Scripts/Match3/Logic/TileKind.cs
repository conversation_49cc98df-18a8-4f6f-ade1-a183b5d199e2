using GameAssets.Scripts.Match3.Logic;

namespace BBB
{
    public static class TileKindExtensions
    {
        public static bool IsColored(this TileKinds tileKind)
        {
            return (int)tileKind > 0;
        }

        public static GoalType ToGoalType(this TileKinds tileKind)
        {
            switch (tileKind)
            {
                case TileKinds.Green:
                    return GoalType.Green;
                case TileKinds.Yellow:
                    return GoalType.Yellow;
                case TileKinds.Blue:
                    return GoalType.Blue;
                case TileKinds.Red:
                    return GoalType.Red;
                case TileKinds.Purple:
                    return GoalType.Purple;
                case TileKinds.Orange:
                    return GoalType.Orange;
                case TileKinds.White:
                    return GoalType.White;
                default:
                    return GoalType.None;
            }
        }

        public static TileAsset ToSimpleTileAsset(this TileKinds tileKind)
        {
            return tileKind switch
            {
                TileKinds.Green => TileAsset.Green,
                TileKinds.Yellow => TileAsset.Yellow,
                TileKinds.Blue => TileAsset.Blue,
                TileKinds.Red => TileAsset.Red,
                TileKinds.Purple => TileAsset.Purple,
                TileKinds.White => TileAsset.White,
                _ => TileAsset.Undefined,
            };
        }

        public static TileAsset ToColorCrateTileAsset(this TileKinds tileKind)
        {
            return tileKind switch
            {
                TileKinds.Green => TileAsset.ColorCrateGreen,
                TileKinds.Yellow => TileAsset.ColorCrateYellow,
                TileKinds.Blue => TileAsset.ColorCrateBlue,
                TileKinds.Red => TileAsset.ColorCrateRed,
                TileKinds.Purple => TileAsset.ColorCratePurple,
                TileKinds.White => TileAsset.ColorCrateWhite,
                _ => TileAsset.Undefined,
            };
        }

        public static TntTargetType ToTntTarget(this TileKinds tileKind)
        {
            return tileKind switch
            {
                TileKinds.Green => TntTargetType.Green,
                TileKinds.Yellow => TntTargetType.Yellow,
                TileKinds.Blue => TntTargetType.Blue,
                TileKinds.Red => TntTargetType.Red,
                TileKinds.Purple => TntTargetType.Purple,
                TileKinds.White => TntTargetType.White,
                _ => TntTargetType.Simple,
            };
        }
    }
    
    //do not change the values or names
    public enum TileKinds : short
    {
        Error = -3,
        Undefined = -2,
        None = 0,
        Green = 1,
        Yellow = 2,
        Blue = 3,
        Red = 4,
        Purple = 5,
        Orange = 6,
        White = 7,
        COUNT
    }
}