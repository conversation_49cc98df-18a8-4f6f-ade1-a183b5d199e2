using UnityEngine;
using System.Collections.Generic;
using BBB.Match3.Renderer;

namespace BBB
{
    public class MatchEffect : BbbMonoBehaviour, IPoolItem
    {
        private HashSet<Coords> _alreadyProcessedCoords;
        private Grid _grid;
        private TileController _tileController;

        public void Init(Grid grid, TileController tileController, List<Match> matches)
        {
            _grid = grid;
            _tileController = tileController;
            _alreadyProcessedCoords = new HashSet<Coords>();
            foreach (var match in matches)
            {
                foreach (var coord in match)
                {
                    ProcessCoordinate(match, coord, coord.GoSingleCardinalDirection(cardinalDirections: CardinalDirections.E));
                    ProcessCoordinate(match, coord, coord.GoSingleCardinalDirection(cardinalDirections: CardinalDirections.W));
                    ProcessCoordinate(match, coord, coord.GoSingleCardinalDirection(cardinalDirections: CardinalDirections.N));
                    ProcessCoordinate(match, coord, coord.GoSingleCardinalDirection(cardinalDirections: CardinalDirections.S));
                }
            }
        }

        public void OnInstantiate()
        {
            gameObject.SetActive(false);
        }
        
        public void OnSpawn()
        {
            gameObject.SetActive(true);
        }

        public void OnRelease()
        {
            gameObject.SetActive(false);
        }

        private void ProcessCoordinate(Match match, Coords origin, Coords coord)
        {
            if (!match.Contains(coord) && !_alreadyProcessedCoords.Contains(coord))
            {
                StartMatchEffect(origin, coord);
                _alreadyProcessedCoords.Add(coord);
            }
        }

        private void StartMatchEffect(Coords origin, Coords coord)
        {
            if (!_grid.TryGetCell(coord, out Cell cell)) return;
            if (!cell.HasTile()) return;
            var view = _tileController.GetOrCreateTileView(cell.Tile);
            view.Animator.StartSubtleShockwaveEffect();
        }

    }
}
