using System;
using System.Collections.Generic;
using BBB;
using BBB.CellTypes;
using UnityEngine;
using Grid = BBB.Grid;

[Flags]
public enum CardinalDirections
{
    // ReSharper disable InconsistentNaming
    None = 0,
    N = 1,
    NE = 2,
    E = 4,
    SE = 8,
    S = 16,
    SW = 32,
    W = 64,
    NW = 128,
    N_S = N | S,
    E_W = E | W,
    N_E_S_W = N | E | S | W
    // ReSharper restore InconsistentNaming
}

public static class CardinalDirectionsHelper
{
    private static readonly Vector2 SouthEastVector = new(1f, -1f);
    private static readonly Vector2 SouthWestVector = new(-1f, -1f);
    private static readonly Vector2 NorthWestVector = new(-1f, 1f);
    
    public static IEnumerable<CardinalDirections> GetAllStraight()
    {
        yield return CardinalDirections.N;
        yield return CardinalDirections.E;
        yield return CardinalDirections.W;
        yield return CardinalDirections.S;
    }

    public static Vector2 ToVector2(this CardinalDirections cd)
    {
        return cd switch
        {
            CardinalDirections.None => Vector2.zero,
            CardinalDirections.N => Vector2.up,
            CardinalDirections.NE => Vector2.one,
            CardinalDirections.E => Vector2.right,
            CardinalDirections.SE => SouthEastVector,
            CardinalDirections.S => Vector2.down,
            CardinalDirections.SW => SouthWestVector,
            CardinalDirections.W => Vector2.left,
            CardinalDirections.NW => NorthWestVector,
            _ => throw new ArgumentOutOfRangeException(nameof(cd), cd, null)
        };
    }

    public static void ToXY(this CardinalDirections cd, out int x, out int y)
    {
        switch (cd)
        {
            case CardinalDirections.None: x = 0; y = 0; break;
            case CardinalDirections.N: x = 0; y = 1; break;
            case CardinalDirections.NE: x = 1; y = 1; break;
            case CardinalDirections.E: x = 1; y = 0; break;
            case CardinalDirections.SE: x = 1; y = -1; break;
            case CardinalDirections.S: x = 0; y = -1; break;
            case CardinalDirections.SW: x = -1; y = -1; break;
            case CardinalDirections.W: x = -1; y = 0; break;
            case CardinalDirections.NW: x = -1; y = 1; break;
            default: x = 0; y = 0; break;
        }
    }

    /// <summary>
    /// Get next not-null cell in south (down) direction at any distance.
    /// </summary>
    /// <remarks>
    /// Used to find next existing cell below current cell, even if there is a gap between them.
    /// Was added because of tiles fall logic update, which makes tiles to fall through gaps by-default. -VK
    /// </remarks>
    public static Coords GoSouthDirectionExtruded(this Coords coords, Grid grid, out int jumpingThroughGapSize)
    {
        var y = coords.Y - 1;
        var result = new Coords(coords.X, y);
        jumpingThroughGapSize = 0;

        // Try find any cell in south (down) direction, regardless of the distance.
        while (result.Y >= 0)
        {
            if (FreeCoordsToMove(grid, result))
            {
                return result;
            }

            result.Y -= 1;
            jumpingThroughGapSize++;
        }

        jumpingThroughGapSize = 0;
        return result;
    }

    public static Coords GoNorthDirectionExtruded(this Coords coords, Grid grid)
    {
        var y = coords.Y + 1;
        var result = new Coords(coords.X, y);

        // Try finding any cell in south (down) direction, regardless of the distance.
        while (result.Y < grid.Height)
        {
            if (FreeCoordsToMove(grid, result))
            {
                return result;
            }
            result.Y += 1;
        }

        return result;
    }

    private static bool FreeCoordsToMove(Grid grid, Coords coords)
    {
        if (!grid.TryGetCell(coords, out var cell))
        {
            return false;
        }

        var nextCell = cell.GetMainCellReference(out _);
        var isGondolaPath = nextCell.IsAnyOf(CellState.GondolaStates);
        return nextCell.HasTile() || !isGondolaPath;
    }

    public static Coords GoDiagDirectionExtruded(this Coords coords, Grid grid, CardinalDirections cardinalDirections)
    {
        var nextCoord = coords.GoSingleCardinalDirection(cardinalDirections);
        
        while (nextCoord.X >= 0 && nextCoord.X < grid.Width && nextCoord.Y >= 0 && nextCoord.Y < grid.Height)
        {
            if (FreeCoordsToMove(grid, nextCoord))
            {
                return nextCoord;
            }
            nextCoord = nextCoord.GoSingleCardinalDirection(CardinalDirections.S);
        }

        return nextCoord;
    }

    public static Coords GoSingleCardinalDirection(this Coords coords, CardinalDirections cardinalDirections, int distance  = 1)
    {
#if M3_DEBUG
        cardinalDirections.CheckForSingleCardinalDirection();
#endif

        return cardinalDirections switch
        {
            CardinalDirections.None => new Coords(coords.X, coords.Y),
            CardinalDirections.N => new Coords(coords.X, coords.Y + distance),
            CardinalDirections.NE => new Coords(coords.X + distance, coords.Y + distance),
            CardinalDirections.E => new Coords(coords.X + distance, coords.Y),
            CardinalDirections.SE => new Coords(coords.X + distance, coords.Y - distance),
            CardinalDirections.S => new Coords(coords.X, coords.Y - distance),
            CardinalDirections.SW => new Coords(coords.X - distance, coords.Y - distance),
            CardinalDirections.W => new Coords(coords.X - distance, coords.Y),
            CardinalDirections.NW => new Coords(coords.X - distance, coords.Y + distance),
            _ => throw new ArgumentOutOfRangeException(nameof(cardinalDirections), cardinalDirections, null)
        };
    }
    
    public static bool Contains(this CardinalDirections cardinalDirections, CardinalDirections singleDirection)
    {
        return (cardinalDirections & singleDirection) == singleDirection;
    }

    public static bool IsAnyOf(this CardinalDirections cardinalDirections, CardinalDirections singleDirection)
    {
        return (cardinalDirections & singleDirection) != 0;
    }

    public static CardinalDirections Reversed(this CardinalDirections cardinalDirections)
    {
        if (cardinalDirections == CardinalDirections.None)
            throw new Exception("[CardinalDirections]: Empty CardinalDirections");

        var n = cardinalDirections.Contains(CardinalDirections.N);
        var ne = cardinalDirections.Contains(CardinalDirections.NE);
        var e = cardinalDirections.Contains(CardinalDirections.E);
        var se = cardinalDirections.Contains(CardinalDirections.SE);
        var s = cardinalDirections.Contains(CardinalDirections.S);
        var sw = cardinalDirections.Contains(CardinalDirections.SW);
        var w = cardinalDirections.Contains(CardinalDirections.W);
        var nw = cardinalDirections.Contains(CardinalDirections.NW);

        return (n ? CardinalDirections.S : CardinalDirections.None)
               | (ne ? CardinalDirections.SW : CardinalDirections.None)
               | (e ? CardinalDirections.W : CardinalDirections.None)
               | (se ? CardinalDirections.NW : CardinalDirections.None)
               | (s ? CardinalDirections.N : CardinalDirections.None)
               | (sw ? CardinalDirections.NE : CardinalDirections.None)
               | (w ? CardinalDirections.E : CardinalDirections.None)
               | (nw ? CardinalDirections.SE : CardinalDirections.None);
    }

    public static CardinalDirections Join(this CardinalDirections firstSingleDirection,
        CardinalDirections secondSingleDirection)
    {
#if M3_DEBUG
        CheckForSingleCardinalDirection(firstSingleDirection);
        CheckForSingleCardinalDirection(secondSingleDirection);
#endif
        var firstN = firstSingleDirection.Contains(CardinalDirections.N);
        var firstE = firstSingleDirection.Contains(CardinalDirections.E);
        var firstS = firstSingleDirection.Contains(CardinalDirections.S);
        var firstW = firstSingleDirection.Contains(CardinalDirections.W);

        var secondN = secondSingleDirection.Contains(CardinalDirections.N);
        var secondE = secondSingleDirection.Contains(CardinalDirections.E);
        var secondS = secondSingleDirection.Contains(CardinalDirections.S);
        var secondW = secondSingleDirection.Contains(CardinalDirections.W);

        if (firstN)
        {
            if(secondE) return CardinalDirections.NE;
            if(secondW) return CardinalDirections.NW;
        }
        if (firstE)
        {
            if(secondN) return CardinalDirections.NE;
            if(secondS) return CardinalDirections.SE;
        }
        if (firstS)
        {
            if (secondE) return CardinalDirections.SE;
            if (secondW) return CardinalDirections.SW;
        }
        if (firstW)
        {
            if (secondN) return CardinalDirections.NW;
            if (secondS) return CardinalDirections.SW;
        }

        if (firstSingleDirection == secondSingleDirection)
            throw new Exception("[CardinalDirections]: Two simple directions are equal!");

        throw new Exception("[CardinalDirections]: Can't join this two simple directions - " + firstSingleDirection + " and " + secondSingleDirection);
    }

    public static void DecomposeToHorVer(this CardinalDirections cardinalDirections, out CardinalDirections hor, out CardinalDirections ver)
    {
        switch (cardinalDirections)
        {
            case CardinalDirections.NE:
                hor = CardinalDirections.E;
                ver = CardinalDirections.N;
                break;
            
            case CardinalDirections.NW:
                hor = CardinalDirections.W;
                ver = CardinalDirections.N;
                break;
            
            case CardinalDirections.SE:
                hor = CardinalDirections.E;
                ver = CardinalDirections.S;
                break;
            case CardinalDirections.SW:
                hor = CardinalDirections.W;
                ver = CardinalDirections.S;
                break;
            default:
                throw new Exception($"{cardinalDirections} is not diag");
        }
    }

    public static HashSet<CardinalDirections> SplitIntoSingleDirections(this CardinalDirections cardinalDirections)
    {
        var singleDirections = new HashSet<CardinalDirections>();

        if (cardinalDirections.Contains(CardinalDirections.N)) singleDirections.Add(CardinalDirections.N);
        if (cardinalDirections.Contains(CardinalDirections.NE)) singleDirections.Add(CardinalDirections.NE);
        if (cardinalDirections.Contains(CardinalDirections.E)) singleDirections.Add(CardinalDirections.E);
        if (cardinalDirections.Contains(CardinalDirections.SE)) singleDirections.Add(CardinalDirections.SE);
        if (cardinalDirections.Contains(CardinalDirections.S)) singleDirections.Add(CardinalDirections.S);
        if (cardinalDirections.Contains(CardinalDirections.SW)) singleDirections.Add(CardinalDirections.SW);
        if (cardinalDirections.Contains(CardinalDirections.W)) singleDirections.Add(CardinalDirections.W);
        if (cardinalDirections.Contains(CardinalDirections.NW)) singleDirections.Add(CardinalDirections.NW);

        return singleDirections;
    }

    public static SimplifiedDirections GetSimplifiedDirections(this CardinalDirections cardinalDirections)
    {
#if M3_DEBUG
        CheckForSingleCardinalDirection(cardinalDirections);
#endif
        switch (cardinalDirections)
        {
            case CardinalDirections.N:
            case CardinalDirections.S:
                return SimplifiedDirections.Vertical;
                
            case CardinalDirections.NE:
            case CardinalDirections.SW:
                return SimplifiedDirections.DiagonalNEToSW;

            case CardinalDirections.E:
            case CardinalDirections.W:
                return SimplifiedDirections.Horizontal;


            case CardinalDirections.NW:
            case CardinalDirections.SE:
                return SimplifiedDirections.DiagonalNWToSE;

            default:
                throw new ArgumentOutOfRangeException(nameof(cardinalDirections), cardinalDirections, null);
        }
    }

    public static CardinalDirections GetCardinalDirectionFromCoords(Coords first, Coords second)
    {
        var deltaX = first.X - second.X;
        var deltaY = first.Y - second.Y;

        if (deltaX < 0)
        {
            return deltaY switch
            {
                < 0 => CardinalDirections.NE,
                > 0 => CardinalDirections.SE,
                _ => CardinalDirections.E
            };
        }

        if (deltaX > 0)
        {
            return deltaY switch
            {
                < 0 => CardinalDirections.NW,
                > 0 => CardinalDirections.SW,
                _ => CardinalDirections.W
            };
        }

        return deltaY switch
        {
            < 0 => CardinalDirections.N,
            > 0 => CardinalDirections.S,
            _ => CardinalDirections.None
        };
    }

    public static CardinalDirections GetCardinalDirectionInSameLineFromCoords(Coords first, Coords second)
    {
        var deltaX = first.X - second.X;
        var deltaY = first.Y - second.Y;

        if (deltaX != 0 && deltaY != 0)
            throw new Exception("[CardinalDirections]: Cells are not on the same line " + first + " and " + second);

        if (deltaX < 0)
        {
            return CardinalDirections.E;
        }

        if (deltaX > 0)
        {
            return CardinalDirections.W;
        }

        if (deltaY < 0)
        {
            return CardinalDirections.N;
        }

        return CardinalDirections.S;
    }

    public static bool GetDistanceAndDirrection(Coords firstCoords, Coords secondCoords, out CardinalDirections direction, out int distance)
    {
        var deltaX = firstCoords.X - secondCoords.X;
        var deltaY = firstCoords.Y - secondCoords.Y;

        if (deltaX == 0 && deltaY == 0)
        {
            direction = CardinalDirections.None;
            distance = 0;
            return false;
        }

        if (deltaX < 0)
        {
            if (deltaY != 0)
            {
                direction = CardinalDirections.None;
                distance = 0;
                return false;
            }

            distance = Mathf.Abs(deltaX) + Mathf.Abs(deltaY);
            direction = CardinalDirections.E;
            return true;
        }

        if (deltaX > 0)
        {
            if (deltaY != 0)
            {
                direction = CardinalDirections.None;
                distance = 0;
                return false;
            }

            distance = Mathf.Abs(deltaX) + Mathf.Abs(deltaY);
            direction = CardinalDirections.W;
            return true;
        }

        if (deltaY < 0)
        {
            distance = Mathf.Abs(deltaX) + Mathf.Abs(deltaY);
            direction = CardinalDirections.N;
            return true;
        }

        if (deltaY > 0)
        {
            distance = Mathf.Abs(deltaX) + Mathf.Abs(deltaY);
            direction = CardinalDirections.S;
            return true;
        }

        direction = CardinalDirections.None;
        distance = 0;
        return false;
    }
    
    public static List<Coords> GetCoordsInDirection(int startX, int startY, CardinalDirections direction, Grid grid)
    {
        var output = new List<Coords>();
            
        direction.ToXY(out var x, out var y);
            
        var currX = startX;
        var currY = startY;
            
        while (0 <= currX && currX < grid.Width && 0 <= currY && currY < grid.Height)
        {
            output.Add(new Coords(currX, currY));
            currX += x;
            currY += y;
        }
            
        return output;
    }

    public static void PopulateNeighborCoords(Coords[] output, int x, int y)
    {
        output[0] = new Coords(x, y - 1); // bottom
        output[1] = new Coords(x - 1, y - 1); // bottom left
        output[2] = new Coords(x - 1, y); // left
        output[3] = new Coords(x - 1, y + 1); // top left
        output[4] = new Coords(x, y + 1); // top
        output[5] = new Coords(x + 1, y + 1); // top right
        output[6] = new Coords(x + 1, y); // right
        output[7] = new Coords(x + 1, y - 1); // bottom right
    }
    
    public static (bool bottom, bool left, bool top, bool right) GetCardinalFlags(bool[] flags)
    {
        return (flags[0], flags[2], flags[4], flags[6]);
    }
}

public enum SimplifiedDirections
{
    Horizontal,
    Vertical,
    DiagonalNEToSW,
    DiagonalNWToSE
}
