using BBB.Core;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    public class AnimalReleaseEffect : ActivePoolItem
    {
        private Vector3 _cachedScale;

        private SkeletonGraphicController _sk;

        public void Setup(TileResourceSelector trSelector)
        {
#if UNITY_EDITOR
            if (_sk != null
                && _sk.gameObject.name.Contains(trSelector.SelectedAnimalPrefab.name))
            {
                Destroy(_sk.gameObject);
                _sk = null;
            }
#endif

            var animalPrefab = trSelector.SelectedAnimalPrefab;
            if (_sk?.gameObject.name != animalPrefab.name)
            {
                if (_sk != null)
                {
                    Destroy(_sk.gameObject);
                }
                UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[{animalPrefab.name}]");
                var go = Instantiate(animalPrefab, transform);
                UnityEngine.Profiling.Profiler.EndSample();
                _sk = go.GetComponent<SkeletonGraphicController>();
                _sk.gameObject.name = animalPrefab.name;
                _sk.PlayAnimation("FinalFlying", true);
            }
        }

        public override void OnSpawn()
        {
            _cachedScale = transform.localScale;
            base.OnSpawn();

            if (_sk != null)
                _sk.PlayAnimation("FinalFlying", true);
        }

        public override void OnRelease()
        {
            transform.localScale = _cachedScale;
            base.OnRelease();
            if (_sk != null)
                _sk.Reset();
        }
    }
}