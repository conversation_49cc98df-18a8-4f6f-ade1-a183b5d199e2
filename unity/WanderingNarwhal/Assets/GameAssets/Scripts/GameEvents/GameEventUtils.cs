using System;
using System.Collections.Generic;
using Core.Configs;
using FBConfig;
using UnityEngine;

namespace BBB
{
    public static class GameEventUtils
    {
        public static IComparableEvent GetHighestPriorityEvent(Predicate<IComparableEvent> eventPredicate,
            IEnumerable<IComparableEvent> events)
        {
            IComparableEvent highestPriorityEvent = null;
            foreach (var gameEvent in events)
            {
                if (!eventPredicate(gameEvent)) continue;
                
                if (highestPriorityEvent == null || CompareEvents(gameEvent, highestPriorityEvent) < 0)
                {
                    highestPriorityEvent = gameEvent;
                }
            }
            return highestPriorityEvent;
        }
        
        private static int CompareEvents(IComparableEvent a, IComparableEvent b)
        {
            int comparisonValue = a.CompareTo(b);
            //in rare cases comparison can be equal, in this case we compare by uid to make sure order will be stable
            if (comparisonValue == 0)
            {
                comparisonValue = string.Compare(a.Uid, b.Uid, StringComparison.Ordinal);
            }
            return comparisonValue;
        }
        
        public static int GetScoreMultiplier(int currentWinStreak, Func<int, DictIntInt?> scoreMultiplier, int ScoreMultiplierLength)
        {
            if (ScoreMultiplierLength == 0) return 1;

            var closestLowerWinCount = int.MinValue;
            var minWinCount = int.MaxValue;

            for (int i = 0; i < ScoreMultiplierLength; i++)
            {
                var dict = scoreMultiplier(i).Value;
                var winCount = dict.Key;
                if (winCount == currentWinStreak)
                {
                    return dict.Value; // Return immediately if we find an exact match
                }

                if (winCount < currentWinStreak && winCount > closestLowerWinCount)
                {
                    closestLowerWinCount = i;
                }
                
                if (winCount < minWinCount)
                {
                    minWinCount = i;
                }
            }

            if (closestLowerWinCount != int.MinValue)
            {
                return scoreMultiplier(closestLowerWinCount).Value.Value;
            }

            return scoreMultiplier(minWinCount).Value.Value;
        }

        
        public static int GetLowestMultiplier(Func<int, DictIntInt?> scoreMultiplier, int ScoreMultiplierLength)
        {
            if (ScoreMultiplierLength == 0)
            {
                return 1;
            }

            var minKey = int.MaxValue;
            for (int i = 0; i < ScoreMultiplierLength; i++)
            {
                var dict = scoreMultiplier(i).Value;
                var key = dict.Key;
                if (key < minKey)
                {
                    minKey = key;
                }
            }

            return scoreMultiplier(minKey).Value.Value;
        }


        public static int GetRewardMultiplier(this IDictionary<string, GameEventConfig> gameEventConfig, string uid)
        {
            if (gameEventConfig != null && gameEventConfig.TryGetValue(uid, out var config))
                return Mathf.Max(1, config.RewardMultiplier);

            return 1;
        }
        
        public static int GetLiveOpsAnalyticsInterval(IConfig config, string uid)
        {
            var systemConfig = config?.TryGetDefaultFromDictionary<FBConfig.SystemConfig>();
            if (systemConfig != null && !systemConfig.Value.IsNull())
            {
                var dictionary = FlatBufferHelper.ToDict(systemConfig.Value.LiveopsAnalytics,
                    systemConfig.Value.LiveopsAnalyticsLength);
                
                if (dictionary.TryGetValue(uid, out var gameEventSpecificValue))
                {
                    return gameEventSpecificValue;
                }

                if (dictionary.TryGetValue("default", out var defaultValue))
                {
                    return defaultValue;
                }
            }

            return 0;
        }
    }
}