using System;
using System.Collections.Generic;
using System.Text;
using BBB.Core;
using BBB.Core.AssetBundles;
using BBB.Core.Crash;
using BBB.Core.ResourcesManager;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Core.AssetBundles;
using UnityEngine;
using UnityEngine.U2D;
using Object = UnityEngine.Object;

namespace BBB
{
    /// <summary>
    /// each game event has a corresponding object of this class intended 
    /// </summary>
    public class GameEventResourcePack
    {
        private readonly List<IDisposable> _disposableResources = new ();

        private readonly HashSet<string> _genericEventAssetsStartedLoading = new ();

        private readonly Dictionary<string, Object> _genericEventAssets = new ();
        private SpriteAtlas _atlas;
        private string _loadedSpriteAtlasName;

        private readonly IGameEventProvider _gameEventProvider;
        private readonly IAssetsManager _assetManager;
        private readonly IBundleManager _bundleManager;

        /// <summary>
        /// names of generic assets that should be loaded for event
        /// for each particular event we add prefix of event uid to every name of the
        /// resource pack asset
        /// </summary>
        private static readonly IEnumerable<string> GenericNames = new List<string>
        {
            GameEventResKeys.SpriteAsset,
            GameEventResKeys.VictoryPrefab,
            GameEventResKeys.Silhouette,
            GameEventResKeys.MainAnimatedComposition,
            GameEventResKeys.InfoLayoutMain,
            GameEventResKeys.GameEventSettings,
            GameEventResKeys.RaceGameEventSettings,
            GameEventResKeys.RoyaleGameEventSettings,
            GameEventResKeys.TeamCoopGameEventSettings,
            GameEventResKeys.HudIconSpineAnim,
            GameEventResKeys.LeaderboardRewardPrefab,
            GameEventResKeys.ScreenAnimatedComposition,
            GameEventResKeys.FennecSpine,
            GameEventResKeys.IntroInfoBubble,
            GameEventResKeys.FennecInfo,
            GameEventResKeys.MainObject,
            GameEventResKeys.GameEventVideo,
        };
        
        private static readonly IEnumerable<string> MilestoneGenericNames = new List<string>
        {
            GameEventResKeys.MilestoneBanner,
            GameEventResKeys.EligibilityMilestoneBanner,
            GameEventResKeys.EmailMilestoneBanner
        };

        public string EventResourceId { get; }

        public GameEventResourcePack(string eventResourceId, IGameEventProvider gameEventProvider, IAssetsManager assetsManager, IBundleManager bundleManager)
        {
            EventResourceId = eventResourceId;
            _gameEventProvider = gameEventProvider;
            _assetManager = assetsManager;
            _bundleManager = bundleManager;
        }

        public bool IsLoading { get; private set; }
        public float Progress { get; private set; }

        public void DisposeAll()
        {
            DisposeAll_Internal();
        }

        private void DisposeAll_Internal()
        {
            _atlas = null;
            _loadedSpriteAtlasName = null;
            
            foreach (var loadedAsset in _disposableResources)
            {
                loadedAsset.Dispose();
            }

            _disposableResources.Clear();
            _genericEventAssetsStartedLoading.Clear();
            _genericEventAssets.Clear();
        }

        private bool ShouldKeepResourceLoaded()
        {
            var gameEvents = _gameEventProvider.GetGameEventsWithResource(EventResourceId);

            foreach (var gameEvent in gameEvents)
            {
                if (gameEvent != null && gameEvent.ShouldKeepResourceLoaded())
                {
                    return true;
                }
            }

            return false;
        }


        /// <summary>
        /// this method is checking game event status
        /// and starts reload or dispose if the status requires it
        /// </summary>
        public async UniTask TryReloadAsync()
        {
            if (!ShouldKeepResourceLoaded())
            {
                IsLoading = false;
                Progress  = 1f;
                DisposeAll_Internal();
                return;
            }

            IsLoading = true;
            Progress = 0f;

            var sb = new StringBuilder();
            var rawTasks = new List<UniTask>();
            foreach (var assetKey in GenericNames)
            {
                sb.Clear()
                    .Append(EventResourceId)
                    .Append('_')
                    .Append(assetKey);
                var addressableName = sb.ToString();

                if (!_bundleManager.ContainsAsset(addressableName) || _genericEventAssets.ContainsKey(assetKey) || !_genericEventAssetsStartedLoading.Add(assetKey))
                    continue;

                rawTasks.Add(LoadResourceAsync<Object>(addressableName).ContinueWith(obj => { _genericEventAssets[assetKey] = obj; }));
            }

            var gameEvents = new List<IGameEventWithResources>(_gameEventProvider.GetGameEventsWithResource(EventResourceId));
            foreach (var gameEvent in gameEvents)
            {
                if (gameEvent == null || !gameEvent.ShouldKeepResourceLoaded() ||
                    gameEvent is not IGameEventWithMilestones eventWithMilestones)
                    continue;
                    
                var totalMilestones = eventWithMilestones.GetLastMilestoneIndex() + 1;
                foreach (var assetName in MilestoneGenericNames)
                {
                    const string underLine = "_";
                    for (var i = 0; i <= totalMilestones; i++)
                    {
                        var addressableName = sb.Clear()
                            .Append(EventResourceId)
                            .Append(underLine)
                            .Append(assetName)
                            .Append(underLine)
                            .Append(i)
                            .ToString();

                        if (!_bundleManager.ContainsAsset(addressableName)) continue;

                        var milestoneAssetName = $"{assetName}_{i}";
                        if (_genericEventAssets.ContainsKey(milestoneAssetName)
                            || !_genericEventAssetsStartedLoading.Add(milestoneAssetName)) continue;

                        rawTasks.Add(
                            LoadResourceAsync<Object>(addressableName)
                                .ContinueWith(result =>
                                {
                                    _genericEventAssets.Add(milestoneAssetName, result);
                                })
                        );
                    }
                }
            }

            var atlasName = $"{EventResourceId}_{GameEventResKeys.Atlas}";

            if (_bundleManager.ContainsAsset(atlasName) && atlasName != _loadedSpriteAtlasName)
            {
                rawTasks.Add(
                    LoadResourceAsync<SpriteAtlas>(atlasName)
                        .ContinueWith(atl =>
                        {
                            if (!atl)
                            {
                                BDebug.LogError(LogCat.Resources, $"GameEventResourcePack sprite atlas is not found for event {EventResourceId}");   
                            }

                            _atlas = atl;
                            _loadedSpriteAtlasName = atlasName;
                        })
                );
            }

            if (rawTasks.Count == 0)
            {
                IsLoading = false;
                Progress = 1f;
                return;
            }

            var total = rawTasks.Count;
            var done = 0;
            var loadTasks = new List<UniTask>(total);
            foreach (var task in rawTasks)
            {
                loadTasks.Add(
                    TrackProgress(task, () =>
                    {
                        done++;
                        Progress = (float)done / total;
                    })
                );
            }

            try
            {
                await UniTask.WhenAll(loadTasks);
                IsLoading = false;
                Progress = 1f;
            }
            catch (Exception ex)
            {
                IsLoading = false;
                ProcessRejectionException(ex);
            }
        }
        
        private async UniTask<T> LoadResourceAsync<T>(string resourceName) where T : Object
        {
            var wrapper = await _assetManager.LoadAsync<T>(resourceName, AssetLoadPriority.Immediately);
            var resource = wrapper?.Get();
            if (resource == null)
                throw new Exception($"Resource {resourceName} not found");

            _disposableResources.Add(wrapper);
            return resource;
        }
        
        private async UniTask TrackProgress(UniTask task, Action onComplete)
        {
            await task;
            onComplete();
        }

        private static void ProcessRejectionException(Exception ex)
        {
            CrashLoggerService.Log("GameEventResourcePack load failed:");
            BDebug.LogError(LogCat.Resources, ex.ToString());
        }

        public T GetGenericAsset<T>(string key) where T : Object
        {
            if (_genericEventAssets.TryGetValue(key, out var asset))
                return (T) asset;

            BDebug.LogWarning(LogCat.Resources, $"Resource {key} not found in game event asset pack {EventResourceId}");
            return null;
        }

        public bool AllAssetsLoaded()
        {
            if (_atlas == null)
            {
                Debug.LogWarning("Game Event Resources: Atlas is not loaded for " + EventResourceId);
            }

            if (_genericEventAssetsStartedLoading.Count == 0)
            {
                Debug.LogWarning("Game Event Resources: _genericEventAssetsStartedLoading is empty for event " + EventResourceId);
            }

            foreach (var assetName in _genericEventAssetsStartedLoading)
            {
                if (!_genericEventAssets.ContainsKey(assetName))
                {
                    Debug.LogWarning($"Game Event Resources: Asset {assetName} not found for event " + EventResourceId);
                }
            }
            
            if (_atlas == null)
            {
                return false;
            }
            
            foreach (var name in _genericEventAssetsStartedLoading)
            {
                if (!_genericEventAssets.ContainsKey(name))
                {
                    return false;
                }
            }

            return true;
        }


        public Sprite GetSprite(string key)
        {
            if (_atlas == null)
            {
                Debug.LogWarning($"GameEventResourcePack sprite atlas is not found for event {EventResourceId}");
                return null;
            } 

            var sprite = _atlas.GetSprite(EventResourceId + "_" + key);

            if (sprite)
                return sprite;

            BDebug.LogWarning(LogCat.Resources, $"Sprite {key} not found in game event asset pack {EventResourceId}");
            return null;
        }
    }
}