using System.Collections.Generic;
using GameAssets.Scripts.GameEvents.RoyaleEvents.UI;
using UnityEngine;

namespace BBB
{
    public class RoyaleEventBackgroundObject : BbbMonoBehaviour
    {
        [SerializeField] private List<RoyaleBalloonMovableObject> _balloonMovableObjects;

        public IEnumerable<RoyaleBalloonMovableObject> GetBalloonMovableObjects()
        {
            return _balloonMovableObjects;
        }
    }
}