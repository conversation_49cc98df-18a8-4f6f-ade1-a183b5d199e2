using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.Core.AssetBundles;
using BBB.Core.Crash;
using BBB.Core.ResourcesManager;
using BBB.DI;
using BBB.RaceEvents;
using BBB.TeamEvents;
using Beebopbee.Core.Extensions;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.Core.AssetBundles;
using UnityEngine;
using Object = UnityEngine.Object;

namespace BBB
{
    public interface IGameEventResourceManager
    {
        T GetGenericAsset<T>(string eventUid, string key) where T : Object;
        UniTask<Sprite> GetSpriteAsync(string eventUid, string key);
        UniTask SetupGameEvents(IGameEventProvider gameEventProvider, IEnumerable<GameEventBase> gameEvents);
        void SetupRaceEvents(IGameEventProvider gameEventProvider, List<RaceEvent> eventsList);
        void SetupRoyaleEvents(IGameEventProvider gameEventProvider, List<RoyaleEvent> eventsList);
        void SetupTeamCoopEvents(IGameEventProvider gameEventProvider, List<TeamEvent> eventsList);
        GameEventSettings GetSettings(string eventUid);
        void Clear();
        bool AreEventBundlesLoadedFor(string eventUid);
        bool AllAssetsAvailable(string eventUid);
        HashSet<string> GetBundleNamesForEvent(string eventUid, string locationUid = null);
        UniTask TryReloadPackAsync(string resourceId, ScreenType screenType, Action<bool> callback = null);
    }

    /// <summary>
    /// This class is responsible for managing game event resources depending on current game event states
    /// If any game event has status that requires resource loading or disposing this
    /// object will know about it and do the loading process under the screen transition curtain
    /// </summary>
    public class GameEventResourceManager : IGameEventResourceManager, IContextInitializable,
        ISpecializedResourceManager
    {
        //list of screen types which should do resource processing when they are being opened
        private readonly List<ScreenType> _screenIdsToReloadGameEventResourcesOn = new List<ScreenType>
        {
            ScreenType.EpisodeScreen,
            ScreenType.SideMapScreen
        };

        private IBundleInfoProvider _bundleInfoProvider;
        private IAssetsManager _assetsManager;
        private IBundleManager _bundleManager;

        private readonly Dictionary<string, List<string>> _bundleNamesForEvent = new();

        //resource packs for game events, one game event is mapped to one resource pack
        private readonly Dictionary<string, GameEventResourcePack> _packsDict = new();
        private readonly HashSet<string> _loadedEvents = new();
        private bool _failedToLoad;
        private LocationBundleManager _locationBundleManager;

        public bool IsRequired => false;
        public void Reset()
        {
            Clear();
        }

        void IContextInitializable.InitializeByContext(IContext context)
        {
            _assetsManager = context.Resolve<IAssetsManager>();
            _bundleManager = context.Resolve<IBundleManager>();
            _bundleInfoProvider = context.Resolve<IBundleInfoProvider>();
            _locationBundleManager = context.Resolve<LocationBundleManager>();
        }

        public async UniTask SetupGameEvents(IGameEventProvider gameEventProvider, IEnumerable<GameEventBase> gameEvents)
        {
            foreach (var gameEvent in gameEvents)
            {
                CacheBundleNamesForEvent(gameEvent.Uid);
                var resourceId = gameEvent.EventResourceId;

                if (_packsDict.ContainsKey(resourceId) || gameEvent.HasNoResources())
                    continue;
                
                _packsDict.Add(resourceId, new GameEventResourcePack(resourceId, gameEventProvider, _assetsManager, _bundleManager));
            }

            ISpecializedResourceManager specializedResourceManager = this;
            if (ConnectivityStatusManager.ConnectivityReachable && !specializedResourceManager.IsLoading(string.Empty, string.Empty))
            {
                await specializedResourceManager.TryReloadAsync(ScreenType.EpisodeScreen.ToString(), ScreenType.EpisodeScreen);
            }
        }

        public void SetupRaceEvents(IGameEventProvider gameEventProvider, List<RaceEvent> raceEvents)
        {
            foreach (var raceEvent in raceEvents)
            {
                CacheBundleNamesForEvent(raceEvent.Uid);
                var resourceId = raceEvent.EventResourceId;

                if (_packsDict.ContainsKey(resourceId))
                    continue;
                
                _packsDict.Add(resourceId, new GameEventResourcePack(resourceId, gameEventProvider, _assetsManager, _bundleManager));
            }
        }

        public void SetupRoyaleEvents(IGameEventProvider gameEventProvider, List<RoyaleEvent> royaleEvents)
        {
            foreach (var ev in royaleEvents)
            {
                CacheBundleNamesForEvent(ev.Uid);
                var resourceId = ev.EventResourceId;

                if (_packsDict.ContainsKey(resourceId))
                    continue;

                _packsDict.Add(resourceId, new GameEventResourcePack(ev.Uid, gameEventProvider, _assetsManager, _bundleManager));
            }
        }

        public void SetupTeamCoopEvents(IGameEventProvider gameEventProvider, List<TeamEvent> eventsList)
        {
            foreach (var ev in eventsList)
            {
                CacheBundleNamesForEvent(ev.Uid);
                var resourceId = ev.EventResourceId;

                if (_packsDict.ContainsKey(resourceId))
                    continue;

                _packsDict.Add(resourceId, new GameEventResourcePack(ev.Uid, gameEventProvider, _assetsManager, _bundleManager));
            }
        }

        public void Clear()
        {
            if (_packsDict.IsNullOrEmpty())
                return;

            foreach (var pack in _packsDict.Values)
            {
                pack.DisposeAll();
            }

            _packsDict.Clear();
        }

        /// <summary>
        /// This method iterates through every resource pack and asks it to try to reload (or dispose)
        /// game event resources if it is required by current game event state
        /// game event state processing should be called during screen transition before this method
        /// to make sure processing and reloading gonna happen during the same transition if required
        /// </summary>
        async UniTask ISpecializedResourceManager.TryReloadAsync(string screenBeingLoaded, ScreenType screenType)
        {
            _failedToLoad = false;

            if (!_screenIdsToReloadGameEventResourcesOn.Contains(screenType))
                return;

            var tasks = new List<UniTask>();
            foreach (var pack in _packsDict.Values)
            {
                var resourceId = pack.EventResourceId;
                if (ConnectivityStatusManager.ConnectivityReachable || AreEventBundlesLoadedFor(resourceId))
                {
                    tasks.Add(ReloadPackInternal(pack, resourceId));
                }
            }

            await UniTask.WhenAll(tasks);

            if (_failedToLoad)
            {
                CrashLoggerService.Log(
                    "GameEventResourceManager failed to load game event resources, Pack Dictionary Keys: " +
                    string.Join(", ", _packsDict.Keys));
            }
        }
        
        private async UniTask ReloadPackInternal(GameEventResourcePack pack, string resourceId)
        {
            try
            {
                await pack.TryReloadAsync();
            }
            catch (Exception ex)
            {
                _failedToLoad = true;
                BDebug.LogError(LogCat.Resources,$"Exception in GameEventResourceManager: {ex.Message} Event: {resourceId} All Assets Loaded {pack.AllAssetsLoaded()}");
            }
        }

        public async UniTask TryReloadPackAsync(string resourceId, ScreenType screenType, Action<bool> callback = null)
        {
            if (!ConnectivityStatusManager.ConnectivityReachable
                || !_screenIdsToReloadGameEventResourcesOn.Contains(screenType)
                || !_packsDict.TryGetValue(resourceId, out var pack)
                || !AreEventBundlesLoadedFor(resourceId))
            {
                callback?.Invoke(false);
                return;
            }

            try
            {
                await pack.TryReloadAsync();
                callback?.Invoke(pack.AllAssetsLoaded());
            }
            catch (Exception ex)
            {
                BDebug.LogError(LogCat.Resources,
                    $"Exception in GameEventResourceManager when loading {resourceId}: {ex.Message}\n{ex.StackTrace}");
                callback?.Invoke(false);
            }
        }

        //used to make screen transition wait for this process to finish
        bool ISpecializedResourceManager.IsLoading(string screenName, string prevScreenName)
        {
            foreach (var pack in _packsDict.Values)
            {
                if (pack.IsLoading)
                {
                    return true;
                }
            }

            return false;
        }

        float ISpecializedResourceManager.Progress()
        {
            var totalProgress = 0f;
            foreach (var pack in _packsDict.Values)
            {
                totalProgress += pack.IsLoading ? pack.Progress : 1f;
            }
            return totalProgress / _packsDict.Count;
        }

        public bool AreBundlesLoaded(string screenBeingLoaded, ScreenType screenType)
        {
            if (_screenIdsToReloadGameEventResourcesOn.Contains(screenType))
            {
                foreach (var pack in _packsDict.Values)
                {
                    if (pack.AllAssetsLoaded())
                        continue;

                    var resourceId = pack.EventResourceId;
                    if (!AreEventBundlesLoadedFor(resourceId))
                        return false;
                }
            }

            return true;
        }

        bool ISpecializedResourceManager.HasFailed()
        {
            return _failedToLoad;
        }

        void ISpecializedResourceManager.ResetFailure()
        {
            _failedToLoad = false;
        }

        /// <summary>
        /// centralized method to get generic resources - prefabs, scriptableobjects
        /// in this context any asset is called generic except sprites 
        /// </summary>
        public T GetGenericAsset<T>(string eventUid, string key) where T : Object
        {
            var resourceUid = eventUid.RemoveDigits();
            if (_packsDict.TryGetValue(resourceUid, out var pack))
            {
                var asset = pack.GetGenericAsset<T>(key);
                return asset;
            }

            {
                BDebug.LogError(LogCat.Resources,
                    $"Resource pack for event {resourceUid} not found while asking for {key}");
                return null;
            }
        }

        public async UniTask<Sprite> GetSpriteAsync(string eventUid, string key)
        {
            var resourceUid = eventUid.RemoveDigits();

            Sprite sprite;
            if (_packsDict.TryGetValue(resourceUid, out var pack) && pack.AllAssetsLoaded())
            {
                sprite = pack.GetSprite(key);
                if (sprite != null)
                    return sprite;
            }

            var spriteName = $"{resourceUid}_{key}";
            sprite = await _assetsManager.LoadSpriteAsync(spriteName, false);
            if (sprite != null)
                return sprite;
            
            return null;
        }

        /// <summary>
        /// Returns overriding settings which can be null
        /// </summary>
        public GameEventSettings GetSettings(string eventUid)
        {
            if (string.IsNullOrEmpty(eventUid))
                return null;

            var resourceUid = eventUid.RemoveDigits();
            return _packsDict.TryGetValue(resourceUid, out var pack) ? pack.GetGenericAsset<GameEventSettings>(GameEventResKeys.GameEventSettings) : null;
        }
        
        private void CacheBundleNamesForEvent(string eventUid)
        {
            _ = GetBundleNamesListFor(eventUid);
        }

        private List<string> GetBundleNamesListFor(string eventUid)
        {
            if (_bundleNamesForEvent.TryGetValue(eventUid, out var bundleNamesList))
            {
                return bundleNamesList;
            }

            bundleNamesList = new List<string>();

            var bundleNamesForCity = _bundleInfoProvider.GetBundleNamesFor(str => str.StartsWith($"downloadable/events/{eventUid}/"));

            foreach (var bundleName in bundleNamesForCity)
            {
                bundleNamesList.Add(bundleName);
            }

            _bundleNamesForEvent.Add(eventUid, bundleNamesList);

            return bundleNamesList;
        }

        public HashSet<string> GetBundleNamesForEvent(string eventUid, string locationUid)
        {
            var bundles = new HashSet<string>(GetBundleNamesListFor(eventUid));
            if (!locationUid.IsNullOrEmpty())
            {
                bundles.UnionWith(_locationBundleManager.GetBundleNamesListFor(locationUid));
            }
            return bundles;
        }

        public bool AreEventBundlesLoadedFor(string eventUid)
        {
            //this hash set check required to optimize subsequent calls:
            //if bundles are loaded, this fact stays true during current session
            if (_loadedEvents.Contains(eventUid))
                return true;

            var bundleNamesList = GetBundleNamesListFor(eventUid);
            var allBundlesLoaded = bundleNamesList.Count > 0;
            foreach (var bundleName in bundleNamesList)
            {
                if (_bundleManager.IsBundleLoadedAnywhere(bundleName)) continue;

                Debug.LogWarning($"Game Event Resources: {bundleName} is not loaded for event " + eventUid);

                allBundlesLoaded = false;
                break;
            }

            if (allBundlesLoaded)
            {
                _loadedEvents.Add(eventUid);
            }

            return allBundlesLoaded;
        }

        public bool AllAssetsAvailable(string eventUid)
        {
            var resourceUid = eventUid.RemoveDigits();
            return AreEventBundlesLoadedFor(resourceUid) && _packsDict.TryGetValue(resourceUid, out var pack) &&
                   pack.AllAssetsLoaded();
        }

        public void DisposeForScreen(ScreenType screenType, ScreenType currentScreenType)
        {
            // TODO: Dispose assets depending on screen type
        }
    }
}