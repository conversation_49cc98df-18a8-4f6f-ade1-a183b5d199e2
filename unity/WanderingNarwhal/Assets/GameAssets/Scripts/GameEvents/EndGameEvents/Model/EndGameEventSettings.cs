using BBB;
using BBB.GameAssets.Scripts.Player;
using GameAssets.Scripts.Theme;
using UnityEngine;

namespace GameAssets.Scripts.GameEvents.EndGameEvents.Model
{
    [CreateAssetMenu(menuName = "BBB/Events/EndGameEventSettings", fileName = "_game_event_settings", order = 1)]
    public class EndGameEventSettings : GameEventSettings
    {
        [field: SerializeField]
        public SideMapLevelsConfig[] LevelsConfigs { get; private set; }
        
        [field: SerializeField]
        public ThemeDefinition Theme { get; private set; }

        public SideMapLevelsConfig GetLevelConfig(string configName)
        {
            if (string.IsNullOrEmpty(configName))
            {
                return null;
            }

            foreach (var config in LevelsConfigs)
            {
                if (config.name == configName)
                {
                    return config;
                }
            }

            return null;
        }

    }
}