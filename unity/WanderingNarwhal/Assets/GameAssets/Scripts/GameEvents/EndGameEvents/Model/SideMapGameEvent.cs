using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.DI;
using BBB.GameAssets.Scripts.Player;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.GameEvents.EndGameEvents.Model;
using FBConfig;
using PBGame;

namespace BBB.EndGameEvents
{
    public class SideMapGameEvent : CompetitionGameEvent
    {
        private const string NOTIFY_TITLE_LOC = "GE_ENDGAMEEVENT_START_NOTIFICATION_TITLE";
        private const string NOTIFY_MESSAGE_LOC = "GE_ENDGAMEEVENT_START_NOTIFICATION_MESSAGE";
        
        //do not use this
        public override float ProgressRatio => 0f;
        public override float ProgressRatioLastDelta =>  0f;
        
        private readonly IGameEventResourceManager _resourceManager;
        private readonly string _levelsConfigName;

        public string LocationUid => _levelsConfigName;
        
        public int CurrentRound => State?.CurrentRound ?? 0;

        private int[] _levelIndexes;
        private LevelState _currentLevelState;

        public override GameEventGameplayType GameplayType => GameEventGameplayType.SideMap;
        public virtual ScreenType HomeScreen => ScreenType.SideMapScreen;
        public virtual ScreenType LevelScreen => ScreenType.SideMapLevelScreen;

        public LevelState CurrentLevelState
        {
            get
            {
                if (_currentLevelState == null)
                {
                    _currentLevelState = new LevelState { Stage = 0, SourceUid = CurrentLevel.LevelUid };
                }
                return _currentLevelState;
            }
        }

        public SideMapLevelConfig CurrentLevel
        {
            get
            {
                var levels = GetLevels();
                if (levels == null)
                {
                    return default;
                }
                int totalLevels = levels.Length;
                if (CurrentRound < totalLevels)
                    return levels[CurrentRound];
                
                if (_levelIndexes == null)
                    InitLevelIndexes();

                int index = (CurrentRound - totalLevels) % _levelIndexes.Length;
                return levels[_levelIndexes[index]];
            }
        }
        
        public override bool ShouldQualificationAnnouncementBeAutoShown => false;
        
        public override bool ShouldEndAnnouncementBeAutoShown => false;

        protected override bool IsQualified() => false; //side map game events don't have qualificationon

        private SideMapLevelConfig[] GetLevels()
        {
            var settings = (EndGameEventSettings)_resourceManager.GetSettings(Uid);

            if (settings == null)
            {
                return null;
            }
            
            var levelsConfig = settings.GetLevelConfig(_levelsConfigName);
            if (levelsConfig != null)
                return levelsConfig.Levels;
            
            if (settings.LevelsConfigs.Length > 0) //use config based on the start day number
            {
                int startEventDay = GetLastStartTime().DayOfYear;
                int index = (startEventDay / EventConfig.DurationInDays) % settings.LevelsConfigs.Length;
                return settings.LevelsConfigs[index].Levels;
            }
            return null;
        }

        public SideMapGameEvent(GameEventConfig eventConfig, IContext context, IGlobalGameEventDataProvider dataProvider, IGlobalGameEventChangesNotifier changeNotifier, SideMapGameEvent oldEvent = null) : 
            base(eventConfig, context, dataProvider, changeNotifier)
        {
            _resourceManager = context.Resolve<IGameEventResourceManager>();
            _levelsConfigName = eventConfig.LevelsConfigName;
            if (IsLaunched())
            {
                CState.QualifiedToJoin = true;
                //update current round from the previous event
                if (oldEvent != null && oldEvent.CurrentRound > CurrentRound)
                {
                    State.CurrentRound = oldEvent.CurrentRound;
                }
            }
        }

        private void InitLevelIndexes()
        {
            var levels = GetLevels();
            int totalLevels = levels.Length;
            _levelIndexes = new int[totalLevels * 5];
            int seed = EventConfig.AbsoluteStartTime.Value.Year ^ (EventConfig.AbsoluteStartTime.Value.Month << 27);
            Random rnd = new Random(seed);
            int currentStep = rnd.Next(1, totalLevels);
            int lastIndex = totalLevels - 1;
            for (int i = 0; i < _levelIndexes.Length; ++i)
            {
                _levelIndexes[i] = (lastIndex + currentStep) % totalLevels;
                if (i % totalLevels == 0)
                    currentStep = rnd.Next(1, totalLevels);
                lastIndex = _levelIndexes[i];
            }
        }

        protected override void HandleLaunch()
        {
            base.HandleLaunch();
            TriggerLiveOpsEvent(LiveOpsAnalytics.AnalyticNames.Start);
        }

        public override GameEventRewardLocParams GetCurrentRewardLocalizationParams()
        {
            var result = new GameEventRewardLocParams();
            result.HeaderLocKey = null;
            result.TitleLocKey = null;
            result.LocParams = null;
            return result;
        }

        public void CompleteRound()
        {
            var status = StatusInternal;
            
            _currentLevelState = null;

            if (status != GameEventStatus.Active)
            {
                BDebug.LogError(LogCat.General, $"Trying to add round to not active event {EventConfig.Uid}");
                //return;
            }

            State.CurrentRound++;
            State.CurrentRoundAttempt = 0;
        }

        public override void AddScore(int delta, bool append = false)
        {
            var status = StatusInternal;

            if (status != GameEventStatus.Active)
            {
                BDebug.LogError(LogCat.General, $"Trying to add score to not active event {EventConfig.Uid}");
                //return;
            }
            
            var prevScore = State.CurrentScore;
            int resultDelta = 0;
            if (delta > 0)
            {
                SetScore(State.CurrentScore + delta);
                
                if (State == null) return;//in case the event was released when processing state within SetScore
                
                resultDelta = State.CurrentScore - prevScore;
                LastAddedScoreToShow = append ? LastAddedScoreToShow + resultDelta : resultDelta;
            }
            State.LastAddedScoreDelta = append ? State.LastAddedScoreDelta + resultDelta : resultDelta;

            if (ShouldAutoSubmit() && delta > 0)
                EventLeaderboard.SubmitCurrentScore(null);
            
            LastRemoteDataUpdateTime = -1;
        }

        public override void DebugSetScore(int score)
        {
            State.LastAddedScoreDelta = 0;
            State.Status = (int) GameEventStatus.Active;
            SetScore(score);
            
            if (ShouldAutoSubmit() && score > 0)
                EventLeaderboard.SubmitCurrentScore(null);
            SetLastScoreShownInCurrentLeague(-1);
        }

        protected override bool CanFetchData()
        {
            return CurrentScore > 0 && (CState.CurrentSubmittedScore > 0 || !IsEnded());
        }

        public override Dictionary<string, int> ClaimReward()
        {
            var status = StatusInternal;

            switch (status)
            {
                case GameEventStatus.Ended:
                {
                    Dictionary<string, int> result = null;
                    if (CState.InfoAlreadyShown && CurrentScore > 0)
                    {
                        result = Leaderboard.GetOwnEndReward();

                        ChangeNotifier.OnAccomplishClaim(this);
                    }
                    State.Status = (int) GameEventStatus.EndRewardCollected;
                    return result ?? new Dictionary<string, int>();
                }
            }

            BDebug.LogError(LogCat.General, $"Can not claim game event {EventConfig.Uid} reward in status {status}");
            return null;
        }
        
        public override int GenerateScore(Stage stage)
        {
            return 1;
        }
        
        public override (string, string) GetDialogueHeader()
        {
            return ("DIALOG_HEADER_STORIES", DisplayName);
        }
        
        public override string GetStartNotificationTitle()
        {
            return LocalizationManager.getLocalizedText(NOTIFY_TITLE_LOC);
        }

        public override string GetStartNotificationMessage()
        {
            return LocalizationManager.getLocalizedText(NOTIFY_MESSAGE_LOC);
        }

        public override bool CanShowInScreen(ScreenType screenType)
        {
            return (screenType & ScreenType.SideMap) > 0;
        }
        
        protected override bool IsReadyToRelease()
        {
            if (IsEnded())
            {
                if (IntroductionAlreadyShown)
                {
                    if (!ConnectivityStatusManager.ConnectivityReachable) return false;
                    
                    if (EventLeaderboard.IsPlayerDataLoaded() && !EventLeaderboard.IsOwnInAnyWinningPlace() &&
                        (!EventLeaderboard.HasFailureReward() || CurrentScore == 0))
                        return true;
                }
                else
                {
                    return true;
                }
            }

            return base.IsReadyToRelease();
        }
    }
}