using System.Collections.Generic;
using BBB.Core;
using BBB.DI;
using FBConfig;

namespace BBB.EndGameEvents
{
    public class EndOfContentGameEvent : SideMapGameEvent
    {
        public override GameEventGameplayType GameplayType => GameEventGameplayType.EndOfContent;
        
        public override bool ShouldEndAnnouncementBeAutoShown => IsEnded();

        public override bool ShouldAutoShowInfo() => false;
        
        private readonly ILocationManager _locationManager;

        public EndOfContentGameEvent(GameEventConfig eventConfig, IContext context, IGlobalGameEventDataProvider dataProvider, IGlobalGameEventChangesNotifier changeNotifier, EndOfContentGameEvent gameEvent) : base(eventConfig, context, dataProvider, changeNotifier, gameEvent)
        {
            _locationManager = context.Resolve<ILocationManager>();
        }
        
        protected override bool IsReadyToRelease()
        {
            if (IsEnded())
            {
                return !IntroductionAlreadyShown || !IsReadyToQualify(); //show leaderboard for any place
            }

            return base.IsReadyToRelease();
        }

        protected override bool AvailableForHighestPassedLevel()
        {
            if (base.AvailableForHighestPassedLevel()) return true;
            
            var location = _locationManager.MainProgressionLocation;
            var mainProgressionCompleted = location.IsCompleted;
            return mainProgressionCompleted;
        }

        public override Dictionary<string, int> ClaimReward()
        {
            var status = StatusInternal;

            switch (status)
            {
                case GameEventStatus.Ended:
                {
                    Dictionary<string, int> result = null;
                    if (CState.IntroductionAlreadyShown && CurrentScore > 0)
                    {
                        result = Leaderboard.GetOwnEndReward();
                        if (Leaderboard.GetOwnSubmittedPlace() == 1)
                        {
                            PlayerManager.Player.PlayerDO.Stats.LeaguesWon++;
                            AccountManager.Profile.LeaguesWon = PlayerManager.Player.PlayerDO.Stats.LeaguesWon;
                        }

                        ChangeNotifier.OnAccomplishClaim(this);
                    }
                    State.Status = (int) GameEventStatus.EndRewardCollected;
                    return result ?? new Dictionary<string, int>();
                }
            }

            BDebug.LogError(LogCat.General, $"Can not claim game event {EventConfig.Uid} reward in status {status}");
            return null;
        }
    }
}