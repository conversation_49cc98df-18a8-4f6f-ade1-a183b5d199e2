using BebopBee;
using GameAssets.Scripts.SocialScreens.Teams;
using FBConfig;

namespace BBB
{
    /// <summary>
    /// this interface should only be used inside GameEvent class
    /// </summary>
    public interface IGlobalGameEventDataProvider : ISchedulableDataProvider
    {
        bool IsInDebugMode { get; }
        GameEventStateProxy GameEventStateProxy { get; }
        Profile OwnProfile { get; }
        ISocialManager SocialManager { get; }
        GameEventMetaConfig GetMetaConfig();
        int GetLastScoreShown(string eventUid);
        string GetOverrideGameEvent();
    }
}