using System;
using System.Collections.Generic;
using BBB.RaceEvents;
using BebopBee.Core.UI;
using Cysharp.Threading.Tasks;
using FBConfig;
using GameAssets.Scripts.Map.UI.Controllers;
using UnityEngine;

namespace BBB
{
    public interface IGameEventManager : IInterruptionTracker
    {
        event Action<GameEventBase> OnGameEventClaimedAfterAccomplish;
        event Action<GameEventBase> OnGameEventExpired;
        event Action<GameEventBase> OnGameEventLaunched;
        int TimeBeforeEventEndNotification { get; }
        HashSet<GameEventBase> LastShownEvents { get; } 
        SweepStakesGameEventConfig SweepStakesGameEventConfig { get; }
        IEnumerable<GameEventBase> GetEvents(Predicate<GameEventBase> filter);
        bool CanLabelsBeGatheredAtLevel(string levelUid);
        void ClaimRewardsFor(GameEventBase gameEvent);
        IEnumerable<(INotifiableEvents gameEvent, DateTime dateTime)> GetFutureNotifiableEvents(Func<GameEventBase, DateTime> timeSelector);
        bool IsActiveSideMap(ScreenType screenType);
        GameEventBase GetHighestPriorityEventByStatus(GameEventStatus status);
        GameEventBase GetHighestPriorityEvent(Predicate<GameEventBase> eventPredicate, bool skipResourceCheck = false);
        GameEventMatch3LabelCounters GetMatch3DataFor(string levelUid);
        GameEventBase GetCurrentSideMapEvent();
        GameEventBase GetAvailableSideMapEvent();
        void IncrementScores(string eventUid, int score);
        void ShowEvent(string eventUid, ShowMode showMode = ShowMode.Delayed);
        void ShowCompletionModal(CompetitionGameEvent eventUid, ShowMode showMode = ShowMode.Delayed);
        void ShowLeaderboardModal(CompetitionGameEvent gameEvent, ShowMode showMode = ShowMode.Delayed);
        void Process();
        GameEventBase FindGameEventByUid(string gameEventUid);
        int GetLastScoreShown(string eventUid);
        void SetLastScoreShown(string eventUid, int value);
        void SetLastRankShown(string eventUid, int value);
        void DebugSetScore(string eventUid, int score);
        void DebugReleaseEvent(string eventUid);
        void ShowInformationModal(CompetitionGameEvent gameEvent, Action<bool> closeCallback);
        void GoToNextGameEventLevel();
        string GetOverrideGameEvent();
        void SetOverrideGameEvent(string value);
        bool TryAutoShowGameEvent();
        bool ShouldShowEndAnnouncement();
        bool ShouldShowStartAnnouncement();
        bool ShouldShowQualificationAnnouncement();
        bool AnyFetchingData();
        int GetLastScoreSeenFor(string eventUid, string playerUid);
        void SetLastScoreSeenFor(string eventUid, string playerUid, int value);
        void ShowSweepstakesMilestoneRewardModal(SweepstakesGameEvent sweepstakesGameEvent,
            Transform rewardItem,
            Action onHide);

        UniTask ShowSweepstakesGameEvent(SweepstakesGameEvent sweepstakesGameEvent, ShowMode showMode = ShowMode.Delayed);

        bool IsValidScore(string scoreId, out GameEventBase gameEvent);
    }
}