using BBB.DI;
using BBB.Generic.Modal;
using GameAssets.Scripts.UI.OverlayDialog;
using TMPro;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace BBB.RaceEvents.UI
{
    public class FoodCarnivalModalViewPresenter : RaceCompetitionModalViewPresenter, IDestroyable
    {
        private const string EventPictureStringFormat = "{0}_fennec_picture";
        private const string StageInfoText = "FOODCARNIVAL_STAGE_INFO";
        
        [SerializeField] private EventPictureRenderer _eventPictureRenderer;
        [SerializeField] private GameObject _stageHolder;
        [SerializeField] private TextMeshProUGUI[] _stageNumber;
        [SerializeField] private Button _stageInfoButton;
        [SerializeField] private FoodCarnivalLeaderboardView _foodCarnivalLeaderboardView;
        [SerializeField] private TextMeshProUGUI _mainText;
        [SerializeField] private Button _continueButton;
        [SerializeField] private Button _claimButton;
        [SerializeField] private Button _startAgainButton;
        [SerializeField] private GameObject _continueButtonHolder;
        [SerializeField] private GameObject _claimButtonHolder;
        [SerializeField] private GameObject _startAgainButtonHolder;
        [FormerlySerializedAs("_speechBubbleConfig")] [SerializeField] private OverlayDialogConfig _overlayDialogConfig;
        [SerializeField] private Transform _stageInfoSpeechBubbleTransform;
        
        protected override void OnContextInitialized(IContext context)
        {
            _foodCarnivalLeaderboardView.InitWithContext(context);
            _continueButton.ReplaceOnClick(ContinueButtonHandler);
            _claimButton.ReplaceOnClick(ClaimButtonHandler);
            _startAgainButton.ReplaceOnClick(StartAgainButtonHandler);
            base.OnContextInitialized(context);
            _stageInfoButton.ReplaceOnClick(ShowStageInfoClicked);
        }
        
        public override void RefreshRemoteDataIndependent(RaceEvent raceEvent)
        {
            base.RefreshRemoteDataIndependent(raceEvent);
            _raceEventNameText.text = raceEvent.StageNameText;
            _mainText.text = string.Empty;
        }

        public override void RefreshRemoteDataDependent(RaceCompetitionViewModel viewModel)
        {
            _mainText.text = viewModel.RaceEventMainModalText;
            _foodCarnivalLeaderboardView.Refresh(viewModel, ClaimButtonCallback, ShowRewardButtonCallback);
            var (eventUid, stageName) = RaceEvent.GetRaceStageUid().SplitRaceUid();
            var prefabName = string.Format(EventPictureStringFormat, stageName);
            _eventPictureRenderer.RefreshFromConfig<RaceEventVisualConfig>(eventUid, prefabName);

            var raceStageToDisplay = RaceEvent.GetCurrentStageIndex() + 1;
            
            foreach (var stageNumber in _stageNumber)
            {
                stageNumber.text = raceStageToDisplay.ToString();
            }
            _stageHolder.SetActive(raceStageToDisplay > 0 && RaceEvent.RaceEventType == RaceEventTypes.RaceEventType.Streak);
            base.RefreshRemoteDataDependent(viewModel);
        }
        
        public override void StartTransitionToActualState(RaceCompetitionViewModel viewModel)
        {
            _foodCarnivalLeaderboardView.TryStartTransitionToActualState(viewModel);
        }
        
        public override void AutoShowRewardSpeechBubble()
        {
            _foodCarnivalLeaderboardView.AutoShowRewardSpeechBubble();
        }

        protected override void UpdateButtons()
        {
            base.UpdateButtons();
            _continueButtonHolder.SetActive(false);
            _claimButtonHolder.SetActive(false);
            _startAgainButtonHolder.SetActive(false);
            
            if (RaceEvent.IsFinished())
            {
                if (RaceEvent.IsInPrizePlace(true))
                {
                    _claimButtonHolder.SetActive(true);
                }
                else
                {
                    if (RaceEvent.GetTimeLeft().TotalSeconds <= 0)
                    {
                        _continueButtonHolder.SetActive(true);
                    }
                    else
                    {
                        _startAgainButtonHolder.SetActive(true);
                    }
                }
            }
            else
            {
                _continueButtonHolder.SetActive(true);
            }
        }

        protected override void OnHide()
        {
            _foodCarnivalLeaderboardView.Hide();
            _stageHolder.SetActive(false);
            
            var (eventUid, stageName) = RaceEvent.GetRaceStageUid().SplitRaceUid();
            var prefabName = string.Format(EventPictureStringFormat, stageName);
            _eventPictureRenderer.HideEventPicture(eventUid, prefabName);
            base.OnHide();
        }
        
        private void ShowStageInfoClicked()
        {
            var currentStageToDisplay = RaceEvent.GetCurrentStageIndex() + 1;
            var totalStages = RaceEvent.TotalRaceStages;

            _overlayDialogConfig.DisplayType = DisplayType.RewardSpeechBubble;
            _overlayDialogConfig.TextToDisplay = StageInfoText;
            _overlayDialogConfig.TextArgs = new object[] {currentStageToDisplay, totalStages};
            OverlayDialogManager.ToggleOverlayDialog(_overlayDialogConfig);
        }
    }
}