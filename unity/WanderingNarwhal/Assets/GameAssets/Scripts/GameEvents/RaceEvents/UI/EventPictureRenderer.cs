using System.Collections.Generic;
using BBB.Core;
using BBB.DI;
using BBB.UI.Core;
using UnityEngine;
using Spine.Unity;

namespace BBB.RaceEvents.UI
{
    public class EventPictureRenderer : ContextedUiBehaviour
    {
        [SerializeField] private Transform _pictureHolderTransform;
        [SerializeField] private BoneFollowerGraphic _boneFollowerGraphic;
        [SerializeField] private int _pictureSiblingIndex = 0;

        [SerializeField] private bool _followLocalScale;
        [SerializeField] private bool _followBoneRotation;
        [SerializeField] private bool _followSkeletonFlip;
        [SerializeField] private bool _followZPosition;
        [SerializeField] private bool _followXYPosition;
        [SerializeField] private string _boneToFollowName = "child";

        private IGameEventResourceManager _resourceManager;

        private readonly Dictionary<string, GameObject> _eventPictures = new();

        protected override void InitWithContextInternal(IContext context)
        {
            _resourceManager = context.Resolve<IGameEventResourceManager>();
        }

        public void Refresh(string eventUid, string prefabName)
        {
            foreach (var kvp in _eventPictures)
                kvp.Value.SetActive(false);

            var goIdentifier = eventUid + "_" + prefabName;

            if (_eventPictures.TryGetValue(goIdentifier, out var go))
            {
                go.SetActive(true);
                return;
            }

            LazyInit();
            var prefab = _resourceManager.GetGenericAsset<GameObject>(eventUid, prefabName);
            InstantiateNewEventPicture(prefab, goIdentifier);
        }

        public void RefreshFromConfig<T>(string eventUid, string prefabName, string gameEventSettingsKey = GameEventResKeys.RaceGameEventSettings)
            where T : EventVisualConfig
        {
            foreach (var kvp in _eventPictures)
                kvp.Value.SetActive(false);

            var goIdentifier = eventUid + "_" + prefabName;
            if (_eventPictures.TryGetValue(goIdentifier, out var go))
            {
                go.SetActive(true);
                return;
            }

            LazyInit();
            var eventVisualConfig = _resourceManager.GetGenericAsset<T>(eventUid, gameEventSettingsKey);
            if (eventVisualConfig == null)
            {
                BDebug.LogError(LogCat.General, $"{gameEventSettingsKey} not found for event {eventUid}");
                return;
            }

            var prefab = eventVisualConfig.FindPrefabByName(prefabName);
            if (prefab == null)
            {
                BDebug.LogError(LogCat.General, $"{prefabName} not found in {gameEventSettingsKey} for event {eventUid}");
                return;
            }

            InstantiateNewEventPicture(prefab, goIdentifier);
        }

        private void InstantiateNewEventPicture(GameObject prefab, string goIdentifier)
        {
            var newGo = Instantiate(prefab, _pictureHolderTransform);
            newGo.transform.SetSiblingIndex(_pictureSiblingIndex);
            newGo.SetActive(true);

            if (_boneFollowerGraphic != null)
            {
                _boneFollowerGraphic.skeletonGraphic = newGo.GetComponent<SkeletonGraphic>();
                _boneFollowerGraphic.followLocalScale = _followLocalScale;
                _boneFollowerGraphic.followBoneRotation = _followBoneRotation;
                _boneFollowerGraphic.followSkeletonFlip = _followSkeletonFlip;
                _boneFollowerGraphic.followZPosition = _followZPosition;
                _boneFollowerGraphic.followXYPosition = _followXYPosition;
                if (!_boneToFollowName.IsNullOrEmpty())
                {
                    _boneFollowerGraphic.boneName = _boneToFollowName;
                }
            }

            _eventPictures[goIdentifier] = newGo;
        }

        public void HideEventPicture(string eventUid, string prefabName)
        {
            var goIdentifier = eventUid + "_" + prefabName;

            if (_eventPictures.TryGetValue(goIdentifier, out var go))
            {
                go.SetActive(false);
            }
        }

        public void SetSpineAnimClipIfCan(string eventUid, string prefabName, string animationName)
        {
            var goIdentifier = $"{eventUid}_{prefabName}";
            if (_eventPictures.TryGetValue(goIdentifier, out var go))
            {
                var skeletonGraphic = go.GetComponent<SkeletonGraphic>();
                if (skeletonGraphic != null)
                {
                    skeletonGraphic.AnimationState.SetAnimation(0, animationName, true);
                }
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            _eventPictures.Clear();
        }
    }
}