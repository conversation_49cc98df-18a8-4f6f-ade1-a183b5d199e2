using BBB.DI;
using UnityEngine;
using GameAssets.Scripts.GameEvents.RaceEvents.UI;
using TMPro;
using UnityEngine.UI;

namespace BBB.RaceEvents.UI
{
    public class DiscoRushModalViewPresenter : RaceCompetitionModalViewPresenter
    {
        private const string EventPictureStringFormat = "{0}_leaderboard_event_picture";
        
        [SerializeField] private EventPictureRenderer _eventPictureRenderer;
        [SerializeField] private DiscoRushLeaderboardView _discoRushLeaderboardView;
        [SerializeField] private Button _claimButton;
        [SerializeField] private Button _continueButton;
        [SerializeField] private GameObject _winBannerHolder;
        [SerializeField] private GameObject _looseBannerHolder;
        [SerializeField] private GameObject _regularBannerHolder;
        [SerializeField] private LocalizedTextPro[] _rankNumberText;
        [SerializeField] private Button _secondCloseButton;
        [SerializeField] private TextMeshProUGUI _mainText;
        
        private const string RankNumberItem = "DISCORUSH_RANK_NUMBER_ITEM";
        
        protected override void OnContextInitialized(IContext context)
        {
            _discoRushLeaderboardView.InitWithContext(context);
            _claimButton.ReplaceOnClick(ClaimButtonHandler);
            _continueButton.ReplaceOnClick(ContinueButtonHandler);
            _secondCloseButton.ReplaceOnClick(CloseButton.onClick.Invoke);
            base.OnContextInitialized(context);
        }
        
        public override void RefreshRemoteDataDependent(RaceCompetitionViewModel viewModel)
        {
            _discoRushLeaderboardView.Refresh(viewModel, ShowRewardButtonCallback);
            
            var (eventUid, stageName) = RaceEvent.GetRaceStageUid().SplitRaceUid();
            var prefabName = string.Format(EventPictureStringFormat, stageName);
            _eventPictureRenderer.RefreshFromConfig<RaceEventVisualConfig>(eventUid, prefabName);

            for (var i = 0; i < viewModel.PlayersList.Count; i++)
            {
                if (!viewModel.PlayersList[i].IsOwn) continue;
                foreach (var rankNumber in _rankNumberText)
                {
                    var rank = i + 1;
                    rankNumber.SetTextId(RankNumberItem, rank);
                }
            }
            _mainText.text = viewModel.RaceEventMainModalText;
            base.RefreshRemoteDataDependent(viewModel);
        }
        
        public override void StartTransitionToActualState(RaceCompetitionViewModel viewModel)
        {
            _discoRushLeaderboardView.TryStartTransitionToActualState(viewModel);
        }
        
        public override void AutoShowRewardSpeechBubble()
        {
            _discoRushLeaderboardView.AutoOpenRewardSpeechBubble();
        }
        
        public override void RefreshRemoteDataIndependent(RaceEvent raceEvent)
        {
            base.RefreshRemoteDataIndependent(raceEvent);
            _raceEventNameText.text = raceEvent.EventNameText;
        }

        protected override void UpdateButtons()
        {
            base.UpdateButtons();
            _claimButton.gameObject.SetActive(false);
            _continueButton.gameObject.SetActive(false);
            _regularBannerHolder.SetActive(false);
            _looseBannerHolder.SetActive(false);
            _winBannerHolder.SetActive(false);

            if (RaceEvent.IsFinished())
            {
                if (RaceEvent.IsInPrizePlace(true))
                {
                    _claimButton.gameObject.SetActive(true);
                    _winBannerHolder.SetActive(true);
                }
                else
                {
                    _continueButton.gameObject.SetActive(true);
                    _looseBannerHolder.SetActive(true);
                }
            }
            else
            {
                _continueButton.gameObject.SetActive(true);
                _regularBannerHolder.SetActive(true);
            }
        }

        public override void Hide()
        {
            _discoRushLeaderboardView.Hide();
            base.Hide();
        }
    }
}