using System;
using System.Collections.Generic;
using GameAssets.Scripts.UI.OverlayDialog;
using JetBrains.Annotations;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace BBB.RaceEvents.UI
{
    public class RaceCompetitionPrizeWidget : BbbMonoBehaviour
    {  
        protected static readonly int HiddenHash = Animator.StringToHash("HiddenState");
        protected static readonly int ClaimTrigger = Animator.StringToHash("Claim");
        protected static readonly int ShowTrigger = Animator.StringToHash("Show");
        
        [SerializeField] protected Animator _animator;
        [SerializeField] protected Button _claimButton;
        [SerializeField] protected Canvas _claimButtonCanvas;
        [SerializeField] protected Canvas _raceOverStateCanvas;
        [SerializeField] protected Canvas _hideStateCanvas;
        [SerializeField] protected List<Button> _showRewardButtons;
        [FormerlySerializedAs("_hiddenStateSpeechBubbleConfig")] [SerializeField] protected OverlayDialogConfig _hiddenStateOverlayDialogConfig;
        [FormerlySerializedAs("_raceOverStateSpeechBubbleConfig")] [SerializeField] protected OverlayDialogConfig _raceOverStateOverlayDialogConfig;
        [SerializeField] protected GameObject _finishImageGo;
        
        protected Action ClaimButtonCallback;
        protected Action<int, OverlayDialogConfig> ShowRewardButtonCallback;

        protected int Index;
        protected bool AlreadyShown;
        
        public bool Enabled
        {
            set => AlreadyShown = value;
        }
        
        public virtual void Refresh(PlayerRowViewData playerItem, int index,
            int scoreGoal, Action claimButtonCallback, 
            Action<int, OverlayDialogConfig> showRewardButtonCallback)
        {
            
        }

        public void Hide()
        {
            _animator.Play(HiddenHash);
            _animator.ResetTrigger(ClaimTrigger);
            _animator.ResetTrigger(ShowTrigger);
            _animator.enabled = false;
            _claimButtonCanvas.enabled = false;
            _hideStateCanvas.enabled = false;
            _claimButton.enabled = false;
        }

        public virtual void StartTransitionToActualState(PlayerRowViewData playerItem, 
            int scoreGoal, int index)
        {
           
        }

        [UsedImplicitly]
        public void OnClaimTransitionFinished()
        {
            OnClaimStateEnter();
        }

        [UsedImplicitly]
        public void OnShowTransitionFinished()
        {
            OnShowStateEnter();
        }

        protected void OnClaimStateEnter()
        {
            _hideStateCanvas.enabled = false;
            _animator.Play(HiddenHash);
            _animator.enabled = false;
        }

        protected void OnShowStateEnter()
        {
            _hideStateCanvas.enabled = false;
            _claimButton.enabled = false;
            _animator.Play(HiddenHash);
            _animator.enabled = false;
        }

        protected void OnHideStateEnter()
        {
            _animator.enabled = true;
            _claimButton.enabled = false;
            _animator.Play(HiddenHash);
        }

        protected void OnClaimButton()
        {
            ClaimButtonCallback.SafeInvoke();
        }

        public void OnShowRewardButton(bool enableAutoHide)
        {
            var speechBubbleConfig = _raceOverStateCanvas.enabled
                ? _raceOverStateOverlayDialogConfig : _hiddenStateOverlayDialogConfig;
            
            speechBubbleConfig.AutoHide = enableAutoHide;
            ShowRewardButtonCallback.SafeInvoke(Index, speechBubbleConfig);
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            ClaimButtonCallback = null;
            ShowRewardButtonCallback = null;
        }
    }
}