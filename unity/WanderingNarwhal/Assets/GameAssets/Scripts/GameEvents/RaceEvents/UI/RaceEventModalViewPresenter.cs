using BBB.DI;
using BBB.Generic.Modal;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.RaceEvents.UI
{
    public class RaceEventModalViewPresenter : RaceCompetitionModalViewPresenter, IDestroyable
    {
        [SerializeField] private GameObject _flagGo;
        [SerializeField] private RaceEventLeaderboardView _raceEventLeaderboardView;
        [SerializeField] private TextMeshProUGUI _mainText;
        
        [SerializeField] private Button _continueButton;
        [SerializeField] private Button _claimButton;
        [SerializeField] private Button _startAgainButton;
        [SerializeField] private GameObject _continueButtonHolder;
        [SerializeField] private GameObject _claimButtonHolder;
        [SerializeField] private GameObject _startAgainButtonHolder;
        
        protected override void OnContextInitialized(IContext context)
        {
            _raceEventLeaderboardView.InitWithContext(context);
            _continueButton.ReplaceOnClick(ContinueButtonHandler);
            _claimButton.ReplaceOnClick(ClaimButtonHandler);
            _startAgainButton.ReplaceOnClick(StartAgainButtonHandler);
            base.OnContextInitialized(context);
        }
        
        public override void RefreshRemoteDataDependent(RaceCompetitionViewModel viewModel)
        {
            _raceEventLeaderboardView.Refresh(viewModel, ClaimButtonCallback, ShowRewardButtonCallback);
            _flagGo.SetActive(viewModel.FlagActive);
            _mainText.text = viewModel.RaceEventMainModalText;
            base.RefreshRemoteDataDependent(viewModel);
        }

        protected override void UpdateButtons()
        {
            base.UpdateButtons();
            _continueButtonHolder.SetActive(false);
            _claimButtonHolder.SetActive(false);
            _startAgainButtonHolder.SetActive(false);
            
            if (RaceEvent.IsFinished())
            {
                if (RaceEvent.IsInPrizePlace(true))
                {
                    _claimButtonHolder.SetActive(true);
                }
                else
                {
                    if (RaceEvent.GetTimeLeft().TotalSeconds <= 0)
                    {
                        _continueButtonHolder.SetActive(true);
                    }
                    else
                    {
                        _startAgainButtonHolder.SetActive(true);
                    }
                }
            }
            else
            {
                _continueButtonHolder.SetActive(true);
            }
        }
        
        public override void RefreshRemoteDataIndependent(RaceEvent raceEvent)
        {
            base.RefreshRemoteDataIndependent(raceEvent);
            _raceEventNameText.text = raceEvent.EventNameText;
            _mainText.text = string.Empty;
        }
        
        public override void StartTransitionToActualState(RaceCompetitionViewModel viewModel)
        {
            _raceEventLeaderboardView.TryStartTransitionToActualState(viewModel);
        }

        public override void AutoShowRewardSpeechBubble()
        {
            _raceEventLeaderboardView.AutoShowRewardSpeechBubble();
        }

        protected override void OnHide()
        {
            _raceEventLeaderboardView.Hide();
            _flagGo.SetActive(false);
            base.OnHide();
        }
    }
}