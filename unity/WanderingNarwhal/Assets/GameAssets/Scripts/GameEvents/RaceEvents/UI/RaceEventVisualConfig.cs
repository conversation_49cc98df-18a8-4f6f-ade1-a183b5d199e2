using System;
using System.Collections.Generic;
using BBB.Core;
using UnityEngine;

namespace BBB.RaceEvents.UI
{
    [CreateAssetMenu(menuName = "BBB/RaceEventVisualConfig", fileName = "RaceEventVisualConfig", order = 1)]
    public class RaceEventVisualConfig : EventVisualConfig
    {
        [Serializable]
        public class VisualType_SpritePair
        {
            public int PlaceIndex;
            public string ViewOverride;
        }

        [SerializeField] private List<VisualType_SpritePair> _viewOverridesByPlaceIndexList;
        
        [SerializeField] public Color FinishLineColor;
        [SerializeField] public bool ShowEventIcon;
        
        private static Dictionary<int, string> _viewOverridesByPlaceIndexDict;

        public string GetViewOverride(int placeIndex)
        {
            if (_viewOverridesByPlaceIndexDict == null)
            {
                _viewOverridesByPlaceIndexDict = new Dictionary<int, string>();

                foreach (var item in _viewOverridesByPlaceIndexList)
                {
                    _viewOverridesByPlaceIndexDict[item.PlaceIndex] = item.ViewOverride;
                }
            }

            if (_viewOverridesByPlaceIndexDict.TryGetValue(placeIndex, out var viewOverride))
            {
                return viewOverride;
            }
    
            BDebug.LogError(LogCat.Events, "No view override found for place index");

            return null;
        }
    }
}