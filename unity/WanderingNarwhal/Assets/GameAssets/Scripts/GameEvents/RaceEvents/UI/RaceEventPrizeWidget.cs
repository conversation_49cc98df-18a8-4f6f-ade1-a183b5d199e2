using System;
using GameAssets.Scripts.UI.OverlayDialog;

namespace BBB.RaceEvents.UI
{
    public class RaceEventPrizeWidget : RaceCompetitionPrizeWidget
    {  
        public override void Refresh(PlayerRowViewData playerItem, int index, int scoreGoal, Action claimButtonCallback,
            Action<int, OverlayDialogConfig> showRewardButtonCallback)
        {
            var goalReached = playerItem.LastScore >= scoreGoal;
            
            _finishImageGo.SetActive(index == 0);

            Index = index;

            _claimButtonCanvas.enabled = false;
            _raceOverStateCanvas.enabled = false;
            _hideStateCanvas.enabled = false;
            if (goalReached)
            {
                if (playerItem.IsOwn)
                {
                    _claimButtonCanvas.enabled = true;
                    OnClaimStateEnter();
                }
                else
                {
                    _raceOverStateCanvas.enabled = true;
                    OnShowStateEnter();
                }
            }
            else
            {
                _hideStateCanvas.enabled = true;
                OnHideStateEnter();
            }
            
            ClaimButtonCallback = claimButtonCallback;
            ShowRewardButtonCallback = showRewardButtonCallback;
            _claimButton.ReplaceOnClick(OnClaimButton);
            foreach(var button in _showRewardButtons)
            {
                button.ReplaceOnClick(() =>
                {
                    OnShowRewardButton(false);
                });
            }
        }

        public override void StartTransitionToActualState(PlayerRowViewData playerItem, int scoreGoal, int index)
        {
            if (playerItem.LastScore == playerItem.CurrentScore &&
                (playerItem.CurrentScore < scoreGoal || AlreadyShown)) return;
            
            var goalReached = playerItem.CurrentScore >= scoreGoal;
                
            if (goalReached)
            {
                _animator.enabled = true;
                _animator.Play(HiddenHash);

                if (playerItem.IsOwn)
                {
                    _claimButtonCanvas.enabled = true;
                    _claimButton.enabled = true;
                    _animator.SetTrigger(ClaimTrigger);
                }
                else
                {
                    AlreadyShown = true;
                    _raceOverStateCanvas.enabled = true;
                    _animator.SetTrigger(ShowTrigger);
                }
            }
            else
            {
                AlreadyShown = false;
            }
        }
    }
}