using System.Collections;
using BBB.Core;
using BBB.DI;
using BebopBee;
using BebopBee.Core.UI;
using GameAssets.Scripts.Core;
using UnityEngine;

namespace BBB.RaceEvents.UI
{
    public class RaceCompetitionModalController : EventBaseModalsController<IRaceCompetitionModalViewPresenter>
    {
        private RaceEvent _raceEvent;

        private IPlayerManager _playerManager;
        private IAccountManager _accountManager;
        private IRaceEventManager _raceEventManager;
        private Coroutine _fetchingWaitRoutine;
        private bool _continueButtonClicked;
        private bool _startAgainButtonClicked;

        protected override void OnInitializeByContext(IContext context)
        {
            _playerManager = context.Resolve<IPlayerManager>();
            _accountManager = context.Resolve<IAccountManager>();
            CoroutineExecutor = context.Resolve<ICoroutineExecutor>();
            _raceEventManager = context.Resolve<IRaceEventManager>();

            base.OnInitializeByContext(context);
        }

        public void Setup(RaceEvent raceEvent)
        {
            _raceEvent = raceEvent;
            EventUid = raceEvent?.Uid;

            _fetchingWaitRoutine = CoroutineExecutor.StartCoroutine(WaitForFetchingDone(raceEvent));
        }

        private IEnumerator WaitForFetchingDone(RaceEvent raceEvent)
        {
            while (!IsReady())
            {
                yield return null;
            }

            View.SetLoadingStateActive(false);
            View.RefreshRemoteDataIndependent(raceEvent);
            
            var finished = _raceEvent.IsFinished();
            if ((finished || !raceEvent.Joined) && raceEvent.FetchingDataNow)
            {
                if (!ConnectivityStatusManager.ConnectivityReachable)
                {
                    base.OnCloseClicked();
                    yield break;
                }
                
                View.SetLoadingStateActive(true);
                while (raceEvent.FetchingDataNow)
                {
                    yield return null;
                }
                View.SetLoadingStateActive(false);
                
                if (!raceEvent.Joined)
                {
                    base.OnCloseClicked();
                    yield break;
                }
            }
            
            yield return null;
            
            while (!View.IsVisible())
            {
                yield return null;
            }
            
            //waiting one more frame after view became visible to be sure players layout group was refreshed before calculating progress bar widths
            yield return null;
            
            var viewModel = RaceCompetitionViewModel.Create(_accountManager, raceEvent);
            View.RefreshRemoteDataDependent(viewModel);

            View.StartTransitionToActualState(viewModel);
            
            if (raceEvent.WasNotIntroduced())
            {
                View.AutoShowRewardSpeechBubble();
                raceEvent.MarkAsIntroduced();
            }
            _fetchingWaitRoutine = null;
        }

        protected override void OnShow()
        {
            base.OnShow();

            _continueButtonClicked = false;
            _startAgainButtonClicked = false;
            _raceEvent.TriggerLiveOpsEvent(LiveOpsAnalytics.AnalyticNames.SnapShot);
            Subscribe();
        }

        protected override void OnHide()
        {
            base.OnHide();
            Unsubscribe();

            if (_fetchingWaitRoutine != null)
            {
                CoroutineExecutor.StopCoroutine(_fetchingWaitRoutine);
                _fetchingWaitRoutine = null;
            }
        }

        protected override void OnPostHide()
        {
            base.OnPostHide();

            _raceEvent.HandleAfterSeen(_accountManager.Profile);

            if (_raceEvent.IsFinished() && !_raceEvent.SubmittingDataNow)
            {
                if (_raceEvent.IsInPrizePlace(true))
                {
                    _raceEventManager.ClaimRewardFor(_raceEvent);
                }
                else if (!_startAgainButtonClicked)
                {
                    _raceEventManager.HandleLoss(_raceEvent);
                }
            }
            else if (_continueButtonClicked)
            {
                _raceEventManager.EnterLevelFlow();
            }
        }

        private void Subscribe()
        {
            Unsubscribe();

            View.ClaimButtonEvent += ClaimButtonHandler;
            View.InfoButtonEvent += InfoButtonHandler;
            View.ContinueButtonEvent += ContinueButtonHandler;
            View.StartAgainButtonEvent += StartAgainButtonHandler;
        }

        private void Unsubscribe()
        {
            View.ClaimButtonEvent -= ClaimButtonHandler;
            View.InfoButtonEvent -= InfoButtonHandler;
            View.ContinueButtonEvent -= ContinueButtonHandler;
            View.StartAgainButtonEvent -= StartAgainButtonHandler;
        }

        private void StartAgainButtonHandler()
        {
            _startAgainButtonClicked = true;

            var ownPreviousRaceRank = _raceEvent.GetOwnPlace();
            
            _raceEvent.LeaveRace(success =>
            {
                if (success)
                {
                    _raceEventManager.TryToShowRace(_raceEvent, ShowMode.Immediate, ownPreviousRaceRank: ownPreviousRaceRank);
                }
                base.OnCloseClicked();
            });
        }

        private void ClaimButtonHandler()
        {
            base.OnCloseClicked();
        }

        private void ContinueButtonHandler()
        {
            _continueButtonClicked = true;
            base.OnCloseClicked();
        }

        private void InfoButtonHandler()
        {
            _raceEventManager.ShowRaceInfo(_raceEvent, () =>
            {
                _raceEventManager.TryToShowRace(_raceEvent, ShowMode.Delayed, RaceInfoOpeningReason.Play);
            });
            base.OnCloseClicked();
        }

        protected override void OnCloseClicked()
        {
            if (!_startAgainButtonClicked)
                base.OnCloseClicked();
        }
    }
}