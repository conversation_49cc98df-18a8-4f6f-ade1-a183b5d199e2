using System;
using BBB.DI;
using BBB.Screens;
using BBB.UI;
using GameAssets.Scripts.UI.OverlayDialog;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.RaceEvents.UI
{
    public class RaceCompetitionModalViewPresenter : ModalsViewPresenter, IRaceCompetitionModalViewPresenter
    {
        public event Action InfoButtonEvent;
        public event Action ContinueButtonEvent;
        public event Action ClaimButtonEvent;
        public event Action StartAgainButtonEvent;
        
        [SerializeField] private Button _infoButton;
        [SerializeField] protected TextMeshProUGUI _raceEventNameText;
        [SerializeField] private ClockCountdownText _clockCountdownText;

        [SerializeField] private Canvas _loadingStateCanvas;
        [SerializeField] private Canvas _loadedStateCanvas;
        
        [SerializeField] private Button _hideSpeechBubbleButton;
        
        private ILocalizationManager _localizationManager;
        protected IOverlayDialogManager OverlayDialogManager;

        protected RaceEvent RaceEvent { private set; get; }

        protected override void OnContextInitialized(IContext context)
        {
            _localizationManager = context.Resolve<ILocalizationManager>();
            OverlayDialogManager = context.Resolve<IOverlayDialogManager>();
            
            _infoButton.ReplaceOnClick(OnInfoButtonPressed);
            _hideSpeechBubbleButton.ReplaceOnClick(OnHideSpeechBubbleButton);
            base.OnContextInitialized(context);
        }
        
        protected void ContinueButtonHandler()
        {
            ContinueButtonEvent?.Invoke();
        }
        
        protected void ClaimButtonHandler()
        {
            ClaimButtonCallback();
        }

        protected void StartAgainButtonHandler()
        {
            SetLoadingStateActive(true);
            StartAgainButtonEvent?.Invoke();
        }

        public virtual void RefreshRemoteDataIndependent(RaceEvent raceEvent)
        {
            RaceEvent = raceEvent;
            _clockCountdownText.Init(_localizationManager, raceEvent.GetTimeLeft);
            _clockCountdownText.TimerExpired -= UpdateButtons;
            _clockCountdownText.TimerExpired += UpdateButtons;
        }

        public virtual void AutoShowRewardSpeechBubble()
        {
           
        }

        protected virtual void UpdateButtons()
        {
            _clockCountdownText.TimerExpired -= UpdateButtons;
        }

        public virtual void RefreshRemoteDataDependent(RaceCompetitionViewModel viewModel)
        {
            UpdateButtons();
        }

        public virtual void StartTransitionToActualState(RaceCompetitionViewModel viewModel)
        {
           
        }

        protected override void OnHide()
        {
            _clockCountdownText.TimerExpired -= UpdateButtons;
            base.OnHide();
        }

        protected void ClaimButtonCallback()
        {
            ClaimButtonEvent.SafeInvoke();
        }

        protected void ShowRewardButtonCallback(int index, OverlayDialogConfig overlayDialogConfig)
        {
            var rewardDict = RaceEvent.GetRewardForPlaceIndex(index);
            if (rewardDict != null)
            {
                overlayDialogConfig.DisplayType = DisplayType.RewardSpeechBubble;
                overlayDialogConfig.RewardToDisplay = rewardDict;
                overlayDialogConfig.TextToDisplay = RaceEvent.GetRewardText(index);
                OverlayDialogManager.ToggleOverlayDialog(overlayDialogConfig);
            }
        }

        private void OnInfoButtonPressed()
        {
            InfoButtonEvent.SafeInvoke();
        }

        private void OnHideSpeechBubbleButton()
        {
            OverlayDialogManager.HideAllOverlayDialogs();
        }

        public void SetLoadingStateActive(bool active)
        {
            _loadingStateCanvas.enabled = active;
            _loadedStateCanvas.enabled = !active;
        }
    }
}