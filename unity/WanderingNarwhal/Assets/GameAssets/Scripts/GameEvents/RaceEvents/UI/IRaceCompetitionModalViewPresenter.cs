using System;
using BBB.UI.Core;

namespace BBB.RaceEvents.UI
{
    public interface IRaceCompetitionModalViewPresenter : IViewPresenter
    {
        event Action InfoButtonEvent;
        event Action ClaimButtonEvent;
        event Action ContinueButtonEvent;
        event Action StartAgainButtonEvent;
        void RefreshRemoteDataDependent(RaceCompetitionViewModel viewModel);
        void StartTransitionToActualState(RaceCompetitionViewModel viewModel);
        void SetLoadingStateActive(bool active);
        void RefreshRemoteDataIndependent(RaceEvent raceEvent);
        void AutoShowRewardSpeechBubble();
    }
}