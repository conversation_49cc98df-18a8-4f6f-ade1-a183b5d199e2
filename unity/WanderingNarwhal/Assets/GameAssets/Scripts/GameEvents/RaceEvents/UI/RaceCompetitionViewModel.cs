using System;
using System.Collections.Generic;
using BebopBee;
using RPC.Social;

namespace BBB.RaceEvents.UI
{
    public class PlayerRowViewData
    {
        public PlayerRowViewData(RaceEventUserDto opponent, int lastSeenScore)
        {
            Uid = opponent.Uid;
            Name = opponent.Name;
            Avatar = opponent.AvatarUrl;
            Country = opponent.Country;
            CurrentScore = opponent.Score;
            CurrentScoreTimestamp = opponent.ScoreTimestamp;
            LastScore = lastSeenScore;
            IsOwn = false;
        }

        public PlayerRowViewData(Profile ownProfile, RaceEvent raceEvent, int lastSeenScore)
        {
            Uid = ownProfile.Uid;
            Name = ownProfile.Name;
            Avatar = ownProfile.Avatar;
            Country = ownProfile.Country;
            CurrentScore = raceEvent.GetOwnScore();
            CurrentScoreTimestamp = raceEvent.GetOwnScoreTimestamp();
            
            LastScore = lastSeenScore;

            if (LastScore > CurrentScore)
                LastScore = CurrentScore;
            
            IsOwn = true;
        }

        public string Uid { get; set; }

        private  string _name;
        public string Name
        {
            get
            {
                if (_name == null)
                {
                    return "Stranger";
                }
                return _name.Contains(" ") ? _name.Split(" ")[0] : _name;
            }
            private set => _name = value;
        }

        public string Avatar { get; set; }
        public string Country { get; set; }
        public int CurrentScore { get; set; }
        public double CurrentScoreTimestamp { get; set; }
        public int LastScore { get; set; }
        public bool IsOwn { get; set; }
    }
    
    public class RaceCompetitionViewModel
    {
        public string RaceEventUid { get; private set; }
        
        public string RaceStageUid { get; private set; }
        
        public string RaceEventMainModalText { get; private set; }
        public int ScoreGoal { get; private set; }
        
        public List<PlayerRowViewData> PlayersList { get; private set; }
        
        public bool FlagActive { get; private set; }
        
        public int RewardCount { get; private set; }
        
        //debug only flag
        public static bool OpponentsDebug { get; set; }

        public static RaceCompetitionViewModel Create(IAccountManager accountManager, RaceEvent raceEvent)
        {
            var viewModel = new RaceCompetitionViewModel();

            viewModel.RaceEventUid = raceEvent.Uid;
            viewModel.RaceStageUid = raceEvent.GetRaceStageUid();
            viewModel.RaceEventMainModalText = raceEvent.CurrentMainModalText;
            viewModel.ScoreGoal = raceEvent.GetCurrentRaceScoreGoal();
            viewModel.RewardCount = raceEvent.GetCurrentRaceStagePrizeCount();

            viewModel.PlayersList = new List<PlayerRowViewData>();

            var profile = accountManager.Profile;

            var lastSeenScore = raceEvent.GetLastScoreSeenFor(profile.Uid);
            viewModel.PlayersList.Add(new PlayerRowViewData(profile, raceEvent, lastSeenScore));

            var opponentsData = raceEvent.GetOpponentsData();

            if (opponentsData != null)
            {
                foreach (var opponent in opponentsData)
                {
                    lastSeenScore = raceEvent.GetLastScoreSeenFor(opponent.Uid);
                    viewModel.PlayersList.Add(new PlayerRowViewData(opponent, lastSeenScore));
                }
            }
            
            viewModel.PlayersList.Sort((a, b) =>
            {
                var result = -a.CurrentScore.CompareTo(b.CurrentScore);
                if (result != 0) return result;

                result = a.CurrentScoreTimestamp.CompareTo(b.CurrentScoreTimestamp);
                if (result != 0) return result;

                if (a.IsOwn)
                {
                    return a.CurrentScore > 0 ? -1 : 1;
                }
                
                if (b.IsOwn)
                {
                    return b.CurrentScore > 0 ? 1 : -1;
                }

                return 0;
            });

            viewModel.FlagActive = !raceEvent.IsFinished() || raceEvent.IsInPrizePlace();

            
            return viewModel;
        }
    }
}