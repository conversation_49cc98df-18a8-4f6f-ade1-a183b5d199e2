using BBB.DI;
using BBB.Wallet;
using Cysharp.Threading.Tasks;

namespace BBB.RaceEvents.UI
{
    public class FoodCarnivalPlayerView : RaceCompetitionPlayerView
    {
        private CurrencyIconsLoader _currencyIconsLoader;

        public override void InitWithContext(IContext context)
        {
            base.InitWithContext(context);
            _currencyIconsLoader = context.Resolve<CurrencyIconsLoader>();
        }

        public override void Refresh(PlayerRowViewData playerRowViewData, int index, int scoreGoal, string raceEventUid, string raceStageUid, bool displayScores)
        {
            base.Refresh(playerRowViewData, index, scoreGoal, raceEventUid, raceStageUid, displayScores);

            if (raceEventUid != null)
            {
                _currencyIconsLoader
                    .LoadAndGetCurrencySpriteAsync(InventoryItems.GetRaceEventStageCurrency(raceStageUid))
                    .ContinueWith(sprite => ProgressRenderer.Image.sprite = sprite);
            }
        }

        protected override void OnDestroy()
        {
            ProgressRenderer.Image.sprite = null;
            base.OnDestroy();
        }
    }
}