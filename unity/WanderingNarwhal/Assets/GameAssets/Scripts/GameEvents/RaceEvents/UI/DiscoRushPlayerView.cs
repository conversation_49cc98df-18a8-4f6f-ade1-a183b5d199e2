using BBB;
using TMPro;
using BBB.DI;
using UnityEngine;
using BBB.RaceEvents.UI;
using DG.Tweening;
using GameAssets.Scripts.SocialScreens.Teams;

namespace GameAssets.Scripts.GameEvents.RaceEvents.UI
{
    public class DiscoRushPlayerView : BbbMonoBehaviour
    {
        [SerializeField] private TextMeshProUGUI _nameText;
        [SerializeField] private AsyncAvatar _asyncAvatar;

        [SerializeField] private Canvas _canvasToShowHide;
        [SerializeField] private Canvas[] _ownPlayerAvatarBgCanvas;
        [SerializeField] private GameObject _friendPlayerAvatarBgCanvas;
        [SerializeField] protected TextMeshProUGUI _progressText;

        private ISocialManager _socialManager;

        public bool Enabled
        {
            set => _canvasToShowHide.enabled = value;
        }

        public void InitWithContext(IContext context)
        {
            _socialManager = context.Resolve<ISocialManager>();
        }

        public void Refresh(PlayerRowViewData playerRowViewData)
        {
            _nameText.text = playerRowViewData.Name;
            _asyncAvatar.Setup(new AvatarInfo(playerRowViewData));

            foreach (var canvas in _ownPlayerAvatarBgCanvas)
            {
                canvas.enabled = playerRowViewData.IsOwn;
            }

            var isFriend = _socialManager.IsTeamMate(playerRowViewData.Uid) && !playerRowViewData.IsOwn;
            _friendPlayerAvatarBgCanvas.SetActive(isFriend);

            _progressText.SetText($"{playerRowViewData.LastScore}");
        }

        public void StartTransitionToActualState(PlayerRowViewData playerItem, int index, int scoreGoal)
        {
            var tempScore = playerItem.LastScore;
            var currentScore = playerItem.CurrentScore;
            var goalReached = playerItem.CurrentScore >= scoreGoal;

            //this makes only player 1 to cross the finish line
            if (goalReached && index != 0)
            {
                currentScore = scoreGoal;
            }

            DOTween.To(() => tempScore, val => tempScore = val,
                    currentScore, 1)
                .OnUpdate(() => _progressText.SetText($"{tempScore}"));
        }
    }
}