using System.Collections.Generic;
using BBB.Core;
using UnityEngine;
using UnityEngine.Serialization;

namespace BBB.RaceEvents.UI
{
    public class EventVisualConfig : ScriptableObject
    {
        [FormerlySerializedAs("_raceEventPrefabs")]  [SerializeField] private List<StringGameObjectPair> _prefabs;
        
        private Dictionary<string, GameObject> _prefabsMap;

        public GameObject FindPrefabByName(string prefabName)
        {
            if (_prefabsMap == null)
            {
                _prefabsMap = new Dictionary<string, GameObject>();
                if(_prefabs != null)
                    foreach (var pair in _prefabs)
                    {
                        _prefabsMap[pair.Uid] = pair.Prefab;
                    }
            }

            if (_prefabsMap.TryGetValue(prefabName, out var prefab))
            {
                return prefab;
            }
            
            BDebug.LogError(LogCat.General, $"Prefab {prefabName} not found in the collection. Keys={string.Join(", ", _prefabsMap.Keys)}");

            return null;
        }
    }
}