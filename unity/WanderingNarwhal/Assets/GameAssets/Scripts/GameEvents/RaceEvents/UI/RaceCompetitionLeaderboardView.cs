using System.Collections.Generic;
using UnityEngine;
using System;
using BBB.DI;
using DG.Tweening;
using GameAssets.Scripts.UI.OverlayDialog;

namespace BBB.RaceEvents.UI
{
    public class RaceCompetitionLeaderboardView : BbbMonoBehaviour
    {
        [SerializeField] private List<RaceCompetitionPlayerView> _playerViews;
        [SerializeField] private List<RaceCompetitionPrizeWidget> _prizeWidgets;
        
         
        private Tween _transitionTweener;

        public void InitWithContext(IContext context)
        {
            foreach (var playerView in _playerViews)
            {
                playerView.InitWithContext(context);
            }
            
            _playerViews.ForEach(view => view.Enabled = false);
            _prizeWidgets.ForEach(view => view.Enabled = false);
        }

        public void Refresh(RaceCompetitionViewModel viewModel, 
            Action claimButtonCallback, Action<int, OverlayDialogConfig> showRewardButtonCallback)
        {
            var playerItemsList = viewModel.PlayersList;
            
            for (var i = 0; i < playerItemsList.Count; i++)
            {
                if (i < _playerViews.Count)
                {
                    var playerView = _playerViews[i];
                    playerView.Refresh(playerItemsList[i], i, 
                        viewModel.ScoreGoal, viewModel.RaceEventUid, viewModel.RaceStageUid, true);
                    playerView.Enabled = true;
                }

                if (i >= _prizeWidgets.Count) continue;
                
                var prizeWidget = _prizeWidgets[i];
                prizeWidget.Refresh(playerItemsList[i], i,
                    viewModel.ScoreGoal, 
                    claimButtonCallback, showRewardButtonCallback);
            }
        }

        public void AutoShowRewardSpeechBubble()
        {
            if(_prizeWidgets is { Count: > 0 })
            {
                _prizeWidgets[0].OnShowRewardButton(true);
            }
        }

        public void TryStartTransitionToActualState(RaceCompetitionViewModel viewModel)
        {
            var playerItemsList = viewModel.PlayersList;
            for (var i = 0; i < playerItemsList.Count; i++)
            {
                if (i >= _playerViews.Count) continue;
                
                var playerView = _playerViews[i];
                var index = i;

                var playerItem = playerItemsList[i];

                if (playerItem.CurrentScore != playerItem.LastScore || playerItem.CurrentScore == viewModel.ScoreGoal)
                {
                    _transitionTweener = playerView.StartTransitionToActualState(playerItem, index,
                        viewModel.ScoreGoal, () =>
                        {
                            _transitionTweener = null;

                            if (_prizeWidgets.Count <= index || _prizeWidgets[index] == null) return;
                                
                            var prizeWidget = _prizeWidgets[index];
                            prizeWidget.StartTransitionToActualState(playerItemsList[index],
                                viewModel.ScoreGoal, index);
                        });
                }
            }
        }
        
        public virtual void Hide()
        {
            foreach (var playerView in _playerViews)
            {
                playerView.Enabled = false;
            }
            _transitionTweener?.Kill();

            foreach (var playerView in _playerViews)
            {
                playerView.Hide();
            }

            foreach (var prizeWidget in _prizeWidgets)
            {
                prizeWidget.Hide();
            }
        }
    }
}