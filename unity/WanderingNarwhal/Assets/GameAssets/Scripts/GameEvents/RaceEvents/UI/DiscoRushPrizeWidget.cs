using System;
using BBB;
using GameAssets.Scripts.UI.OverlayDialog;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace GameAssets.Scripts.GameEvents.RaceEvents.UI
{
    public class DiscoRushPrizeWidget : BbbMonoBehaviour
    {
        [SerializeField] private Canvas _rewardIcon;
        [SerializeField] private Button _showRewardButtons;
        [FormerlySerializedAs("_speechBubbleConfig")] [SerializeField] private OverlayDialogConfig _overlayDialogConfig;
        private int _index;
        private Action<int, OverlayDialogConfig> _showRewardButtonCallback;

        public void Refresh(int index, Action<int, OverlayDialogConfig> showRewardButtonCallback)
        {
            _index = index;
            _showRewardButtonCallback = showRewardButtonCallback;

            if (_index == 0)
            {
                _rewardIcon.enabled = true;
                _showRewardButtons.ReplaceOnClick(() =>
                {
                    OnShowRewardButton(false);
                });
            }
            else
            {
                _rewardIcon.enabled = false;
            }
        }

        public void OnShowRewardButton(bool enableAutoHide)
        {
            _overlayDialogConfig.AutoHide = enableAutoHide;
            _showRewardButtonCallback.SafeInvoke(_index, _overlayDialogConfig);
        }

        public void StartTransitionToActualState(int index)
        {
            _rewardIcon.enabled = index == 0;
        }
    }
}