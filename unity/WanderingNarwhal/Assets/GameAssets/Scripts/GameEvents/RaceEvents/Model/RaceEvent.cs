using System;
using System.Collections;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.UI.Level;
using BebopBee.Core.Extensions;
using GameAssets.Scripts.Player;
using FBConfig;
using PBGame;
using RPC.Social;
using UnityEngine;
using Profile = BebopBee.Profile;
using BBB.BrainCloud;
using Beebopbee.Core.Extensions;
using BBB.DI;
using BebopBee;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.Utils;
using Newtonsoft.Json;

namespace BBB.RaceEvents
{
    public class RaceEvent : IComparableEvent, IGameEventWithResources, INotifiableEvents
    {
        private const string RaceEventNotificationTitleLoc = "GE_RACEEVENT_START_NOTIFICATION_TITLE";
        private const string RaceEventNotificationMessageLoc = "GE_RACEEVENT_START_NOTIFICATION_MESSAGE";
        
        private const string FoodEventNotificationTitleLoc = "GE_FOODCARNIVAL_START_NOTIFICATION_TITLE";
        private const string FoodEventNotificationMessageLoc = "GE_FOODCARNIVAL_START_NOTIFICATION_MESSAGE";
        
        private const string DiscoEventNotificationTitleLoc = "GE_DISCORUSH_START_NOTIFICATION_TITLE";
        private const string DiscoEventNotificationMessageLoc = "GE_DISCORUSH_START_NOTIFICATION_MESSAGE";
        
        private const string FirstRewardText = "FIRST_PLACE_REWARD_TEXT";
        private const string SecondRewardText = "SECOND_PLACE_REWARD_TEXT";
        private const string ThirdRewardText = "THIRD_PLACE_REWARD_TEXT";

        protected const float StartTimesMaxDifference = 0.01f;

        private const int SuccessResponseStatus = 200;

        private static readonly Regex BotMask = new(".{6}b.-.{2}b.-.b.-.b.-.{10}b.");

        public event Action<RaceEvent> OnRemoteDataUpdated;
        public event Action<RaceEvent> OnRaceEventReleased;

        public bool IsLastScoreAppend { get; private set; }

        protected readonly RaceGameEventConfig RaceEventConfig;
        private readonly List<RaceStage> _raceStages;
        private readonly GameNotificationManager _notificationManager;
        protected readonly IPlayerManager PlayerManager;
        private readonly ILocalizationManager _localizationManager;
        protected readonly ISchedulableDataProvider SchedulableDataProvider;
        private readonly IRaceDataProvider _raceDataProvider;
        private readonly IEventDispatcher _eventDispatcher;
        private readonly BrainCloudManager _brainCloudManager;
        private readonly IAccountManager _accountManager;
        private readonly IConfig _config;
        private bool _fetchingDataNow;
        private bool _submittingDataNow;
        private bool _shouldSubmitScore;
        private readonly int _liveOpsAnalyticsInterval;
        private readonly ICoroutineExecutor _coroutineExecutor;
        private Coroutine _liveOpsRoutine;

        private List<RaceEventUserDto> LastOpponentsData;

        public string Uid => RaceEventConfig.Uid;
        public string ShortNameText => _localizationManager.getLocalizedText(RaceEventConfig.NameTextId);
        public string BundlesLoadLevelUid => RaceEventConfig.BundlesLoadLevelUid;
        public string GetStartNotificationTitle()
        {
            var notificationTitleLoc = RaceEventType switch
            {
                RaceEventTypes.RaceEventType.Simple => RaceEventNotificationTitleLoc,
                RaceEventTypes.RaceEventType.Streak => FoodEventNotificationTitleLoc,
                RaceEventTypes.RaceEventType.Collect => DiscoEventNotificationTitleLoc,
                _ => RaceEventNotificationTitleLoc
            };
            return _localizationManager.getLocalizedText(notificationTitleLoc);
        }

        public string GetStartNotificationMessage()
        {
            var notificationMessageLoc = RaceEventType switch
            {
                RaceEventTypes.RaceEventType.Simple => RaceEventNotificationMessageLoc,
                RaceEventTypes.RaceEventType.Streak => FoodEventNotificationMessageLoc,
                RaceEventTypes.RaceEventType.Collect => DiscoEventNotificationMessageLoc,
                _ => RaceEventNotificationMessageLoc
            };
            return _localizationManager.getLocalizedText(notificationMessageLoc);
        }

        //state related data
        private  GenericEventStateProxy<PBRaceEventState, RaceGameEventConfig> StateProxy => PlayerManager.Player.RaceEventStateProxy;
        protected PBRaceEventState State;
        //

        //remote dependent data, is initialized for the first time when answer from the server comes to the clinet
        public RaceRemoteData CachedRemoteData { get; private set; }
        //

        public int CurrentScore => State?.Score ?? 0;
        public RaceEventTypes.RaceEventType RaceEventType { get; }

        public ResultRaceStatus Status
        {
            get
            {
                if (State == null)
                    return ResultRaceStatus.Unknown;

                return (ResultRaceStatus)State.RaceStatus;
            }
        }
        public bool Joined => State is { Joined: true };
        public bool RemoteDataLoaded => CachedRemoteData?.RaceId != null;
        public bool FetchingDataNow => _fetchingDataNow || _submittingDataNow;
        public bool SubmittingDataNow => _submittingDataNow;
        
        public bool LeaveRaceInProgress { get; private set; }

        public int TotalRaceStages => _raceStages.Count;

        private double _lastRemoteDataUpdateTime = -1.0;
        private readonly GameEventMetaConfig _metaConfig;
        private bool _autoShownInSession;

        public RaceEvent(RaceGameEventConfig raceEventConfig,
            List<RaceStage> raceStages,
            GameNotificationManager notificationManager,
            IPlayerManager playerManager,
            IConfig config,
            ILocalizationManager localizationManager,
            IEventDispatcher eventDispatcher,
            ISchedulableDataProvider schedulableDataProvider,
            IRaceDataProvider raceDataProvider,
            IContext context
            )
        {
            RaceEventConfig = raceEventConfig;
            RaceEventType = raceEventConfig.RaceEventType.TryParseToEnum<RaceEventTypes.RaceEventType>();
            _raceStages = new List<RaceStage>(raceStages);
            _notificationManager = notificationManager;
            PlayerManager = playerManager;
            _config = config;
            _metaConfig = _config.TryGetDefaultFromDictionary<GameEventMetaConfig>();
            _localizationManager = localizationManager;
            SchedulableDataProvider = schedulableDataProvider;
            _raceDataProvider = raceDataProvider;
            _eventDispatcher = eventDispatcher;
            _brainCloudManager = context.Resolve<BrainCloudManager>();
            _accountManager = context.Resolve<IAccountManager>();
            _coroutineExecutor = context.Resolve<ICoroutineExecutor>();
            State = StateProxy.GetEventState(RaceEventConfig.Uid);
            if (State?.PenalizedScore > 0)
            {
                SetScore(0);
                State.PenalizedScore = 0;
                SubmitScore(null);
            }
            UpdateCachedRemoteData(null, false); // Don't trigger update during construction
            
            _liveOpsAnalyticsInterval = GameEventUtils.GetLiveOpsAnalyticsInterval(_config, Uid);
            if (_liveOpsAnalyticsInterval > 0)
            {
                _liveOpsRoutine = _coroutineExecutor.StartCoroutine(TriggerLiveOpsAnalyticsSnapshot());
            }
        }
        
        public void MarkPrizeClaimed()
        {
            if (State != null)
            {
                State.PrizeClaimed = true;
            }
        }
        
        public bool IsPrizeClaimed()
        {
            return State is {PrizeClaimed: true};
        }
        
        public void StopCoroutine()
        {
            if (_liveOpsRoutine != null)
            {
                _coroutineExecutor?.StopCoroutine(_liveOpsRoutine);
                _liveOpsRoutine = null;
            }
        }

        private IEnumerator TriggerLiveOpsAnalyticsSnapshot()
        {
            while (IsLaunched_Internal())
            {
                yield return WaitCache.Seconds(_liveOpsAnalyticsInterval);
                TriggerLiveOpsEvent(LiveOpsAnalytics.AnalyticNames.SnapShot);
            }
        }

        public int GetCurrentStageIndex()
        {
            if (_raceStages.Count == 0)
            {
                BDebug.LogError(LogCat.Events, "Race configs are missing for event " + RaceEventConfig.Uid);
                return -1;
            }

            if (!IsLaunched_Internal())
            {
                BDebug.LogError(LogCat.Events, "Can't get race index if event is not launched for event " + RaceEventConfig.Uid);
                return -1;
            }

            var index = State != null ? State.NumberOfRacesPlayed % _raceStages.Count : 0;
            return index;
        }
        
        public static bool CanShowInScreen(ScreenType screenType)
        {
            return true;
        }

        protected RaceStage GetCurrentRaceStage()
        {
            var index = GetCurrentStageIndex();

            if (index < 0)
                return null;

            return _raceStages[index];
        }

        public int GetCurrentRaceStagePrizeCount()
        {
            var raceConfig = GetCurrentRaceStage();
            return raceConfig.RewardsCount;
        }

        public int GetCurrentRaceScoreGoal()
        {
            var raceConfig = GetCurrentRaceStage();
            return raceConfig.ScoreGoal;
        }

        private bool RaceGoalReached()
        {
            return CurrentScore >= GetCurrentRaceScoreGoal();
        }

        public string CurrentIntroText
        {
            get
            {
                var raceConfig = GetCurrentRaceStage();
                return _localizationManager.getLocalizedText(raceConfig.IntroTextId);
            }
        }

        public string CurrentMainModalText
        {
            get
            {
                if (Status == ResultRaceStatus.Unknown)
                {
                    BDebug.LogError(LogCat.General, "race status in not known, can't check main modal place");
                    return string.Empty;
                }

                var raceConfig = GetCurrentRaceStage();
                if (IsFinished_Internal())
                {
                    if (IsInPrizePlace(true))
                    {
                        return _localizationManager.getLocalizedText(raceConfig.GetVictoryTextId(CachedRemoteData.OwnPlace-1));
                    }

                    return _localizationManager.getLocalizedText(raceConfig.LossMainModalTextId);
                }

                return _localizationManager.getLocalizedText(raceConfig.MainModalTextId);
            }
        }

        public string EventNameText => _localizationManager.getLocalizedText(RaceEventConfig.NameTextId);

        public string StageNameText => _localizationManager.getLocalizedText(GetCurrentRaceStage().NameTextId);
        
        public bool ShouldAutoShow
        {
            get
            {
                if (Joined)
                {
                    if (ConnectivityStatusManager.ConnectivityReachable && IsFinished_Internal())
                        return true;
                }
                else
                {
                    if (Status is ResultRaceStatus.Closed or ResultRaceStatus.Stopped or ResultRaceStatus.Unknown)
                        return false;

                    if (State is { WasIntroduced: false } && !_autoShownInSession)
                        return true;
                }

                return false;
            }
        }
        
        public bool ShouldAutoShowIntro => ShouldAutoShow && !Joined && State is { WasIntroduced: false } && !_autoShownInSession;


        public string GetIntroTextForPreviouslyLost(int rank)
        {
            var raceConfig = GetCurrentRaceStage();
            return _localizationManager.getLocalizedText($"{raceConfig.LossMainModalTextId}_{rank}");
        }
        
        private bool IsEnded => IsLaunched() && !IsEventActive();
        private bool ShouldSyncScore => IsLaunched() && IsEventActive() && (_shouldSubmitScore || State.Score != State.CurrentSubmittedScore);

        public void MarkAsAutoShown()
        {
            _autoShownInSession = true;
        }
        
        private bool IsEventActive()
        {
            var raceStage = GetCurrentRaceStage();
            return State != null && (ResultRaceStatus)State.RaceStatus == ResultRaceStatus.Active && GetTimeLeft().TotalSeconds > 0 && State.CurrentSubmittedScore < raceStage.ScoreGoal && !CachedRemoteData.OpponentsWonEvent(raceStage);
        } 

        public string GetRaceStageUid()
        {
            var raceConfig = GetCurrentRaceStage();
            return raceConfig.Uid;
        }

        private bool IsInPrizePlace_Internal()
        {
            if (Status == ResultRaceStatus.Unknown)
            {
                BDebug.LogError(LogCat.General, "Can not check if race is finished because its status is unknown");
                return false;
            }

            var raceStage = GetCurrentRaceStage();
            return CachedRemoteData.IsOwnPlaceHasReward(raceStage);
        }

        public bool IsFinished()
        {
            return IsFinished_Internal();
        }

        public bool IsInPrizePlace(bool checkIfHasWon = false)
        {
            return IsInPrizePlace_Internal() && (!checkIfHasWon || CachedRemoteData.UserWonEvent(GetCurrentRaceStage()));
        }

        protected virtual bool IsFinished_Internal()
        {
            if (CachedRemoteData.IsFinished(GetCurrentRaceStage()))
            {
                return true;
            }

            return Status == ResultRaceStatus.Stopped;
        }

        public virtual TimeSpan GetTimeLeft(string arg = null)
        {
            return SchedulableConfigUtils.GetTimeLeft(RaceEventConfig, SchedulableDataProvider, arg);
        }

        protected virtual DateTime GetEndTime()
        {
            return SchedulableConfigUtils.GetNextEndTime(RaceEventConfig, SchedulableDataProvider);
        }

        public bool IsLaunched()
        {
            return IsLaunched_Internal();
        }

        protected bool IsLaunched_Internal()
        {
            return State != null && State.CurrentStartTimestamp != 0;
        }

        private DateTime GetStartTimestamp()
        {
            return DateTimeExtensions.FromUnixTimeSeconds(State.CurrentStartTimestamp);
        }

        public virtual void ProcessState(float highestPassedSortOrder)
        {
            var currentTime = SchedulableDataProvider.GetCurrentUtcDateTime();
            var withinTimeSpan = DoesMyTimeSpanContain(currentTime);
            var withinActiveTimeSpan = DoesMyActiveTimeSpanContain(currentTime);

            if (IsReadyToLaunch(withinTimeSpan))
            {
                var requiredLevelSortOrder = LevelHelper.GetLevelSortOrder(_config, RaceEventConfig.RequiredLevelUid);

                if (highestPassedSortOrder >= requiredLevelSortOrder)
                {
                    Launch();
                }
            }
            else if (State is { Uid: null } || IsReadyToRelease(withinActiveTimeSpan) || IsPrizeClaimed())
            {
                if(Joined)
                    LeaveRace(null);

                Release();
            }
            
            UpdateNotifier();
        }

        private void UpdateNotifier()
        {
            if (!IsLaunched_Internal()) return;
            
            var notifier = _notificationManager.GetRaceEventNotifier(RaceEventConfig.Uid);
            if (notifier == null) return;

            var ownPlace = GetOwnPlace();
            var rewardCount = -1;
            if (IsFinished_Internal() && IsInPrizePlace_Internal())
            {
                var placeIndex = ownPlace - 1;
                var raceStage = GetCurrentRaceStage();
                var reward = raceStage.GetRewardAt(placeIndex);
                if (reward is { Count: > 0 })
                {
                    rewardCount = reward.Count;
                }
            }

            if (rewardCount > 0 || !Joined || State.LastShownPlace != ownPlace || !DoesMyTimeSpanContain(SchedulableDataProvider.GetCurrentUtcDateTime()))
            {
                notifier.SetNotifier(1, rewardCount);
            }
        }

        private bool IsReadyToLaunch(bool withinTimeSpan)
        {
            if (IsLaunched_Internal() || !ConnectivityStatusManager.ConnectivityReachable)
                return false;

            switch (Status)
            {
                case ResultRaceStatus.Unknown:
                case ResultRaceStatus.Active:
                    return withinTimeSpan;
                case ResultRaceStatus.Closed:
                    return Joined;
                default:
                    return false;
            }
        }

        private void Launch()
        {
            var lastStartTime = SchedulableConfigUtils.GetLastStartTime(RaceEventConfig, SchedulableDataProvider);
            State = StateProxy.CreateEventState(RaceEventConfig.Uid);
            State.CurrentStartTimestamp = lastStartTime.ToUnixTimeSeconds();
            State.FailedAttempts = 0;
            State.MultiplierStreak = 0;
            State.MultiplierStreakCached = 0;
            State.RaceStatus = (int)ResultRaceStatus.Active;
            State.Uid = RaceEventConfig.Uid;
            PlayerManager.MarkDirty();
        }

        protected virtual bool IsReadyToRelease(bool withinActiveTimeSpawn)
        {
            if (IsLaunched_Internal() && ConnectivityStatusManager.ConnectivityReachable)
            {
                var lastStartTime = SchedulableConfigUtils.GetLastStartTime(RaceEventConfig, SchedulableDataProvider);

                //if start time is a lot different then we release the event
                if (Math.Abs(lastStartTime.ToUnixTimeSeconds() - State.CurrentStartTimestamp) > StartTimesMaxDifference)
                    return true;

                switch (Status)
                {
                    case ResultRaceStatus.Active:
                        return !DoesMyExpirationTimeSpanContain(SchedulableDataProvider.GetCurrentUtcDateTime());
                    case ResultRaceStatus.Closed:
                    case ResultRaceStatus.Stopped:
                        return !Joined;
                    case ResultRaceStatus.Expired:
                    case ResultRaceStatus.Unknown:
                        return true;
                }
            }

            return false;
        }

        private int GetBotCountForThisRace()
        {
            var botCount = 0;
            if (CachedRemoteData.OpponentStates != null)
            {
                foreach (var opponent in CachedRemoteData.OpponentStates)
                {
                    if (BotMask.IsMatch(opponent.Uid))
                    {
                        botCount += 1;
                    }
                }
            }
            return botCount;
        }

        public void LeaveRace(Action<bool> callback)
        {
            if (IsLaunched_Internal() && ConnectivityStatusManager.ConnectivityReachable)
            {
                LeaveRaceInProgress = true;
                _lastRemoteDataUpdateTime = -1.0;
                _fetchingDataNow = true;
                
                _brainCloudManager.LeaveRaceEvent(RaceEventConfig.Uid, LeaveRaceEventSuccess, LeaveRaceEventFail);
                return;
                
                void LeaveRaceEventSuccess(BCRaceEventResponse raceEventResponse)
                {
                    _fetchingDataNow = false;
                    LeaveRaceInProgress = false;

                    var success = raceEventResponse.Status == SuccessResponseStatus;
                    if (success)
                    {
                        if (IsLaunched_Internal())
                        {
                            var raceStage = GetCurrentRaceStage();
                            var raceDuration = (int)DateTime.Now.ToUnixTimeSeconds() - CachedRemoteData.JoinTimestamp;
                            var hasWon = CachedRemoteData.IsOwnPlaceHasReward(raceStage) && CachedRemoteData.UserWonEvent(raceStage);
                            var botCount = GetBotCountForThisRace();
                            Analytics.LogEvent(new RaceEventAnalyticsRaceEnded(
                                CachedRemoteData.OwnPlace,
                                hasWon,
                                CachedRemoteData.OwnScore,
                                raceDuration,
                                botCount, Uid));
                            
                            TriggerLiveOpsEvent(LiveOpsAnalytics.AnalyticNames.End);

                            State.CleanFields(PBRaceEventState.LeaveRace);
                            State.LastShownPlace = 0;
                            State.LastShownScore = null;

                            _lastRemoteDataUpdateTime = -1.0;
                            
                            if (hasWon)
                            {
                                State.NumberOfRacesPlayed++;
                            }
                            
                            OnRaceEventReleased?.Invoke(this);
                            LogResponseMessage("LeaveRace: debugLog: " + raceEventResponse.DebugLog);
                            UpdateCachedRemoteDataFromServer(raceEventResponse.Response);
                            
                            var raceEventUpdateRequest = _eventDispatcher.GetMessage<RaceEventUpdateRequest>();
                            raceEventUpdateRequest.Set(this);
                            _eventDispatcher.TriggerEvent(raceEventUpdateRequest);
                            PlayerManager.MarkDirty();
                            callback.SafeInvoke(true);
                            return;
                        }
                    }
                    else
                    {
                        BDebug.LogError(LogCat.Events, "RaceEvent debug: " + raceEventResponse.Message);
                    }
                    callback.SafeInvoke(false);
                }

                void LeaveRaceEventFail()
                {
                    _fetchingDataNow = false;
                    LeaveRaceInProgress = false;
                    callback.SafeInvoke(false);
                }
            }

            LeaveRaceInProgress = false;
            callback.SafeInvoke(false);
        }
        
        private void LogResponseMessage(string debugLog)
        {
            if (!string.IsNullOrWhiteSpace(debugLog))
            {
                BDebug.Log(LogCat.Events, $"RaceEvent debug: {debugLog}");
            }
        }

        public virtual RaceRemoteData GetRemoteDataFromBC(BCUserRaceData dto)
        {
            return new RaceRemoteData(dto);
        }

        public void Release()
        {
            OnRaceEventReleased?.Invoke(this);
            StopCoroutine();
            Analytics.LogEvent(new RaceEventAnalyticsEventReleased(State.NumberOfRacesPlayed, Uid));
            var notifier = _notificationManager.GetRaceEventNotifier(RaceEventConfig.Uid);
            notifier?.ResetNotifier();
            StateProxy.ReleaseEventState(RaceEventConfig.Uid);
            PlayerManager.MarkDirty();
            State = null;
            LastOpponentsData = CachedRemoteData.OpponentStates;
            UpdateCachedRemoteData();
        }

        protected virtual RaceRemoteData GetDefaultRemoteData()
        {
            return new RaceRemoteData();
        }

        protected virtual bool DoesMyTimeSpanContain(DateTime dateTime)
        {
            return SchedulableConfigUtils.DoesMyTimeSpanContain(RaceEventConfig, SchedulableDataProvider, dateTime);
        }

        //active time span is a time span of event episode with its close offset subtracted from its end time
        //event is considered closed when it approaches to the point near to its end but not ended yet
        //if event is closed you can continue playing races in it but can not join
        private bool DoesMyActiveTimeSpanContain(DateTime dateTime)
        {
            var closeOffset = RaceEventConfig.CloseOffsetInSeconds;
            return SchedulableConfigUtils.DoesMyTimeSpanContain(RaceEventConfig, SchedulableDataProvider, dateTime, 0, -closeOffset);
        }
        //expiration time span is a time span of event episode with its expiration time added to its end time
        //if event is ended but not expired yet you can see your results
        //if it is expired
        protected bool DoesMyExpirationTimeSpanContain(DateTime dateTime)
        {
            var expirationTime = RaceEventConfig.ExpirationTimeInSeconds;
            return SchedulableConfigUtils.DoesMyTimeSpanContain(RaceEventConfig, SchedulableDataProvider, dateTime, 0, expirationTime);
        }

        /// <summary>
        /// Join method used to join the event and fetch the data
        /// </summary>
        /// <param name="callback">callback with success bool and current race status</param>
        public virtual void TryJoinRace(Action<bool, ResultRaceStatus> callback)
        {
            if (!IsLaunched_Internal())
            {
                BDebug.LogError(LogCat.Events, $"Can't join event {RaceEventConfig.Uid} if it is not launched yet");
                callback.SafeInvoke(false, ResultRaceStatus.Unknown);
                return;
            }
            
            if (!ConnectivityStatusManager.ConnectivityReachable)
            {
                callback.SafeInvoke(false, ResultRaceStatus.Unknown);
                return;
            }

            _lastRemoteDataUpdateTime = -1.0;

            _fetchingDataNow = true;
            var raceStage = GetCurrentRaceStage();
            var sortOrder = _raceDataProvider.GetHighestLevelSortOrder();
            _brainCloudManager.JoinRaceEvent(RaceEventConfig.Uid, raceStage.Uid, sortOrder, JoinRaceEventSuccess, JoinRaceEventFail);
            return;
            
            void JoinRaceEventSuccess(BCRaceEventResponse raceEventResponse)
            {
                _fetchingDataNow = false;
                var success = raceEventResponse.Status == SuccessResponseStatus;
                if (success)
                {
                    LogResponseMessage("TryJoinRace: debugLog: "+raceEventResponse.DebugLog);
                    UpdateCachedRemoteDataFromServer(raceEventResponse.Response);
                        
                    Analytics.LogEvent(new RaceEventAnalyticsRaceStarted(GetBotCountForThisRace(), Uid));
                    Analytics.LogEvent(new DauInteractionsEvent(AnalyticNames.Category, Uid, AnalyticNames.RaceStart));
                    TriggerLiveOpsEvent(LiveOpsAnalytics.AnalyticNames.Start);
                }
                else
                {
                    BDebug.LogError(LogCat.Events, "RaceEvent debug: " + raceEventResponse.Message);
                }

                callback.SafeInvoke(success, Status);
            }

            void JoinRaceEventFail()
            {
                _fetchingDataNow = false;
                callback.SafeInvoke(false, Status);
            }
        }

        public void TriggerLiveOpsEvent(string category)
        {
            if (CachedRemoteData is not {OpponentStates: {Count: > 0}}) return;

            var startTime = GetStartTimestamp();
            var endTime = GetEndTime();
            if (startTime == default || endTime == default) return;

            var bots = GetBotCountForThisRace();
            var totalPlayers = CachedRemoteData.OpponentStates.Count + 1;
            var humans = totalPlayers - bots;
            
            if (humans <= 0)
            {
                BDebug.LogError(LogCat.Events, $"Human / Bots count is wrong in Race Event {Uid} " +
                                               $"with race id {CachedRemoteData.RaceId}");
                return;
            }
            
           /* Analytics.LogEventIntoSpecificService<BigFishAnalyticsWrapper>(new LiveOpsEvent(category, GetRaceStageUid(),
                _accountManager.BfgBucketId, CachedRemoteData.RaceId,
                startTime.ToUniversalIso8601(), endTime.ToUniversalIso8601(), humans, bots,
                CurrentScore, GetOwnPlace()));
                */
        }

        public void TryFetchDataSilently()
        {
            if (IsLaunched_Internal() && !_fetchingDataNow && !_submittingDataNow)
            {
                TryFetchRaceRemoteData(null, true);
            }
        }

        /// <summary>
        /// Fetch method used to fetch up to date server data
        /// </summary>
        /// <param name="callback">bool success, RaceRemoteData data</param>
        /// <param name="force">whether to force fetch data or not</param>
        public void TryFetchRaceRemoteData(Action<bool, RaceRemoteData> callback, bool force = false)
        {
            if (!IsLaunched_Internal())
            {
                BDebug.LogError(LogCat.Events, $"Can't fetch event {RaceEventConfig.Uid} data if it is not launched yet");
                callback.SafeInvoke(false, default);
                return;
            }

            if (ShouldSyncScore)
            {
                SubmitScore(callback);
                return;
            }
            
            if (!force && IsLastRemoteDataFresh())
            {
                callback.SafeInvoke(true, CachedRemoteData);
                return;
            }

            if (!ConnectivityStatusManager.ConnectivityReachable)
            {
                callback.SafeInvoke(false, CachedRemoteData);
                return;
            }

            _fetchingDataNow = true;

            _brainCloudManager.FetchRaceEvent(RaceEventConfig.Uid, FetchRaceEventSuccess, FetchRaceEventFail);
            return;

            void FetchRaceEventFail()
            {
                _fetchingDataNow = false;
                callback.SafeInvoke(false, CachedRemoteData);
            }

            void FetchRaceEventSuccess(BCRaceEventResponse raceEventResponse)
            {
                _fetchingDataNow = false;
                var success = raceEventResponse.Status == SuccessResponseStatus;
                if (success)
                {
                    LogResponseMessage("RaceEvent TryFetchRace: debugLog: " + raceEventResponse.DebugLog);
                    UpdateCachedRemoteDataFromServer(raceEventResponse.Response);

                    if (Status == ResultRaceStatus.Expired && !Joined)
                    {
                        BDebug.LogError(LogCat.Events, "RaceEvent unexpected state: somehow fetched expired and not joined event for event " + RaceEventConfig.Uid);
                    }
                }
                else
                {
                    BDebug.LogError(LogCat.Events, "RaceEvent debug: " + raceEventResponse.Message + " debugLog: " + raceEventResponse.DebugLog);
                }
                callback.SafeInvoke(success, CachedRemoteData);
            }
        }

        private bool IsLastRemoteDataFresh()
        {
            if (_lastRemoteDataUpdateTime < 0.0)
            {
                return false;
            }

            var totalUnixSeconds = SchedulableDataProvider.GetCurrentUtcDateTime().ToUnixTimeSeconds();

            return Mathf.Abs((float)(totalUnixSeconds - _lastRemoteDataUpdateTime)) < _metaConfig.RaceTechnicalConfig.Value.SecondsUntilDataRefreshNecessity;
        }

        public void AddScore(int score, bool append = false)
        {
            IsLastScoreAppend = append;
            
            if (IsLaunched_Internal())
            {
                var prevScore = State.Score;
                var newScore = State.Score + score;

                var currentRace = GetCurrentRaceStage();

                if (newScore >= currentRace.ScoreGoal)
                    newScore = currentRace.ScoreGoal;
                
                SetScore(newScore);

                State.LastAddedScoreDeltaToShow += State.Score - prevScore;
            }
        }

        private void SetScore(int score)
        {
            State.Score = score;
            UpdateCachedRemoteDataScore();
        }

        public int LastAddedScoreDeltaToShow => State?.LastAddedScoreDeltaToShow ?? 0;

        public void DebugSetScore(int score)
        {
            if (IsLaunched_Internal())
            {
                var newScore = score;

                var currentRace = GetCurrentRaceStage();

                if (newScore >= currentRace.ScoreGoal)
                    newScore = currentRace.ScoreGoal;
                
                SetScore(newScore);
            }
        }

        /// <summary>
        /// Submit score data to submit the score and fetch current data
        /// </summary>
        /// <param name="callback">callback with success bool and current race status</param>
        public void SubmitScore(Action<bool, RaceRemoteData> callback)
        {
            if (!IsLaunched_Internal())
            {
                BDebug.LogError(LogCat.Events, $"Can't submit score to event {RaceEventConfig.Uid} data if it is not launched yet");
                callback.SafeInvoke(false, default);
                return;
            }

            if (!ConnectivityStatusManager.ConnectivityReachable)
            {
                _shouldSubmitScore = true;
                callback.SafeInvoke(false, CachedRemoteData);
                return;
            }

            _submittingDataNow = true;
            var sortOrder = _raceDataProvider.GetHighestLevelSortOrder();
            _brainCloudManager.SubmitRaceEventScore(RaceEventConfig.Uid, State.Score, sortOrder, SubmitRaceEventScoreSuccess, SubmitRaceEventScoreFail);
            return;
            
            void SubmitRaceEventScoreSuccess(BCRaceEventResponse raceEventResponse)
            {
                var success = raceEventResponse.Status == SuccessResponseStatus;
                _submittingDataNow = false;
                _shouldSubmitScore = !success;
                if (success)
                {
                    LogResponseMessage("RaceEvent SubmitScore: debugLog: " + raceEventResponse.DebugLog);
                    UpdateCachedRemoteDataFromServer(raceEventResponse.Response);
                }
                else
                {
                    BDebug.LogError(LogCat.Events, "RaceEvent debug: " + raceEventResponse.Message);
                }
                callback.SafeInvoke(success, CachedRemoteData);
            }

            void SubmitRaceEventScoreFail()
            {
                _submittingDataNow = false;
                _shouldSubmitScore = true;
                callback.SafeInvoke(false, CachedRemoteData);
            }
        }

        public void HandleRemoteDataUpdate()
        {
            if(CachedRemoteData != null)
                BDebug.Log(LogCat.Events, $"RaceEvent debug: remote data updated: {JsonConvert.SerializeObject(CachedRemoteData)}");
            
            UpdateNotifier();

            _lastRemoteDataUpdateTime = SchedulableDataProvider.GetCurrentUtcDateTime().ToUnixTimeSeconds();

            var raceEventUpdateRequest = _eventDispatcher.GetMessage<RaceEventUpdateRequest>();
            raceEventUpdateRequest.Set(this);
            _eventDispatcher.TriggerEvent(raceEventUpdateRequest);
            OnRemoteDataUpdated.SafeInvoke(this);
        }

        public virtual bool ShouldShowIcon()
        {
            if (!IsLaunched_Internal() || LeaveRaceInProgress ||
                Status is ResultRaceStatus.Closed or ResultRaceStatus.Stopped or ResultRaceStatus.Unknown)
                return false;

            return !IsPrizeClaimed() && 
                   ((Joined && (NotExpiredByTimeYet() || IsInPrizePlace_Internal())) || 
                    DoesMyActiveTimeSpanContain(SchedulableDataProvider.GetCurrentUtcDateTime()));
        }

        public void HandleAfterSeen(Profile userProfile)
        {
            if (State != null)
            {
                State.LastShownPlace = CachedRemoteData.OwnPlace;
                State.WasIntroduced = true;

                State.LastShownScore ??= new Dictionary<string, int>();

                var opponentsData = CachedRemoteData.OpponentStates;
                if(opponentsData != null)
                    foreach (var opponent in opponentsData)
                    {
                        State.LastShownScore[opponent.Uid] = opponent.Score;
                    }

                State.LastShownScore[userProfile.Uid] = CachedRemoteData.OwnScore;

            }

            var notifier = _notificationManager.GetRaceEventNotifier(RaceEventConfig.Uid);
            notifier?.ResetNotifier();
        }

        public int GetOwnPlace()
        {
            return CachedRemoteData.OwnPlace;
        }

        public int GetOwnPlaceBeforeDeltaScore(int deltaScore)
        {
            return CachedRemoteData.GetOwnPlaceBeforeDeltaScore(deltaScore);
        }

        public void UpdateRemoteData(BCUserRaceData dto)
        {
            if (IsLaunched_Internal())
            {
                UpdateCachedRemoteDataFromServer(dto);
            }
        }

        public string EventResourceId => RaceEventConfig.Uid.RemoveDigits();
        public string JoinRewards => RaceEventConfig.JoinReward;

        public bool ShouldKeepResourceLoaded()
        {
            return IsLaunched_Internal();
        }

        public int GetOwnScore()
        {
            return CachedRemoteData.OwnScore;
        }
        
        public double GetOwnScoreTimestamp()
        {
            return CachedRemoteData.OwnScoreTimestamp;
        }

        public List<RaceEventUserDto> GetOpponentsData()
        {
            return Status != ResultRaceStatus.Unknown || !CachedRemoteData.OpponentStates.IsNullOrEmpty() ? CachedRemoteData.OpponentStates : LastOpponentsData;
        }

        public int GetLastScoreSeenFor(string uid)
        {
            return State?.LastShownScore?.GetValueOrDefault(uid, 0) ?? 0;
        }

        public bool CacheToPenalizableScore()
        {
            if (RaceEventType == RaceEventTypes.RaceEventType.Streak && !RaceGoalReached())
            {
                State.PenalizedScore = State.Score;
                SetScore(0);
                return true;
            }

            return false;
        }

        public void RestorePenalizableScore()
        {
            if (RaceEventType == RaceEventTypes.RaceEventType.Streak && State.PenalizedScore > 0)
            {
                SetScore(State.PenalizedScore);
                State.PenalizedScore = 0;
            }
        }

        public bool ResetScore()
        {
            if (RaceEventType != RaceEventTypes.RaceEventType.Streak || !IsLaunched_Internal() || RaceGoalReached())
                return false;
            
            SetScore(0);
            
            return true;
        }

        public Dictionary<string,int> GetRewardForPlaceIndex(int index)
        {
            var raceStage = GetCurrentRaceStage();
            return raceStage.GetRewardAt(index);
        }
        
        public static string GetRewardText(int placeIndex)
        {
            return placeIndex switch
            {
                0 => FirstRewardText,
                1 => SecondRewardText,
                2 => ThirdRewardText,
                _ => string.Empty
            };
        }

        public Dictionary<string, int> ClaimReward()
        {
            if (Status == ResultRaceStatus.Unknown)
            {
                BDebug.LogError(LogCat.Events, "race status is unknown for " + Uid);
                return null;
            }

            if (!IsLaunched_Internal())
            {
                BDebug.LogError(LogCat.Events, "race is not launched to claim " + Uid);
                return null;
            }

            if (IsFinished_Internal())
            {
                if (IsInPrizePlace_Internal())
                {
                    var placeIndex = GetOwnPlace() - 1;
                    var raceStage = GetCurrentRaceStage();
                    var reward = raceStage.GetRewardAt(placeIndex);
                    return reward;
                }
            }

            BDebug.LogError(LogCat.Events, "claiming reward for unfinished event for " + Uid);
            return null;
        }
        public DateTime GetNextStartTime()
        {
            return SchedulableConfigUtils.GetNextStartTime(RaceEventConfig, SchedulableDataProvider);
        }

        public GameEventCurrencyFlowData GetCurrencyFlowAnalyticsData()
        {
            return new GameEventCurrencyFlowData
            {
                Category = CurrencyFlow.GameEvents.Name,
                Family = Uid,
                Item = IsInPrizePlace_Internal()
                    ? string.Format(CurrencyFlow.GameEvents.CompetitivePlaceWin, GetCurrentRaceStage().Uid, CachedRemoteData.OwnPlace)
                    : CurrencyFlow.GameEvents.ConsolationPrize
            };
        }

        public void ClearLastAddedScoreDeltaToShow()
        {
            if (State != null)
                State.LastAddedScoreDeltaToShow = 0;
        }

        public bool AlmostWon()
        {
            if (!IsLaunched_Internal() || !IsInPrizePlace_Internal())
                return false;
            
            var delta = GetCurrentRaceScoreGoal() - CachedRemoteData.OwnScore;
            return delta is <= 3 and > 0;
        }

        public bool NotExpiredByTimeYet()
        {
            return DoesMyExpirationTimeSpanContain(SchedulableDataProvider.GetCurrentUtcDateTime());
        }

        public bool WithinTimeSpan()
        {
            return DoesMyActiveTimeSpanContain(SchedulableDataProvider.GetCurrentUtcDateTime());
        }

        public virtual void HandleLoss()
        {
            LeaveRace(null);
        }

        public virtual void HandleClaimReward(Action<bool> callback)
        {
            LeaveRace(callback.SafeInvoke);
        }
        
        public void DecrementFailedAttempt(bool isShuffleFailed = false)
        {
            if (State == null)
                return;
            
            State.FailedAttempts--;
            if (State.FailedAttempts < 0)
            {
                State.FailedAttempts = 0;
                BDebug.LogWarning(LogCat.Events, "Failed attempts shouldn't be a negative number. Check Increment and Decrement logic");
            }
                
            State.MultiplierStreak = isShuffleFailed ? State.MultiplierStreakCached : ++State.MultiplierStreakCached;
        }

        /// <summary>
        /// Increment on the first move and decrement on win to catch the killing app cases
        /// </summary>
        public void IncrementFailedAttempt()
        {
            if (State == null)
                return;
            
            State.FailedAttempts++;
            State.MultiplierStreakCached = State.MultiplierStreak;
            State.MultiplierStreak = 0;
        }
        
        public bool IsMultiplierScoreStreakActive()
        {
            return RaceEventConfig is { IsDoubleScoreStreakEnabled: true } && State != null && RaceEventConfig.ScoreMultiplierFbLength > 0;
        }

        public int GetScoreMultiplier(bool includeCurrentAttempt = false)
        {
            return GameEventUtils.GetScoreMultiplier(includeCurrentAttempt ? State.MultiplierStreakCached : State.MultiplierStreak, RaceEventConfig.ScoreMultiplierFb, RaceEventConfig.ScoreMultiplierFbLength);
        }
        
        public int GetLowestMultiplier()
        {
            return GameEventUtils.GetLowestMultiplier(RaceEventConfig.ScoreMultiplierFb, RaceEventConfig.ScoreMultiplierFbLength);
        }

        public bool WasNotIntroduced()
        {
            return State is { WasIntroduced: false };
        }

        public void MarkAsIntroduced()
        {
            if (State != null)
            {
                State.WasIntroduced = true;
            }
        }

        private int GetRaceEventTypeSortOrder()
        {
            return RaceEventType == RaceEventTypes.RaceEventType.Collect ? 1 : 0;
        }

        public int CompareTo(IComparableEvent otherEvent)
        {
            if (otherEvent is not RaceEvent otherGameEvent)
            {
                return -1;
            }

            var raceEventTypePriority = GetRaceEventTypeSortOrder();
            var otherRaceEventTypePriority = otherGameEvent.GetRaceEventTypeSortOrder();

            var raceEventTypeComparison = raceEventTypePriority.CompareTo(otherRaceEventTypePriority);

            //this makes sure worldrace and foodcarnival are always higher priority to show than disco rush
            if (raceEventTypeComparison != 0)
                return raceEventTypeComparison;

            var ownScoreRatio = CurrentScore / (double)GetCurrentRaceScoreGoal();
            var otherScoreRatio = otherGameEvent.CurrentScore / (double)otherGameEvent.GetCurrentRaceScoreGoal();
            return otherScoreRatio.CompareTo(ownScoreRatio);
        }

        public void UpdateCachedRemoteDataFromServer(BCUserRaceData dto)
        {
            var remoteData = GetRemoteDataFromBC(dto);
            if (State != null)
            {
                State.RaceStatus = (int)remoteData.RaceStatus;
                State.Joined = remoteData.Joined;
                State.CurrentSubmittedScore = remoteData.OwnScore;
                State.CurrentSubmittedScoreTimestamp = remoteData.OwnScoreTimestamp;
                PlayerManager.MarkDirty();
            }
            UpdateCachedRemoteData(remoteData);
        }

        public void UpdateCachedRemoteData(RaceRemoteData dto = null, bool triggerUpdate = true)
        {
            dto ??= GetDefaultRemoteData();
            
            if (CachedRemoteData == null)
            {
                CachedRemoteData = dto;
            }
            else
            {
                CachedRemoteData.MigrateData(dto);
            }

            UpdateCachedRemoteDataScore();
            
            if (triggerUpdate)
            {
                HandleRemoteDataUpdate();
            }
        }

        private void UpdateCachedRemoteDataScore()
        {
            if (State == null || CachedRemoteData == null) return;
            
            if (IsEnded)
            {
                State.Score = State.CurrentSubmittedScore;
            }
            
            CachedRemoteData.OverrideScore(State.Score, State.CurrentSubmittedScoreTimestamp);
        }

        public virtual void MigrateData(RaceEvent other)
        {
            LastOpponentsData = other.LastOpponentsData;
            IsLastScoreAppend = other.IsLastScoreAppend;
            _autoShownInSession = other._autoShownInSession;
        }
        
        public void Clear()
        {
            OnRemoteDataUpdated = null;
            OnRaceEventReleased = null;
        }
    }
}