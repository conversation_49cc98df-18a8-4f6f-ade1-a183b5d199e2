using System.Collections.Generic;
using RPC.Social;
using BBB.BrainCloud;

namespace BBB.RaceEvents
{
    public class RaceRemoteData
    {
        public ResultRaceStatus RaceStatus;
        public int OwnScore;
        public double OwnScoreTimestamp;
        public List<RaceEventUserDto> OpponentStates;
        public bool Joined;
        public int JoinTimestamp;
        public int OwnPlace;
        public string RaceId;

        public RaceRemoteData()
        {
        }

        public RaceRemoteData(BCUserRaceData dto)
        {
            RaceStatus = (ResultRaceStatus)dto.raceStatus;
            OwnScore = dto.ownScore;
            OwnScoreTimestamp = dto.ownScoreTimestamp;
            OpponentStates = new List<RaceEventUserDto>();

            if (dto.opponents != null)
            {
                foreach (var opponent in dto.opponents)
                {
                    var opponentState = new RaceEventUserDto
                    {
                        Uid = opponent.uid,
                        Name = opponent.name,
                        AvatarUrl = opponent.avatarUrl,
                        Country = opponent.country,
                        Score = opponent.score,
                        ScoreTimestamp = opponent.scoreTimestamp,
                    };
                    OpponentStates.Add(opponentState);
                }
            }

            Joined = dto.joined;
            JoinTimestamp = dto.joinedTimestamp;
            RaceId = dto.raceId;
            OwnPlace = CalculatePlace(OwnScore, OwnScoreTimestamp);
        }

        private int CalculatePlace(int score, double scoreTimestamp)
        {
            var expectedPlace = 1;
            if (OpponentStates == null || OpponentStates.Count == 0) return expectedPlace;
            
            foreach (var opponent in OpponentStates)
            {
                if (opponent.Score < score) continue;
                if (opponent.Score > score)
                {
                    expectedPlace++;
                    continue;
                }

                if (opponent.ScoreTimestamp > scoreTimestamp) continue;
                if (opponent.ScoreTimestamp < scoreTimestamp || score == 0)
                {
                    expectedPlace++;
                }
            }

            return expectedPlace;
        }

        public void OverrideScore(int score, double scoreTimestamp)
        {
            OwnScore = score;
            OwnScoreTimestamp = scoreTimestamp;
            OwnPlace = CalculatePlace(OwnScore, OwnScoreTimestamp);
        }

        public int GetOwnPlaceBeforeDeltaScore(int deltaScore)
        {
            return CalculatePlace(OwnScore - deltaScore, OwnScoreTimestamp);
        }

        public bool IsOwnPlaceHasReward(RaceStage raceStage)
        {
            if (raceStage == null)
                return false;
            
            var ownPlaceIndex = OwnPlace - 1;
            return raceStage.HasRewardAt(ownPlaceIndex);
        }

        public bool IsFinished(RaceStage raceStage)
        {
            return UserWonEvent(raceStage) || OpponentsWonEvent(raceStage);
        }

        public virtual bool UserWonEvent(RaceStage raceStage)
        {
            return raceStage != null && OwnScore >= raceStage.ScoreGoal;
        }

        public bool OpponentsWonEvent(RaceStage raceStage)
        {
            // if we checking for finishing if race event is not launched anymore, we assume it wasn't started yet so not finished
            if (raceStage == null || OpponentStates is not { Count: > 0 }) return false;

            var finishedOpponentsCount = 0;
            foreach (var opponent in OpponentStates)
            {
                if (opponent.Score >= raceStage.ScoreGoal)
                    finishedOpponentsCount++;
            }

            return finishedOpponentsCount >= raceStage.RewardsCount;
        }

        public void MigrateData(RaceRemoteData other)
        {
            RaceStatus = other.RaceStatus;
            OwnScore = other.OwnScore;
            OpponentStates = other.OpponentStates;
            Joined = other.Joined;
            JoinTimestamp = other.JoinTimestamp;
            OwnPlace = other.OwnPlace;
            RaceId = other.RaceId;
        }
    }
}