using BBB.BrainCloud;
using RPC.Social;

namespace BBB.RaceEvents
{
    public class DiscoRushRemoteData : RaceRemoteData
    {
        public DiscoRushRemoteData() : base() {}

        public DiscoRushRemoteData(BCUserRaceData dto) : base(dto)
        {
            
        }
        
        public override bool UserWonEvent(RaceStage raceStage)
        {
            return IsOwnPlaceHasReward(raceStage);
        }
        
    }
}