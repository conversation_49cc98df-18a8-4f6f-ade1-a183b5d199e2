using System;

namespace BBB.RaceEvents
{
    public static class RaceEventHelper
    {
        private const string RACE_ID_SEPARATOR = "_";

        // whole usage of this method is overcomplicated, we already have event uid in that config
        public static (string eventUid, string stageName) SplitRaceUid(this string raceStageUid)
        {
            if (raceStageUid == null || !raceStageUid.Contains("_"))
                return default;

            var separatorIndex = raceStageUid.LastIndexOf(RACE_ID_SEPARATOR, StringComparison.Ordinal);
            var eventUid = raceStageUid[..separatorIndex];
            var stageName = raceStageUid.Substring(separatorIndex + 1, raceStageUid.Length - separatorIndex - 1);
            return (eventUid, stageName);
        }
    }
}