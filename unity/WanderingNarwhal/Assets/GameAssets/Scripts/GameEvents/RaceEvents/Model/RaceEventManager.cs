using System;
using System.Collections.Generic;
using System.Threading;
using BBB.Core;
using BBB.Core.Wallet;
using BBB.DI;
using BBB.Quests;
using BBB.RaceEvents.UI;
using BBB.UI;
using BBB.UI.Level;
using BBB.Wallet;
using BebopBee;
using BebopBee.Core;
using BebopBee.Core.UI;
using Core.Configs;
using GameAssets.Scripts.Core.TimeManager;
using GameAssets.Scripts.CurrenciesRewardModalUI;
using GameAssets.Scripts.IAP.EndlessTreasure;
using RPC.Social;
using BBB.BrainCloud;
using BBB.Core.AssetBundles;
using BBB.Modals;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.GenericModals.Info;
using GameAssets.Scripts.GenericModals.Info.Core;
using FBConfig;
using GameAssets.Scripts.GenericModals.Tutorial;
using GameAssets.Scripts.GenericModals.Tutorial.Core;

namespace BBB.RaceEvents
{
    public enum RaceInfoOpeningReason
    {
        Start = 0,
        Play = 1
    }

    public class RaceEventManager : IContextInitializable, IContextReleasable, ISchedulableDataProvider, IRaceEventManager, IRaceDataProvider, IGameEventProvider, IBundlePredownloadProvider
    {
        private const int SilentFetchIntervalInMillis = 60000; // 60 seconds, e.g. 1 minute

        private static readonly Type[] RequiredConfigs =
        {
            typeof(RaceGameEventConfig),
            typeof(GameEventMetaConfig)
        };

        private readonly List<RaceEvent> _eventsList = new();
        private readonly Dictionary<string, RaceEvent> _eventsDict = new();

        private IConfig _config;
        private IAccountManager _accountManager;
        private GameEventMetaConfig _metaConfig;
        private IGameEventResourceManager _gameEventResourceManager;
        private IPlayerManager _playerManager;
        private ILocalizationManager _localizationManager;
        private TimeManager _timeManager;
        private ILockManager _lockManager;
        private GameNotificationManager _notificationManager;
        private IModalsBuilder _modalsBuilder;
        private IEventDispatcher _eventDispatcher;
        private IScreensManager _screenManager;
        private IWalletManager _walletManager;
        private IUIWalletManager _uiWalletManager;
        private ICoroutineExecutor _coroutineExecutor;
        private IContext _context;
        private ILocationManager _locationManager;
        private BundlesBackgroundDownloaderManager _bundlesBackgroundDownloaderManager;

        private CancellationTokenSource _cancellationTokenSource;
        private bool _periodicFetchRunning;


        private IWalletTransactionController WalletTransactionController => _walletManager.TransactionController;
        private IPlayer Player => _playerManager.Player;

        private readonly Dictionary<string, RaceRemoteData> _remoteRaceData = new();
        public HashSet<RaceEvent> LastShownEvents { get; } = new();

        public void ResetTracking()
        {
            LastShownEvents.Clear();
        }

        void IContextInitializable.InitializeByContext(IContext context)
        {
            _context = context;
            _config = context.Resolve<IConfig>();
            _playerManager = context.Resolve<IPlayerManager>();
            _localizationManager = context.Resolve<ILocalizationManager>();
            _timeManager = context.Resolve<TimeManager>();
            _lockManager = context.Resolve<ILockManager>();
            _gameEventResourceManager = context.Resolve<IGameEventResourceManager>();
            _modalsBuilder = context.Resolve<IModalsBuilder>();
            _notificationManager = context.Resolve<GameNotificationManager>();
            _accountManager = context.Resolve<IAccountManager>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _screenManager = context.Resolve<IScreensManager>();
            _walletManager = context.Resolve<IWalletManager>();
            _uiWalletManager = context.Resolve<IUIWalletManager>();
            _coroutineExecutor = context.Resolve<ICoroutineExecutor>();
            _locationManager = context.Resolve<ILocationManager>();
            _bundlesBackgroundDownloaderManager = context.Resolve<BundlesBackgroundDownloaderManager>();
            
            context.Resolve<IModalsManager>().AddToInterruptionTracking(this);
        }

        private void ClearEvents()
        {
            foreach (var (_, raceEvent) in _eventsDict)
            {
                if (raceEvent == null)
                    continue;

                raceEvent.Clear();
                raceEvent.StopCoroutine();
            }

            _eventsDict.Clear();
            _eventsList.Clear();
        }

        void IContextReleasable.ReleaseByContext(IContext context)
        {
            ClearEvents();
            Unsubscribe();
            _gameEventResourceManager.Clear();
            StopPeriodicFetch();
        }

        public void Setup()
        {
            SetupGameEventMetaConfig(_config);
            RemoteRaceEventsDataUpdatedHandler(_accountManager.LastRaceEventData);
            Subscribe();
            SetPeriodicFetchState(ConnectivityStatusManager.ConnectivityReachable);
        }

        private void Unsubscribe()
        {
            Config.OnConfigUpdated -= SetupGameEventMetaConfig;
            _accountManager.ProfileUpdated -= OnProfileUpdated;
            _accountManager.RemoteRaceEventsDataUpdated -= RemoteRaceEventsDataUpdatedHandler;
            _screenManager.OnScreenChangingStarted -= OnScreenChangingStartedHandler;
            ConnectivityStatusManager.ConnectivityChanged -= OnConnectivityChanged;
            _bundlesBackgroundDownloaderManager.UnregisterProvider(this);
        }

        private void Subscribe()
        {
            Unsubscribe();
            Config.OnConfigUpdated += SetupGameEventMetaConfig;
            _accountManager.ProfileUpdated += OnProfileUpdated;
            _accountManager.RemoteRaceEventsDataUpdated += RemoteRaceEventsDataUpdatedHandler;
            _screenManager.OnScreenChangingStarted += OnScreenChangingStartedHandler;
            ConnectivityStatusManager.ConnectivityChanged += OnConnectivityChanged;
            _bundlesBackgroundDownloaderManager.RegisterProvider(this);
        }

        private void OnConnectivityChanged(bool reachable)
        {
            if (reachable)
            {
                FetchRemoteData();
            }

            SetPeriodicFetchState(reachable);
        }

        public void OnApplicationPause(bool pauseStatus)
        {
            SetPeriodicFetchState(!pauseStatus && ConnectivityStatusManager.ConnectivityReachable);
        }

        private void SetPeriodicFetchState(bool enabled)
        {
            if (enabled)
            {
                StartPeriodicFetch();
            }
            else
            {
                StopPeriodicFetch();
            }
        }

        private void StartPeriodicFetch()
        {
            if (_periodicFetchRunning) return;
            _cancellationTokenSource = new CancellationTokenSource();
            PeriodicFetchRemoteData(_cancellationTokenSource.Token).Forget();
            _periodicFetchRunning = true;
        }

        private void StopPeriodicFetch()
        {
            if (!_periodicFetchRunning || _cancellationTokenSource == null) return;
            _cancellationTokenSource.Cancel();
            _cancellationTokenSource.Dispose();
            _cancellationTokenSource = null;
            _periodicFetchRunning = false;
        }

        private async UniTask PeriodicFetchRemoteData(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                await UniTask.Delay(SilentFetchIntervalInMillis, ignoreTimeScale: true, cancellationToken: cancellationToken);
                FetchRemoteData();
            }
        }

        public void FetchRemoteData()
        {
            foreach (var ev in _eventsList)
            {
                if (ev.Status == ResultRaceStatus.Active && ev.Joined)
                {
                    ev.TryFetchDataSilently();
                }
            }
        }

        private void OnProfileUpdated(IPlayer obj)
        {
            OnPlayerProfileUpdated(_config);
        }

        private void SetupGameEventMetaConfig(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;
            _metaConfig = config.TryGetDefaultFromDictionary<GameEventMetaConfig>();
            OnPlayerProfileUpdated(config);
        }

        public IEnumerable<RaceEvent> GetAllEvents()
        {
            return _eventsList;
        }

        private void OnPlayerProfileUpdated(IConfig config)
        {
            //Cache current remote data so we can re-apply later, as this is not saved in the event's state
            _remoteRaceData.Clear();
            foreach (var raceEvent in _eventsDict)
            {
                _remoteRaceData[raceEvent.Key] = raceEvent.Value.CachedRemoteData;
            }

            var oldEvents = new Dictionary<string, RaceEvent>(_eventsDict);
            ClearEvents();
            var eventConfigDict = config.Get<RaceGameEventConfig>();
            if (eventConfigDict == null)
            {
                BDebug.LogError(LogCat.General, "RaceGameEventConfig is missing");
                return;
            }

            var raceConfigDict = config.Get<RaceStageConfig>();
            if (raceConfigDict == null)
            {
                BDebug.LogError(LogCat.General, "RaceStageConfig is missing");
                return;
            }

            Player?.RaceEventStateProxy?.ReleaseGarbageGameEventStates(eventConfigDict);

            foreach (var kvp in eventConfigDict)
            {
                var raceEventConfig = kvp.Value;
                var raceStages = new List<RaceStage>();
                foreach (var kvpRace in raceConfigDict)
                {
                    var raceConfig = kvpRace.Value;
                    if (raceConfig.RaceEventUid == raceEventConfig.Uid)
                    {
                        raceStages.Add(new RaceStage(raceConfig));
                    }
                }

                raceStages.Sort((a, b) => a.RaceConfig.SortOrder.CompareTo(b.RaceConfig.SortOrder));

                var raceEvent = raceEventConfig.RaceEventType.TryParseToEnum<RaceEventTypes.RaceEventType>() ==
                                RaceEventTypes.RaceEventType.Collect
                    ? new DiscoRushEvent(raceEventConfig, raceStages, _notificationManager, _playerManager,
                        _config, _localizationManager, _eventDispatcher, this, this
                        , _context)
                    : new RaceEvent(raceEventConfig, raceStages, _notificationManager, _playerManager,
                        _config, _localizationManager, _eventDispatcher, this, this
                        , _context);

                _eventsDict.Add(raceEventConfig.Uid, raceEvent);
                _eventsList.Add(raceEvent);

                // Re-apply race remote data, if available
                if (_remoteRaceData.TryGetValue(raceEvent.Uid, out var remoteData))
                {
                    raceEvent.UpdateCachedRemoteData(remoteData);
                }

                if (oldEvents.TryGetValue(raceEventConfig.Uid, out var oldEvent))
                {
                    raceEvent.MigrateData(oldEvent);
                }

                // Trigger remote data update after the event is fully registered
                raceEvent.HandleRemoteDataUpdate();
            }

            _gameEventResourceManager.SetupRaceEvents(this, _eventsList);
        }

        private bool IsNotReady()
        {
            return _eventsList.Count == 0 || _remoteRaceData == null;
        }

        private RaceEvent GetRaceEvent_Internal(string raceEventUid)
        {
            if (raceEventUid.IsNullOrEmpty())
                return null;

            if (_eventsDict.TryGetValue(raceEventUid, out var ev))
                return ev;

            BDebug.LogError(LogCat.Events, $"Event {raceEventUid} was not found");

            return null;
        }

        public RaceEvent GetRaceEvent(string raceEventUid)
        {
            var ev = GetRaceEvent_Internal(raceEventUid);
            return ev != null && EventLookupFilter(ev) ? ev : null;
        }

        private bool EventLookupFilter(RaceEvent raceEvent)
        {
            return _gameEventResourceManager.AllAssetsAvailable(raceEvent.Uid) && raceEvent.IsLaunched();
        }

        public RaceEvent GetHighestPriorityEvent()
        {
            return (RaceEvent)GameEventUtils.GetHighestPriorityEvent(comparableEvent => comparableEvent is RaceEvent raceEvent
                                                                                        && EventLookupFilter(raceEvent), _eventsList);
        }

        public bool TryToShowRace(RaceEvent raceEvent, ShowMode showMode = ShowMode.Delayed, RaceInfoOpeningReason openingReason = RaceInfoOpeningReason.Start,
            int ownPreviousRaceRank = -1)
        {
            var highestPassedLevel = _accountManager.Profile.HighestPassedLevelId;
            var highestPassedSortOrder = LevelHelper.GetLevelSortOrder(_config, highestPassedLevel);
            raceEvent.ProcessState(highestPassedSortOrder);

            if (raceEvent.LeaveRaceInProgress)
                return false;
            if (!raceEvent.NotExpiredByTimeYet())
                return false;
            if (raceEvent.IsPrizeClaimed())
                return false;

            if (raceEvent.Joined)
            {
                if (!ConnectivityStatusManager.ConnectivityReachable)
                    return false;

                raceEvent.TryFetchRaceRemoteData(null);
                var modalType = GetRaceEventModal(raceEvent);
                ShowRaceEventModal(modalType, raceEvent, showMode);
                return true;
            }

            if (!raceEvent.WithinTimeSpan())
                return false;

            ShowInfoModal(openingReason, raceEvent, showMode, ownPreviousRaceRank);
            return true;
        }

        public void ShowRaceInfo(RaceEvent raceEvent, Action onHide)
        {
            GenericTutorialModel tutorialModel = raceEvent.RaceEventType switch
            {
                RaceEventTypes.RaceEventType.Simple => new RaceEventTutorialModel(),
                RaceEventTypes.RaceEventType.Streak => new FoodEventTutorialModel(),
                RaceEventTypes.RaceEventType.Collect => new DiscoRushTutorialModel(),
                _ => throw new ArgumentOutOfRangeException()
            };

            tutorialModel.SetupHideCallback(TutorialModalHiddenHandler);
            _modalsBuilder.CreateModalView<GenericTutorialModalController>(ModalsType.GenericTutorialModal).SetupAndShow(tutorialModel);
            return;

            void TutorialModalHiddenHandler()
            {
                onHide?.Invoke();
            }
        }

        private static ModalsType GetRaceEventModal(RaceEvent raceEvent)
        {
            var modalType = raceEvent?.RaceEventType switch
            {
                RaceEventTypes.RaceEventType.Simple => ModalsType.RaceEventCompetition,
                RaceEventTypes.RaceEventType.Streak => ModalsType.FoodEventCompetition,
                RaceEventTypes.RaceEventType.Collect => ModalsType.DiscoEventCompetition,
                _ => throw new ArgumentOutOfRangeException()
            };

            return modalType;
        }

        private void ShowRaceEventModal(ModalsType modalType, RaceEvent raceEvent, ShowMode showMode)
        {
            LastShownEvents.Add(raceEvent);
            var ctrl = modalType switch
            {
                ModalsType.RaceEventCompetition => _modalsBuilder.CreateModalView<RaceEventModalController>(modalType),
                ModalsType.FoodEventCompetition => _modalsBuilder.CreateModalView<FoodCarnivalModalController>(modalType),
                ModalsType.DiscoEventCompetition => _modalsBuilder.CreateModalView<DiscoRushModalController>(modalType),
                _ => null
            };

            if (ctrl == null)
                return;

            ctrl.Setup(raceEvent);
            ctrl.ShowModal(showMode);
        }

        private void ShowInfoModal(RaceInfoOpeningReason openingReason, RaceEvent raceEvent, ShowMode showMode = ShowMode.Delayed, int ownPreviousRaceRank = -1)
        {
            LastShownEvents.Add(raceEvent);
            var modalType = GetRaceEventModal(raceEvent);

            BaseRaceEventIntroModel raceEventIntroModel = raceEvent.RaceEventType switch
            {
                RaceEventTypes.RaceEventType.Simple => new RaceEventIntroModel(openingReason, raceEvent, ownPreviousRaceRank, CloseCallback),
                RaceEventTypes.RaceEventType.Streak => new FoodEventIntroModel(openingReason, raceEvent, ownPreviousRaceRank, CloseCallback),
                RaceEventTypes.RaceEventType.Collect => new DiscoRushEventIntroModel(openingReason, raceEvent, ownPreviousRaceRank, DiscoRushCallback),
                _ => throw new ArgumentOutOfRangeException()
            };

            _modalsBuilder.CreateModalView<GenericInfoModalController>(ModalsType.GenericInfoModal).SetupAndShow(raceEventIntroModel, showMode);
            return;

            void DiscoRushCallback(bool success, RaceEvent usedRaceEvent, Action closeInfoModal)
            {
                if (!success)
                    return;

                switch (openingReason)
                {
                    case RaceInfoOpeningReason.Start:
                        if (modalType == ModalsType.DiscoEventCompetition)
                        {
                            if (!ConnectivityStatusManager.ConnectivityReachable) return;

                            usedRaceEvent.TryJoinRace((joined, _) =>
                            {
                                closeInfoModal?.Invoke();
                                CollectEventInitialSetup(joined, raceEvent, () => { ShowRaceEventModal(modalType, raceEvent, showMode); });
                            });
                        }

                        break;
                    case RaceInfoOpeningReason.Play:
                        closeInfoModal?.Invoke();
                        EnterLevelFlow();
                        break;
                }
            }

            void CloseCallback(bool success, RaceEvent usedRaceEvent, Action closeInfoModal)
            {
                closeInfoModal?.Invoke();

                if (!success)
                    return;

                switch (openingReason)
                {
                    case RaceInfoOpeningReason.Start:
                        if (!ConnectivityStatusManager.ConnectivityReachable) return;

                        usedRaceEvent.TryJoinRace(null);
                        ShowRaceEventModal(modalType, raceEvent, showMode);
                        break;
                    case RaceInfoOpeningReason.Play:
                        EnterLevelFlow();
                        break;
                }
            }
        }

        public void ClaimRewardFor(RaceEvent raceEvent)
        {
            var reward = raceEvent.ClaimReward();

            if (!RewardsUtility.IsRewardValid(reward) || raceEvent.LeaveRaceInProgress) return;

            raceEvent.MarkPrizeClaimed();

            var ownPlaceIndex = raceEvent.GetOwnPlace() - 1;
            var visualConfig = _gameEventResourceManager.GetGenericAsset<RaceEventVisualConfig>(raceEvent.EventResourceId, GameEventResKeys.RaceGameEventSettings);

            var analyticsData = raceEvent.GetCurrencyFlowAnalyticsData();
            var rewardDictionary = new Dictionary<string, long>();

            foreach (var kv in reward)
            {
                rewardDictionary[kv.Key] = kv.Value;
            }

            var transaction = new Transaction()
                .AddTag(TransactionTag.GameEvent)
                .SetAnalyticsData(analyticsData.Category, analyticsData.Family, analyticsData.Item)
                .Earn(rewardDictionary);

            WalletTransactionController.MakeTransaction(transaction);

            var rewardModal = _modalsBuilder.CreateModalView<CurrenciesRewardModalController>(ModalsType.CurrenciesRewardModal);
            var raceLeft = false;

            raceEvent.HandleClaimReward(success => raceLeft = success);

            rewardModal.SetupInitialParams(
                new CurrenciesRewardViewModel()
                {
                    RewardDict = reward,
                    SubtitleText = EndlessTreasure.RewardSubtitle,
                    RewardViewOverride =
                        new RewardViewOverride()
                        {
                            IconPrefab = visualConfig.GetViewOverride(ownPlaceIndex),
                        }
                }, skippedCurrencies =>
                {
                    _uiWalletManager.VisualizeAllTransactionsWithTag(TransactionTag.GameEvent, skippedCurrencies,
                        () =>
                        {
                            _coroutineExecutor.StartCoroutine(
                                CoroutineUtils.WaitWhileRoutine(() => !raceLeft, () => TryToShowRace(raceEvent)));
                        });
                });

            rewardModal.ShowModal(ShowMode.Immediate);
        }

        public void CollectEventInitialSetup(bool joined, RaceEvent raceEvent, Action close)
        {
            if (!joined) return;
            if (raceEvent.RaceEventType != RaceEventTypes.RaceEventType.Collect) return;

            const string rewardTitle = "DISCORUSH_ENTERED_REWARD_INITIAL_GIVEN";

            var reward = RewardsUtility.RewardStringToDict(raceEvent.JoinRewards).FilterRewards();

            var analyticsData = raceEvent.GetCurrencyFlowAnalyticsData();
            var transaction = new Transaction()
                .AddTag(TransactionTag.GameEvent)
                .SetAnalyticsData(analyticsData.Category, analyticsData.Family, analyticsData.Item)
                .Earn(reward);
            WalletTransactionController.MakeTransaction(transaction);

            var currentScreen = _screenManager.GetCurrentScreenType();
            if (_screenManager.IsTransitionInProgress || (currentScreen & ScreenType.FullHudScreen) == 0)
                return;

            var rewardModal = _modalsBuilder.CreateModalView<CurrenciesRewardModalController>(ModalsType.CurrenciesRewardModal);

            rewardModal.SetupInitialParams(
                new CurrenciesRewardViewModel
                {
                    RewardDict = reward,
                    SubtitleText = rewardTitle,
                    TitleText = rewardTitle,
                }, _ => { close?.Invoke(); });

            rewardModal.ShowModal(ShowMode.Immediate);
        }

        public void HandleLoss(RaceEvent raceEvent)
        {
            raceEvent.HandleLoss();
        }

        public void EnterLevelFlow()
        {
            var currentScreen = _screenManager.GetCurrentScreenType();
            if ((currentScreen & ScreenType.FullHudScreen) == 0)
                return;

            _eventDispatcher.TriggerEvent(_eventDispatcher.GetMessage<EventFlowRequestedEvent>());
        }

        float IRaceDataProvider.GetHighestLevelSortOrder()
        {
            var highestPassedLevel = _accountManager.Profile.HighestPassedLevelId;
            if (string.IsNullOrWhiteSpace(highestPassedLevel))
            {
                return 0f;
            }

            var highestPassedSortOrder = LevelHelper.GetLevelSortOrder(_config, highestPassedLevel);
            return highestPassedSortOrder;
        }

        public void ProcessEventStates()
        {
            if (IsNotReady())
                return;

            var highestPassedLevel = _accountManager.Profile.HighestPassedLevelId;

            if (string.IsNullOrWhiteSpace(highestPassedLevel) || _lockManager.IsLocked(GameEventManager.WeeklyStoriesLockKey, LockItemType.DailyTour)) return;

            var highestPassedSortOrder = LevelHelper.GetLevelSortOrder(_config, highestPassedLevel);

            foreach (var gameEvent in _eventsList)
            {
                gameEvent.ProcessState(highestPassedSortOrder);
            }
        }

        DateTime ISchedulableDataProvider.GetCurrentUtcDateTime()
        {
            return _timeManager.GetCurrentDateTime().AddSeconds(_metaConfig.DebugLocalTimeOffsetInSeconds);
        }

        int ISchedulableDataProvider.TotalRelativeDuration => 0;

        int ISchedulableDataProvider.GetTotalPrecedingDuration(int relativeSortIndex)
        {
            return 0;
        }

        public DateTime GetRelativeTimelineStart()
        {
            var relativeTimelineStart = _metaConfig.RelativeTimelineStart;
            return new DateTime(relativeTimelineStart.Value.Year, relativeTimelineStart.Value.Month,
                relativeTimelineStart.Value.Day, _metaConfig.UtcHourToStart, 0, 0);
        }

        public (int hour, int minute) GetUtcHourAndMinuteToStart()
        {
            return (_metaConfig.UtcHourToStart, _metaConfig.UtcMinuteToStart);
        }

        public void IncrementScores(string eventUid, int score)
        {
            if (!_eventsDict.TryGetValue(eventUid, out var ev)) return;

            ev.AddScore(score);
            ev.SubmitScore(null);
        }

        public void DebugSetScore(string eventUid, int score)
        {
            if (!_eventsDict.TryGetValue(eventUid, out var ev)) return;

            ev.DebugSetScore(score);
            ev.SubmitScore(null);
        }

        public void DebugReleaseEvent(string eventUid)
        {
            if (_eventsDict.TryGetValue(eventUid, out var ev))
            {
                ev.LeaveRace(_ => { ev.Release(); });
            }
        }

        public IEnumerable<(string raceEventUid, string stageUid, int score)> GetLastDeltaScoreTuples()
        {
            foreach (var ev in _eventsList)
            {
                if (IsRaceEventActive(ev.Uid) && ev.LastAddedScoreDeltaToShow > 0)
                {
                    yield return (ev.Uid, ev.GetRaceStageUid(), ev.LastAddedScoreDeltaToShow);
                }
            }
        }

        private bool IsRaceEventActive(string raceEventUid)
        {
            if (!ConnectivityStatusManager.ConnectivityReachable) return false;

            var raceEvent = GetRaceEvent(raceEventUid);

            return raceEvent != null
                   && raceEvent.IsLaunched()
                   && raceEvent.Status == ResultRaceStatus.Active
                   && raceEvent.Joined;
        }

        public void ClearLastDeltaScores()
        {
            foreach (var ev in _eventsList)
            {
                ev.ClearLastAddedScoreDeltaToShow();
            }
        }

        public IEnumerable<(INotifiableEvents gameEvent, DateTime dateTime)> GetFutureNotifiableEvents(Func<RaceEvent, DateTime> timeSelector)
        {
            var currentDateTime = _timeManager.GetCurrentDateTime();

            foreach (var raceEvent in _eventsList)
            {
                var characteristicTime = timeSelector(raceEvent);
                if (characteristicTime >= currentDateTime)
                {
                    yield return (raceEvent, characteristicTime);
                }
            }
        }

        private void RemoteRaceEventsDataUpdatedHandler(BCUserRaceData[] raceDtos)
        {
            ProcessEventStates();

            if (raceDtos is not { Length: > 0 }) return;

            foreach (var raceDto in raceDtos)
            {
                if (raceDto.uid == null)
                {
                    BDebug.LogError(LogCat.General, "raceDto has null Uid");
                    continue;
                }

                if (_eventsDict.TryGetValue(raceDto.uid, out var raceEvent))
                {
                    raceEvent.UpdateRemoteData(raceDto);
                }
            }

            ProcessEventStates();
        }

        private void OnScreenChangingStartedHandler(ScreenType type, IScreensController controller)
        {
            ProcessEventStates();
        }

        public IGameEventWithResources GetGameEvent(string uid)
        {
            return GetRaceEvent_Internal(uid);
        }

        public IEnumerable<IGameEventWithResources> GetGameEventsWithResource(string resourceId)
        {
            foreach (var e in _eventsDict.Values)
            {
                if (e != null && e.EventResourceId == resourceId)
                {
                    yield return e;
                }
            }
        }

        public List<BackgroundDownloadData> GetBundlesToPredownload()
        {
            List<BackgroundDownloadData> bundles = null;
            foreach (var gameEvent in _eventsList)
            {
                if (gameEvent.BundlesLoadLevelUid.IsNullOrEmpty() || _locationManager.GetLevelStage(gameEvent.BundlesLoadLevelUid) <= 0) continue;

                var eventBundles = _gameEventResourceManager.GetBundleNamesForEvent(gameEvent.EventResourceId);
                if (eventBundles == null || eventBundles.Count == 0) continue;

                bundles ??= new List<BackgroundDownloadData>();
                foreach (var bundle in eventBundles)
                {
                    if (bundle.IsNullOrEmpty()) continue;
                    bundles.Add(new BackgroundDownloadData { Priority = BackgroundDownloadPriority.Normal, Name = bundle });
                }
            }

            return bundles;
        }
    }
}