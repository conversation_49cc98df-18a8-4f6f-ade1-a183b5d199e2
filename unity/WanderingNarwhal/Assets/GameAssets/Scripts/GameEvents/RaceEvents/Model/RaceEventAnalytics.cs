using System.Collections.Generic;
using BBB.Core.Analytics;

namespace BBB.RaceEvents
{
    public static class AnalyticNames
    {
        public const string Category = "race_event";
        public const string RaceStart = "race_start";
        public const string RaceEnd = "race_end";
        public const string EventEnd = "event_end";

        public const string Rank = "rank";
        public const string IsWin = "result";
        public const string LevelsPassed = "level_passed";
        public const string RaceTime = "race_time";
        public const string RaceCompleted = "race_completed";
        public const string BotCount = "bot_no";
    }

    public class RaceEventAnalyticsRaceStarted : BaseEvent
    {
        public RaceEventAnalyticsRaceStarted(int botCount, string raceEventUid)
        {
            var data = new Dictionary<string, object>
            {
                {GenericTaxonomyProperties.Category, AnalyticNames.Category},
                {GenericTaxonomyProperties.Family, raceEventUid},
                {GenericTaxonomyProperties.Item, AnalyticNames.RaceStart},
                {AnalyticNames.BotCount, botCount},
            };
            Initialize(CurrencyFlow.GameEvents.Name, data);
        }
    }

    public class RaceEventAnalyticsRaceEnded : BaseEvent
    {
        public RaceEventAnalyticsRaceEnded(int rank, bool won, int levelPassed, int raceDurationInSec, int botCount, string raceEventUid)
        {
            var data = new Dictionary<string, object>()
            {
                {GenericTaxonomyProperties.Category, AnalyticNames.Category},
                {GenericTaxonomyProperties.Family, raceEventUid},
                {GenericTaxonomyProperties.Item, AnalyticNames.RaceEnd},
                {AnalyticNames.Rank, rank},
                {AnalyticNames.IsWin, won},
                {AnalyticNames.LevelsPassed, levelPassed},
                {AnalyticNames.RaceTime, raceDurationInSec},
                {AnalyticNames.BotCount, botCount},
            };
            Initialize(CurrencyFlow.GameEvents.Name, data);
        }
    }

    public class RaceEventAnalyticsEventReleased : BaseEvent
    {
        public RaceEventAnalyticsEventReleased(int raceCount, string raceEventUid)
        {
            var data = new Dictionary<string, object>()
            {
                {GenericTaxonomyProperties.Category, AnalyticNames.Category},
                {GenericTaxonomyProperties.Family, raceEventUid},
                {GenericTaxonomyProperties.Item, AnalyticNames.EventEnd},
                {AnalyticNames.RaceCompleted, raceCount},
            };
            Initialize(CurrencyFlow.GameEvents.Name, data);
        }
    }
}