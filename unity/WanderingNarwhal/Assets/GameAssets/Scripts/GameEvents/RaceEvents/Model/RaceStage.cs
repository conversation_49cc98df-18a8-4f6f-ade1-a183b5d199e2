using System.Collections.Generic;
using BBB.Core;
using FBConfig;

namespace BBB.RaceEvents
{
    public class RaceStage
    {
        private readonly List<Dictionary<string, int>> _rewards;

        public string IntroTextId => RaceConfig.IntroTextId;
        public string LossMainModalTextId => RaceConfig.LossMainModalTextId;
        public string MainModalTextId => RaceConfig.MainModalTextId;
        public string NameTextId => RaceConfig.NameTextId;
        public string Uid => RaceConfig.Uid;
        public int RewardsCount => _rewards.Count;
        public int ScoreGoal => RaceConfig.RoundTimeLimit > 0 ? int.MaxValue : RaceConfig.ScoreGoal;
        public int RoundTimeLimit => RaceConfig.RoundTimeLimit;
        public int RoundCoolDown => RaceConfig.RoundCoolDown;
        
        public RaceStageConfig RaceConfig { get; }

        public RaceStage(RaceStageConfig raceConfig)
        {
            RaceConfig = raceConfig;
            _rewards = new List<Dictionary<string, int>> { RewardsUtility.RewardStringToDict(raceConfig.FirstPlaceReward) };

            if (!raceConfig.SecondPlaceReward.IsNullOrEmpty())
            {
                _rewards.Add(RewardsUtility.RewardStringToDict(raceConfig.SecondPlaceReward));
            }

            if (!raceConfig.ThirdPlaceReward.IsNullOrEmpty())
            {
                _rewards.Add(RewardsUtility.RewardStringToDict(raceConfig.ThirdPlaceReward));
            }
        }

        public string GetVictoryTextId(int placeIndex)
        {
            return placeIndex switch
            {
                0 => RaceConfig.FirstPlaceVictoryMainModalTextId,
                1 => RaceConfig.SecondPlaceVictoryMainModalTextId,
                2 => RaceConfig.ThirdPlaceVictoryMainModalTextId,
                _ => null
            };
        }

        public bool HasRewardAt(int placeIndex)
        {
            if (placeIndex >= 0 && placeIndex < _rewards.Count)
            {
                var rewardDict = _rewards[placeIndex];
                return rewardDict is { Count: > 0 };
            }

            return false;
        }

        public Dictionary<string, int> GetRewardAt(int placeIndex)
        {
            if (placeIndex >= 0 && placeIndex < _rewards.Count)
            {
                return _rewards[placeIndex].FilterRewards();
            }

            BDebug.LogError(LogCat.General, RaceConfig.Uid +
                                            " : reward not found for place index " + placeIndex);
            return null;
        }
    }
}