using System;
using System.Collections.Generic;
using BebopBee.Core.UI;
using GameAssets.Scripts.Map.UI.Controllers;

namespace BBB.RaceEvents
{
    public interface IRaceEventManager : IInterruptionTracker
    {
        HashSet<RaceEvent> LastShownEvents { get; } 
        RaceEvent GetRaceEvent(string eventUid);
        RaceEvent GetHighestPriorityEvent();
        IEnumerable<RaceEvent> GetAllEvents();
        bool TryToShowRace(RaceEvent raceEvent, ShowMode showMode = ShowMode.Delayed, RaceInfoOpeningReason openingReason = RaceInfoOpeningReason.Start, int ownPreviousRaceRank = -1);
        void ShowRaceInfo(RaceEvent raceEvent, Action onHide);
        void ClaimRewardFor(RaceEvent raceEvent);
        void HandleLoss(RaceEvent raceEvent);
        void IncrementScores(string eventUid, int score);
        void DebugSetScore(string eventUid, int score);
        void DebugReleaseEvent(string eventUid);
        IEnumerable<(string raceEventUid, string stageUid, int score)> GetLastDeltaScoreTuples();
        void ClearLastDeltaScores();
        IEnumerable<(INotifiableEvents gameEvent, DateTime dateTime)> GetFutureNotifiableEvents(Func<RaceEvent, DateTime> timeSelector);
        void EnterLevelFlow();
        void CollectEventInitialSetup(bool status, RaceEvent raceEvent, Action close);
    }
}