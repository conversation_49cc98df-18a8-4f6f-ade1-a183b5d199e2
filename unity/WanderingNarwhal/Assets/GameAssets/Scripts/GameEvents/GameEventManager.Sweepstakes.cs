using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.Generic.Modal;
using BBB.UI;
using BebopBee.Core.UI;
using Cysharp.Threading.Tasks;
using FBConfig;
using GameAssets.Scripts.Core;
using UnityEngine;

namespace BBB
{
    public partial class GameEventManager
    {
        public SweepStakesGameEventConfig SweepStakesGameEventConfig => _sweepstakesConfig;
        
        private SweepStakesGameEventConfig _sweepstakesConfig;
        private readonly HashSet<SweepstakesVideoConfig> _sweepstakesVideoConfigs = new();

        private void SetupSweepstakesConfig(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;
            _sweepstakesConfig = config.TryGetDefaultFromDictionary<SweepStakesGameEventConfig>();

            var sweepstakesVideoConfigs = config.Get<SweepstakesVideoConfig>();
            _sweepstakesVideoConfigs.Clear();
            for (var i = 0; i < _sweepstakesConfig.WinnersVideoIdsLength; i++)
            {
                var videoId = _sweepstakesConfig.WinnersVideoIds(i);
                if (!sweepstakesVideoConfigs.TryGetValue(videoId, out var videoConfig))
                {
                    BDebug.LogError(LogCat.Config, $"No video found with {videoId} in {nameof(SweepstakesVideoConfig)}");
                    continue;
                }
                _sweepstakesVideoConfigs.Add(videoConfig);
            }
        }
        
        private void AutoShowSweepstakesGameEventModal(SweepstakesGameEvent sweepstakesGameEvent)
        {
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.AutoPopups.Name, DauInteractions.AutoPopups.GameEvent, sweepstakesGameEvent.Uid));
            DauInteractions.TapOnAutoPopups.AwaitLogs(DauInteractions.TapOnAutoPopups.Name, DauInteractions.TapOnAutoPopups.GameEventClose);
            ShowSweepstakesGameEvent(sweepstakesGameEvent).Forget();
        }

        private async UniTask ShowSweepstakesMilestoneModalAsync(SweepstakesGameEvent sweepstakesGameEvent, int bannerIndex = 0, ShowMode showMode = ShowMode.Delayed, bool checkForConnection = true)
        {
            if (checkForConnection && !ConnectivityStatusManager.ConnectivityReachable)
            {
                ShowNoConnectionModal(_ => ShowSweepstakesMilestoneModalAsync(sweepstakesGameEvent, bannerIndex, showMode, false).Forget());
                return;
            }

            var milestoneModal = _modalsBuilder.CreateModalView<SweepstakesMilestoneModalController>(ModalsType.SweepstakesMilestoneModal);
            await milestoneModal.Setup(_sweepstakesConfig, _sweepstakesVideoConfigs, sweepstakesGameEvent, bannerIndex, shouldGoToNextLevel =>
            {
                if (shouldGoToNextLevel)
                {
                    GoToNextGameEventLevel();
                }
            });
            milestoneModal.ShowModal(showMode);
        }

        public void ShowSweepstakesMilestoneRewardModal(SweepstakesGameEvent sweepstakesGameEvent, Transform rewardItem, Action onHide)
        {
            _modalsManager.HideAllModals();

            if (ConnectivityStatusManager.ConnectivityReachable)
            {
                ShowRewardModalAsync().Forget();
            }
            else
            {
                ShowNoConnectionModal(_ => ShowRewardModalAsync().Forget());
            }

            return;

            async UniTask ShowRewardModalAsync()
            {
                var milestoneRewardModal = _modalsBuilder.CreateModalView<SweepstakesMilestoneRewardController>(ModalsType.SweepstakesMilestoneRewardModal);

                var currentMilestoneIndex = sweepstakesGameEvent.GetCurrentMilestoneIndex();
                var milestone = sweepstakesGameEvent.GetMilestoneByIndex(currentMilestoneIndex);
                if (milestone.GetRelativeScore(sweepstakesGameEvent.CurrentScore) >= milestone.Goal)
                {
                    currentMilestoneIndex++;
                }

                var spriteKey = $"{GameEventResKeys.MilestoneIcon}_{currentMilestoneIndex}";
                var sprite = await _gameEventResourceManager.GetSpriteAsync(sweepstakesGameEvent.EventResourceId, spriteKey);
                
                if (sprite == null)
                {
                    BDebug.LogError(LogCat.Resources,$"Couldn't load milestone icon '{spriteKey}' for event {sweepstakesGameEvent.EventResourceId}");
                }

                milestoneRewardModal.Setup(rewardItem, currentMilestoneIndex, sprite,
                    () =>
                    {
                        onHide?.Invoke();
                        ShowSweepstakesMilestoneModalAsync(sweepstakesGameEvent, checkForConnection: false).Forget();
                    });
                milestoneRewardModal.ShowModal();
            }
        }

        private void ShowNoConnectionModal(Action<int> onClose)
        {
            var noConnectionModalController = _modalsBuilder.CreateModalView<NoConnectionModalController>(ModalsType.NoConnectionModal);
            noConnectionModalController.SetupWithOkButton(string.Empty, //for default no connection title
                LocalizationManager.GetLocalizedText(SweepStakesGameEventConfig.NoConnectionMessageId),
                onClose);
            noConnectionModalController.ShowModal();
        }
        
        public async UniTask ShowSweepstakesGameEvent(SweepstakesGameEvent sweepstakesGameEvent, ShowMode showMode = ShowMode.Delayed)
        {
            var gameEventModalController = _modalsBuilder.CreateModalView<SweepstakesGameEventModalController>(ModalsType.SweepstakesEventModal);
            await gameEventModalController.Setup(sweepstakesGameEvent, shouldGoToNextLevel =>
            {
                if (shouldGoToNextLevel)
                {
                    GoToNextGameEventLevel();
                }
            });
            gameEventModalController.ShowModal(showMode);
        }
    }
}