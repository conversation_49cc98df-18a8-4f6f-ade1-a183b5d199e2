using System;
using System.Collections.Generic;
using System.Threading;
using BBB.BrainCloud;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.Core.AssetBundles;
using BBB.Core.Wallet;
using BBB.DI;
using BBB.EndGameEvents;
using BBB.Modals;
using BBB.Narrative.Controllers;
using BBB.RaceEvents;
using BBB.UI;
using BBB.UI.Core;
using BBB.Wallet;
using Bebopbee.Core.Systems.RpcCommandManager;
using Bebopbee.Core.Systems.RpcCommandManager.Social;
using BebopBee;
using BebopBee.Core;
using BebopBee.Core.UI;
using Core.Configs;
using Cysharp.Threading.Tasks;
using FBConfig;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.Core.TimeManager;
using GameAssets.Scripts.CurrenciesRewardModalUI;
using GameAssets.Scripts.GenericModals.Tutorial;
using GameAssets.Scripts.GenericModals.Tutorial.Core;
using GameAssets.Scripts.Narrative.Controllers;
using GameAssets.Scripts.SocialScreens.Teams;
using PBGame;
using RPC.Command;
using UnityEngine;

namespace BBB
{
    /// <summary>
    /// Central GameEventManager living in the app context
    /// </summary>
    public partial class GameEventManager : IGameEventManager, IContextInitializable, IContextReleasable, IGlobalGameEventDataProvider,
        IGlobalGameEventChangesNotifier, IGameEventProvider, IBundlePredownloadProvider
    {
        private static IGameEventManager _instance;
        private static readonly Type[] RequiredConfigs =
        {
            typeof(GameEventConfig),
            typeof(GameEventMetaConfig),
            typeof(SweepStakesGameEventConfig)
        };

        private const int SilentFetchIntervalInMillis = 60000; // 60 seconds, e.g. 1 minute

        public const string WeeklyStoriesLockKey = "weeklystories";

        private const string GameEventOverrideKey = "GameEventOverride";
        //time for ticks
        private const float TickTimeInternal = 1f;

        //this event fires when the victory reward is claimed to notify other systems about it
        public event Action<GameEventBase> OnGameEventClaimedAfterAccomplish;

        //this event fires at the moment when game event time expires while event is already in the active status (launched but not won)
        public event Action<GameEventBase> OnGameEventExpired;

        public event Action<GameEventBase> OnGameEventLaunched;

        //overall duration of all of the Relative scheduled events
        int ISchedulableDataProvider.TotalRelativeDuration => _totalRelativeDuration;
        public int TimeBeforeEventEndNotification => _metaConfig.TimeBeforeEventEndNotification;

        private IAccountManager _accountManager;
        private IConfig _config;
        private IDictionary<string, ProgressionLevelConfig> _levelConfigs;
        private IScreensManager _screensManager;
        private IPlayerManager _playerManager;
        private ILocationManager _locationManager;
        private TimeManager _timeManager;
        private IGameEventResourceManager _gameEventResourceManager;
        private BundlesBackgroundDownloaderManager _bundlesBackgroundDownloaderManager;

        private GameEventMetaConfig _metaConfig;
        private readonly IDictionary<string, GameEventBase> _eventsDict = new Dictionary<string, GameEventBase>();
        private readonly List<GameEventBase> _eventsList = new();
        private readonly GameEventPriorityComparer _priorityComparer = new();

        private int _totalRelativeDuration;
        private float _lastTimeTicked;
        private IModalsBuilder _modalsBuilder;
        private IEventDispatcher _eventDispatcher;
        private IModalsManager _modalsManager;
        private IWalletManager _walletManager;
        private IUIWalletManager _uiWalletManager;
        private bool _participatedInLottery;
        private IContext _context;

        private CancellationTokenSource _cancellationTokenSource;
        private bool _periodicFetchRunning;
        private bool _appPaused;

        private IWalletTransactionController WalletTransactionController => _walletManager.TransactionController;
        private IPlayer Player => _playerManager.Player;
        public HashSet<GameEventBase> LastShownEvents { get; } = new();

        public void ResetTracking()
        {
            LastShownEvents.Clear();
        }

        void IContextInitializable.InitializeByContext(IContext context)
        {
            _walletManager = context.Resolve<IWalletManager>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _accountManager = context.Resolve<IAccountManager>();
            _screensManager = context.Resolve<IScreensManager>();
            _locationManager = context.Resolve<ILocationManager>();
            _modalsBuilder = context.Resolve<IModalsBuilder>();
            _modalsManager = context.Resolve<IModalsManager>();
            _playerManager = context.Resolve<IPlayerManager>();
            _uiWalletManager = context.Resolve<IUIWalletManager>();
            SocialManager = context.Resolve<ISocialManager>();
            _timeManager = context.Resolve<TimeManager>();
            _config = context.Resolve<IConfig>();
            _levelConfigs = _config.Get<ProgressionLevelConfig>();
            _gameEventResourceManager = context.Resolve<IGameEventResourceManager>();
            _bundlesBackgroundDownloaderManager = context.Resolve<BundlesBackgroundDownloaderManager>();
            _context = context;
            _instance = this;
            
            _modalsManager.AddToInterruptionTracking(this);
        }

        public void Setup()
        {
            SetupGameEventMetaConfig(_config);
            SetupSweepstakesConfig(_config);
            RemoteEventsDataUpdatedHandler(_accountManager.LastGameEventData);
            Subscribe();
            SetPeriodicFetchState();
        }

        private void Unsubscribe()
        {
            Config.OnConfigUpdated -= SetupGameEventMetaConfig;
            Config.OnConfigUpdated -= SetupSweepstakesConfig;
            _accountManager.ProfileUpdated -= OnProfileUpdated;
            _accountManager.RemoteEventsDataUpdated -= RemoteEventsDataUpdatedHandler;
            _screensManager.OnScreenChangingStarted -= OnScreenChangingStartedHandler;
            _screensManager.OnScreenChanged -= OnScreenChanged;
            ConnectivityStatusManager.ConnectivityChanged -= OnConnectivityChanged;
            _bundlesBackgroundDownloaderManager.UnregisterProvider(this);
            _playerManager.PlayerInventory.OnAddedItemToInventoryEvent -= OnAddedItemToInventoryHandler;
        }

        private void Subscribe()
        {
            Unsubscribe();
            Config.OnConfigUpdated += SetupGameEventMetaConfig;
            Config.OnConfigUpdated += SetupSweepstakesConfig;
            _accountManager.ProfileUpdated += OnProfileUpdated;
            _accountManager.RemoteEventsDataUpdated += RemoteEventsDataUpdatedHandler;
            _screensManager.OnScreenChangingStarted += OnScreenChangingStartedHandler;
            _screensManager.OnScreenChanged += OnScreenChanged;
            ConnectivityStatusManager.ConnectivityChanged += OnConnectivityChanged;
            _bundlesBackgroundDownloaderManager.RegisterProvider(this);
            _playerManager.PlayerInventory.OnAddedItemToInventoryEvent += OnAddedItemToInventoryHandler;
        }

        private void OnAddedItemToInventoryHandler(string uid, int amount)
        {
            if (amount <= 0 || !InventoryItems.IsGameEventScore(uid))
                return;

            var eventType = InventoryItems.GetGameplayTypeByScoreUid(uid);
            var gameEvent = GetHighestPriorityEvent(e =>
                e.Status == GameEventStatus.Active && e.GameplayType == eventType);
            if (gameEvent != null)
            {
                gameEvent.AddScore(amount);
                _playerManager.PlayerInventory.SpendItem(uid, amount);
            }
            else
            {
                BDebug.LogWarning(LogCat.Events, $"Game event {eventType} is not active");
            }
        }

        private void SetupGameEventMetaConfig(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;

            _metaConfig = config.TryGetDefaultFromDictionary<GameEventMetaConfig>();
            OnPlayerProfileUpdated(config);
        }

        private void OnProfileUpdated(IPlayer player)
        {
            OnPlayerProfileUpdated(_config);
        }

        private void OnPlayerProfileUpdated(IConfig config)
        {
            var oldEvents = new Dictionary<string, GameEventBase>(_eventsDict);
            ClearEvents();
            _totalRelativeDuration = 0;

            var gameEventConfigDict = config.Get<GameEventConfig>();
            if (gameEventConfigDict == null)
            {
                BDebug.LogError(LogCat.General, "GameEventConfig is missing");
                return;
            }

            var stateProxy = Player.GameEventStateProxy;
            stateProxy.ReleaseGarbageGameEventStates(gameEventConfigDict);

            foreach (var kvp in gameEventConfigDict)
            {
                var gameEventConfig = kvp.Value;
                //we moved weekly leaderboard config to a separate config, so we do not need to handle its legacy game event
                //this is added to skip it for now
                if (gameEventConfig.Uid == LeaderboardConstants.WeeklyLbEventUid)
                {
                    continue;
                }

                oldEvents.TryGetValue(gameEventConfig.Uid, out var oldEvent);

                var gameplayType = (GameEventGameplayType)gameEventConfig.GameplayType;
                GameEventBase gameEvent = null;
                switch (gameplayType)
                {
                    case GameEventGameplayType.Competition:
                        gameEvent = new CompetitionGameEvent(gameEventConfig, _context, this, this);
                        break;
                    case GameEventGameplayType.Collection:
                        gameEvent = new CollectionGameEvent(gameEventConfig, _context, this, this);
                        break;
                    case GameEventGameplayType.SideMap:
                        gameEvent = new SideMapGameEvent(gameEventConfig, _context, this, this, oldEvent as SideMapGameEvent);
                        break;
                    case GameEventGameplayType.EndOfContent:
                        gameEvent = new EndOfContentGameEvent(gameEventConfig, _context, this, this, oldEvent as EndOfContentGameEvent);
                        break;
                    case GameEventGameplayType.Sweepstakes:
                        gameEvent = new SweepstakesGameEvent(gameEventConfig, _context, this, this);
                        break;
                    default:
                        BDebug.LogError(LogCat.General, $"Unknown game event type {gameplayType}");
                        break;
                }

                if (gameEvent != null)
                {
                    _eventsDict.Add(gameEventConfig.Uid, gameEvent);
                    _eventsList.Add(gameEvent);
                    if (oldEvent != null)
                    {
                        gameEvent.MigrateData(oldEvent);
                        oldEvent.Reset();
                    }
                }
            }

            foreach (var gameEvent in _eventsList)
            {
                if (gameEvent.SchedulingType == SchedulingType.Relative)
                    _totalRelativeDuration += gameEvent.Duration;
            }

            _gameEventResourceManager.SetupGameEvents(this, _eventsList).Forget();
            ProcessEventStates(_accountManager.LastGameEventData);
        }

        private void ClearEvents()
        {
            foreach (var (_, gameEvent) in _eventsDict)
            {
                gameEvent.StopCoroutine();
            }

            _eventsDict.Clear();
            _eventsList.Clear();
        }

        void IContextReleasable.ReleaseByContext(IContext context)
        {
            ClearEvents();
            _gameEventResourceManager.Clear();
            Unsubscribe();
            StopPeriodicFetch();
            _instance = null;
        }

        //total duration of Relative scheduled events preceding a Relative event with certain sort index
        int ISchedulableDataProvider.GetTotalPrecedingDuration(int relativeSortIndex)
        {
            var result = 0;
            foreach (var gameEvent in _eventsList)
            {
                if (gameEvent.SchedulingType == SchedulingType.Relative &&
                    gameEvent.RelativeSortIndex < relativeSortIndex)
                    result += gameEvent.Duration;
            }

            return result;
        }

        public IEnumerable<GameEventBase> GetEvents(Predicate<GameEventBase> filter)
        {
            foreach (var ev in _eventsList)
            {
                if (filter(ev))
                {
                    yield return ev;
                }
            }
        }

        public DateTime GetCurrentUtcDateTime()
        {
            return _timeManager.GetCurrentDateTime().AddSeconds(_metaConfig.DebugLocalTimeOffsetInSeconds);
        }

        public DateTime GetRelativeTimelineStart()
        {
            var relativeTimelineStart = _metaConfig.RelativeTimelineStart;
            return new DateTime(relativeTimelineStart.Value.Year, relativeTimelineStart.Value.Month,
                relativeTimelineStart.Value.Day, _metaConfig.UtcHourToStart, _metaConfig.UtcMinuteToStart, 0);
        }

        public (int, int) GetUtcHourAndMinuteToStart()
        {
            return (_metaConfig.UtcHourToStart, _metaConfig.UtcMinuteToStart);
        }

        public bool IsInDebugMode => _metaConfig.GameEventDebugMode;

        public GameEventStateProxy GameEventStateProxy => _playerManager.Player.GameEventStateProxy;

        public Profile OwnProfile => _accountManager.Profile;

        public ISocialManager SocialManager { get; private set; }

        public GameEventMetaConfig GetMetaConfig()
        {
            return _metaConfig;
        }

        private bool IsNotReady()
        {
            return _eventsList.Count == 0;
        }

        /// <summary>
        /// processing all of the states of events, should be called before resource reloading
        /// to allow GameEventResourceManager to reload resources depending on states during the same screen transition
        /// </summary>
        public void Process()
        {
            if (IsNotReady())
                return;

            ProcessEventStates(_accountManager.LastGameEventData);

            foreach (var gameEvent in _eventsList)
                gameEvent.UpdateRemoteDataIfNeeded();

            //to restart ticking
            _lastTimeTicked = Time.time;
        }

        private void OnConnectivityChanged(bool reachable)
        {
            if (reachable)
            {
                FetchRemoteData();
            }

            SetPeriodicFetchState();
        }

        public void OnApplicationPause(bool pauseStatus)
        {
            _appPaused = pauseStatus;
            SetPeriodicFetchState();
        }

        private void SetPeriodicFetchState()
        {
            if (!_appPaused && ConnectivityStatusManager.ConnectivityReachable && ShouldFetchInCurrentScreen())
            {
                StartPeriodicFetch();
            }
            else
            {
                StopPeriodicFetch();
            }
        }

        private void StartPeriodicFetch()
        {
            if (_periodicFetchRunning) return;
            _cancellationTokenSource = new CancellationTokenSource();
            PeriodicFetchRemoteData(_cancellationTokenSource.Token).Forget();
            _periodicFetchRunning = true;
        }

        private void StopPeriodicFetch()
        {
            if (!_periodicFetchRunning || _cancellationTokenSource == null) return;
            _cancellationTokenSource.Cancel();
            _cancellationTokenSource.Dispose();
            _cancellationTokenSource = null;
            _periodicFetchRunning = false;
        }

        private async UniTask PeriodicFetchRemoteData(CancellationToken cancellationToken)
        {
            while (true)
            {
                await UniTask.Delay(SilentFetchIntervalInMillis, ignoreTimeScale: true, cancellationToken: cancellationToken);
                if (cancellationToken.IsCancellationRequested)
                {
                    return;
                }

                FetchRemoteData();
            }
        }

        private void FetchRemoteData()
        {
            foreach (var ev in _eventsList)
            {
                if (ev.Status == GameEventStatus.Active && ev is CompetitionGameEvent competitionGameEvent)
                {
                    competitionGameEvent.TryFetchDataSilently();
                }
            }
        }

        public bool ShouldShowEndAnnouncement()
        {
            if (IsNotReady() || !ConnectivityStatusManager.ConnectivityReachable)
                return false;

            var gameEventToAutoShow = GetGameEventToAutoShow();
            return gameEventToAutoShow != null && gameEventToAutoShow.ShouldEndAnnouncementBeAutoShown && AllResourcesAvailable(gameEventToAutoShow);
        }

        public bool ShouldShowStartAnnouncement()
        {
            if (IsNotReady())
                return false;

            var gameEventToAutoShow = GetGameEventToAutoShow();
            return gameEventToAutoShow is { ShouldStartAnnouncementBeAutoShown: true } && AllResourcesAvailable(gameEventToAutoShow);
        }

        public bool ShouldShowQualificationAnnouncement()
        {
            if (IsNotReady() || !ConnectivityStatusManager.ConnectivityReachable)
                return false;

            var gameEventToAutoShow = GetGameEventToAutoShow();
            return gameEventToAutoShow is { ShouldQualificationAnnouncementBeAutoShown: true } && AllResourcesAvailable(gameEventToAutoShow);
        }

        private bool AllResourcesAvailable(GameEventBase gameEvent)
        {
            return gameEvent.HasNoResources() || _gameEventResourceManager.AllAssetsAvailable(gameEvent.Uid);
        }

        private async UniTask TryReloadEventResources(GameEventBase gameEvent, Action<bool> callback = null)
        {
            if (AllResourcesAvailable(gameEvent))
            {
                callback?.Invoke(true);
                return;
            }

            await _gameEventResourceManager.TryReloadPackAsync(gameEvent.EventResourceId, _screensManager.GetCurrentScreenType(), callback);
        }

        public bool AnyFetchingData()
        {
            foreach (var gameEvent in _eventsList)
            {
                if (gameEvent is CompetitionGameEvent { IsUpdatingDataNow: true })
                    return true;
            }

            return false;
        }

        //this method is picking game event to auto show and then shows it
        public bool TryAutoShowGameEvent()
        {
            var gameEventToAutoShow = GetGameEventToAutoShow();

            foreach (var gameEvent in _eventsList)
            {
                if (gameEvent != gameEventToAutoShow)
                {
                    gameEvent.UpdateRemoteDataIfNeeded();
                }
            }

            if (gameEventToAutoShow == null) return false;

            AutoShowGameEvent(gameEventToAutoShow);
            return true;
        }

        public IEnumerable<(INotifiableEvents gameEvent, DateTime dateTime)> GetFutureNotifiableEvents(Func<GameEventBase, DateTime> timeSelector)
        {
            var currentDateTime = _timeManager.GetCurrentDateTime();

            foreach (var gameEvent in _eventsList)
            {
                var characteristicTime = timeSelector(gameEvent);
                if (characteristicTime >= currentDateTime)
                {
                    yield return (gameEvent, characteristicTime);
                }
            }
        }

        public GameEventBase GetHighestPriorityEventByStatus(GameEventStatus status)
        {
            return GetHighestPriorityEvent(Predicate);

            bool Predicate(GameEventBase gameEvent)
            {
                return gameEvent.Status == status;
            }
        }

        public GameEventBase GetHighestPriorityEvent(Predicate<GameEventBase> eventPredicate, bool skipResourceCheck = false)
        {
            return GetHighestPriorityEvent_Internal(eventPredicate, skipResourceCheck);
        }

        public GameEventBase GetHighestPriorityEvent_Internal(Predicate<GameEventBase> eventPredicate, bool skipResourceCheck = false)
        {
            return (GameEventBase)GameEventUtils.GetHighestPriorityEvent(comparableEvent => comparableEvent is GameEventBase gameEvent
                                                                                            && eventPredicate(gameEvent)
                                                                                            && IsGameEventValid(gameEvent, skipResourceCheck), _eventsList);
        }

        private bool IsGameEventValid(GameEventBase gameEvent, bool skipResourceCheck = false)
        {
            return gameEvent != null && !gameEvent.ExcludeFromLookup()
                                     && (gameEvent.HasNoResources() || skipResourceCheck || _gameEventResourceManager.AllAssetsAvailable(gameEvent.Uid));
        }

        public bool CanLabelsBeGatheredAtLevel(string levelUid)
        {
            var match3Data = GetMatch3DataFor_Internal(levelUid);
            return match3Data != null && match3Data.HasAny();
        }

        private void AutoShowGameEvent(GameEventBase gameEvent)
        {
            LastShownEvents.Add(gameEvent);
            var narrativeIds = gameEvent.CurrentNarrativeIds;
            var precedingNarrativeId = narrativeIds.Item1;
            var hasPrecedingNarrative = !precedingNarrativeId.IsNullOrEmpty();

            //if preceding narrative exists, first we show it and then show game event modal
            if (hasPrecedingNarrative)
            {
                var dialogModal = _modalsBuilder.CreateModalView<DialogModalController>(ModalsType.DialogModal);
                var specialParams = gameEvent.GetDialogueSpecialParams();
                var setupParams = new DialogModalSetupParams(precedingNarrativeId,
                    () =>
                    {
                        switch (gameEvent)
                        {
                            case CompetitionGameEvent competitionGameEvent:
                                AutoShowCompetitionGameEventModal(competitionGameEvent);
                                break;
                            case SweepstakesGameEvent sweepstakesGameEvent:
                                AutoShowSweepstakesGameEventModal(sweepstakesGameEvent);
                                break;
                            case CollectionGameEvent collectionGameEvent:
                                AutoShowCollectionGameEventModal(collectionGameEvent);
                                break;
                        }
                    }, DialogType.GameEvent, specialParams);

                dialogModal.ShowModal(ShowMode.Delayed, setupParams: setupParams);
            }
            else
            {
                switch (gameEvent)
                {
                    case SideMapGameEvent sideMapGameEvent:
                        if (gameEvent.Status == GameEventStatus.Ended)
                        {
                            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.AutoPopups.Name, DauInteractions.AutoPopups.GameEvent, gameEvent.Uid));
                            DauInteractions.TapOnAutoPopups.AwaitLogs(DauInteractions.TapOnAutoPopups.GameEventClick, DauInteractions.TapOnAutoPopups.GameEventClose);
                            ShowCompletionModal(sideMapGameEvent);
                        }
                        else
                        {
                            AutoShowCompetitionGameEventModal(sideMapGameEvent);
                        }

                        break;
                    case CompetitionGameEvent competitionGameEvent:
                        AutoShowCompetitionGameEventModal(competitionGameEvent);
                        break;
                    case SweepstakesGameEvent sweepstakesGameEvent:
                        AutoShowSweepstakesGameEventModal(sweepstakesGameEvent);
                        break;
                    case CollectionGameEvent collectionGameEvent:
                        AutoShowCollectionGameEventModal(collectionGameEvent);
                        break;
                }
            }
        }

        //this method is called to add scores to a particular game evnet if it exists
        public void IncrementScores(string eventUid, int score)
        {
            if (_eventsDict.TryGetValue(eventUid, out var gameEvent))
            {
                gameEvent.AddScore(score);
            }
            else
            {
                BDebug.LogError(LogCat.General, $"Game event {eventUid} is not found");
            }
        }

        public void DebugSetScore(string eventUid, int score)
        {
            if (_eventsDict.TryGetValue(eventUid, out var gameEvent))
            {
                gameEvent.DebugSetScore(score);
            }
            else
            {
                BDebug.LogError(LogCat.General, $"Game event {eventUid} is not found");
            }
        }

        public void DebugReleaseEvent(string eventUid)
        {
            if (_eventsDict.TryGetValue(eventUid, out var gameEvent))
            {
                gameEvent.DebugForceRelease();
            }
            else
            {
                BDebug.LogError(LogCat.General, $"Game event {eventUid} is not found");
            }
        }

        /// <summary>
        ///for all events ready to be auto shown we picked the highest priority defined by priority comparer object
        /// </summary>
        private GameEventBase GetGameEventToAutoShow()
        {
            GameEventBase highestPriorityEvent = null;
            foreach (var gameEvent in _eventsList)
            {
                bool shouldShow = gameEvent.ShouldStartAnnouncementBeAutoShown ||
                                  (ConnectivityStatusManager.ConnectivityReachable && (gameEvent.ShouldQualificationAnnouncementBeAutoShown || gameEvent.ShouldEndAnnouncementBeAutoShown));
                if (shouldShow && (highestPriorityEvent == null || _priorityComparer.Compare(gameEvent, highestPriorityEvent) < 0))
                {
                    highestPriorityEvent = gameEvent;
                }
            }

            return highestPriorityEvent;
        }

        private void RemoteEventsDataUpdatedHandler(BCUserGameEventData[] gameDtos)
        {
            ProcessEventStates(gameDtos);
        }

        private void ProcessEventStates(BCUserGameEventData[] gameDtos)
        {
            var highestPassedLevel = _accountManager.Profile.HighestPassedLevelId;
            if (!string.IsNullOrWhiteSpace(highestPassedLevel))
            {
                if (gameDtos != null)
                {
                    foreach (var gameDto in gameDtos)
                    {
                        if (gameDto.EventId == null)
                        {
                            BDebug.LogError(LogCat.General, "gameDto has null Uid");
                            continue;
                        }

                        if (_eventsDict.TryGetValue(gameDto.EventId, out var gameEvent))
                        {
                            gameEvent.UpdateRemoteData(gameDto);
                        }
                    }
                }

                foreach (var gameEvent in _eventsList)
                {
                    gameEvent.TryProcessState();
                }
            }
        }

        void IGlobalGameEventChangesNotifier.OnAccomplishClaim(GameEventBase gameEvent)
        {
            OnGameEventClaimedAfterAccomplish.SafeInvoke(gameEvent);
        }

        void IGlobalGameEventChangesNotifier.OnRelease(GameEventBase gameEvent)
        {
            OnGameEventExpired.SafeInvoke(gameEvent);
            _playerManager.MarkDirty();
            var gameEventDelayInMsecs = _metaConfig.MsecsUntilBraincloudDataUpdate;
            Rx.Invoke(gameEventDelayInMsecs / 1000f, _ =>
            {
                _accountManager.UpdateLastGameEventDataFor(gameEvent.Uid, data =>
                {
                    gameEvent.UpdateRemoteData(data);
                    gameEvent.TryProcessState();
                }, gameEvent.TryProcessState);
            });
        }

        void IGlobalGameEventChangesNotifier.OnLaunch(GameEventBase gameEvent)
        {
            TryReloadEventResources(gameEvent, _ => { OnGameEventLaunched.SafeInvoke(gameEvent); }).Forget();
        }

        public void Tick()
        {
            if (_screensManager == null)
                return;

            if ((_screensManager.GetCurrentScreenType() & ScreenType.Map) > 0)
            {
                if (Time.time - _lastTimeTicked > TickTimeInternal)
                {
                    _lastTimeTicked = Time.time;
                    
                    var utcNow = GetCurrentUtcDateTime();

                    foreach (var gameEvent in _eventsList)
                    {
                        //for all active events we check every second if they are expired to call event that will handle
                        //ui on expiration (i.e. hide hud icon)
                        var expired = gameEvent.ExpiredByTimeLeft(utcNow);
                        if (!expired && !gameEvent.ShouldBeReleased && !gameEvent.ShouldProcessForLaunch) continue;

                        ProcessEventStates(_accountManager.LastGameEventData);
                        if (expired)
                            OnGameEventExpired.SafeInvoke(gameEvent);
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// This method is called on claiming user input in order to pick
        /// appropriate reward depending on event status, register it with transaction
        /// and then show generic reward popup
        /// </summary>
        public void ClaimRewardsFor(GameEventBase gameEvent)
        {
            var rewardLocParams = gameEvent.GetCurrentRewardLocalizationParams();

            var reward = gameEvent.ClaimReward();
            TryFireExpirationEvent(gameEvent);

            if (!RewardsUtility.IsRewardValid(reward))
            {
                var message = _eventDispatcher.GetMessage<OnClaimDone>();
                message.Set(gameEvent);
                _eventDispatcher.TriggerEvent(message);
                return;
            }

            var analyticsData = gameEvent.GetCurrencyFlowAnalyticsData();

            var rewardDictionary = new Dictionary<string, long>();
            foreach (var kv in reward)
            {
                rewardDictionary[kv.Key] = kv.Value;
            }

            var transaction = new Transaction()
                .AddTag(TransactionTag.GameEvent)
                .SetAnalyticsData(analyticsData.Category, analyticsData.Family, analyticsData.Item)
                .Earn(rewardDictionary);
            WalletTransactionController.MakeTransaction(transaction);
            var gameEventRewardCollected = _eventDispatcher.GetMessage<GameEventRewardCollected>();
            gameEventRewardCollected.Set(gameEvent);
            _eventDispatcher.TriggerEvent(gameEventRewardCollected);

            var rewardModal = _modalsBuilder.CreateModalView<CurrenciesRewardModalController>(ModalsType.CurrenciesRewardModal);
            var isRewardCollected = false;

            rewardModal.SetupInitialParams(reward,
                rewardLocParams.HeaderLocKey, onHide: skippedCurrencies =>
                {
                    if (isRewardCollected) return;

                    isRewardCollected = true;

                    _uiWalletManager.VisualizeAllTransactionsWithTag(TransactionTag.GameEvent, skippedCurrencies,
                        animCallback: () =>
                        {
                            //if game event has final narrative then we show it on reward popup close
                            var finalNarrative = gameEvent.GetCurrentRewardFollowingNarrativeId();
                            var hasFinalNarrative = !finalNarrative.IsNullOrEmpty();
                            if (hasFinalNarrative)
                            {
                                var dialogModal =
                                    _modalsBuilder.CreateModalView<DialogModalController>(ModalsType.DialogModal);
                                var specialParams = gameEvent.GetDialogueSpecialParams();
                                var setupParams = new DialogModalSetupParams(finalNarrative, () =>
                                {
                                    //then we show victory popup
                                    ShowGameEventAfterReward(gameEvent, true);
                                }, DialogType.GameEvent, specialParams);

                                dialogModal.ShowModal(ShowMode.Delayed,
                                    setupParams: setupParams);
                            }
                            else
                            {
                                ShowGameEventAfterReward(gameEvent, true);
                            }
                        });
                },
                rewardLocParams.LocParams);

            rewardModal.ShowModal(ShowMode.Delayed);
        }

        private void TryFireExpirationEvent(GameEventBase gameEvent)
        {
            if (!gameEvent.ShouldShowHudIcon())
                OnGameEventExpired.SafeInvoke(gameEvent);
        }

        private void ShowGameEventAfterReward(GameEventBase gameEvent, bool afterClaim)
        {
            var victory = gameEvent.IsWon();
            if (victory)
            {
                void OnHide()
                {
                    if (!afterClaim) return;

                    var gameEventPassportHudRevealAnimationTriggerEvent = _eventDispatcher.GetMessage<GameEventPassportHudRevealAnimationTriggerEvent>();
                    gameEventPassportHudRevealAnimationTriggerEvent.Set(gameEvent.Uid, OnPassportDone);
                    _eventDispatcher.TriggerEvent(gameEventPassportHudRevealAnimationTriggerEvent);

                    return;

                    void OnPassportDone()
                    {
                        switch (gameEvent)
                        {
                            case CompetitionGameEvent competitionGameEvent:
                                OnClaimDone();
                                break;
                        }
                    }
                }

                if (gameEvent.ShowVictoryModal)
                {
                    var gameEventVictoryModel = new GameEventVictoryModel(gameEvent);
                    gameEventVictoryModel.SetupHideCallback(OnHide);

                    _modalsBuilder.CreateModalView<GenericTutorialModalController>(ModalsType.GenericTutorialModal).SetupAndShow(gameEventVictoryModel, ShowMode.Delayed);
                }
                else
                {
                    OnHide();
                }
            }
            else
            {
                if (afterClaim)
                {
                    switch (gameEvent)
                    {
                        case SideMapGameEvent:
                            OnClaimDone();
                            break;
                        case CompetitionGameEvent competitionGameEvent:
                        {
                            if (competitionGameEvent.Status == GameEventStatus.Active) //this check ensures it is the qualification reward
                                AutoShowCompetitionGameEventModal(competitionGameEvent);
                            else
                                OnClaimDone();

                            break;
                        }
                    }
                }
            }

            return;

            void OnClaimDone()
            {
                ProcessEventStates(_accountManager.LastGameEventData);
                var message = _eventDispatcher.GetMessage<OnClaimDone>();
                message.Set(gameEvent);
                _eventDispatcher.TriggerEvent(message);
            }
        }

        /// <summary>
        /// If game event is opened manually and is in final status we show victory popup
        /// otherwise just game event popup.
        /// </summary>
        /// <param name="eventUid"></param>
        /// <param name="showMode"></param>
        public void ShowEvent(string eventUid, ShowMode showMode = ShowMode.Delayed)
        {
            if (!_eventsDict.TryGetValue(eventUid, out var gameEvent) || (_screensManager.GetCurrentScreenType() & ScreenType.Map) == 0) 
                return;

            LastShownEvents.Add(gameEvent);
            var narrativeIds = gameEvent.CurrentNarrativeIds;
            var precedingNarrativeId = narrativeIds.Item1;
            var hasPrecedingNarrative = !precedingNarrativeId.IsNullOrEmpty();

            if (hasPrecedingNarrative && gameEvent.WaitingToBeNotified)
            {
                var dialogModal = _modalsBuilder.CreateModalView<DialogModalController>(ModalsType.DialogModal);
                var specialParams = gameEvent.GetDialogueSpecialParams();
                var setupParams = new DialogModalSetupParams(precedingNarrativeId,
                    () =>
                    {
                        switch (gameEvent)
                        {
                            case SweepstakesGameEvent sweepstakesEvent:
                                ShowSweepstakesMilestoneModalAsync(sweepstakesEvent, showMode: showMode).Forget();
                                break;
                            case CompetitionGameEvent competitionEvent:
                                ShowCompetitionGameEvent(competitionEvent, showMode);
                                break;
                        }
                    }, DialogType.GameEvent, specialParams);

                dialogModal.ShowModal(showMode, setupParams: setupParams);
            }
            else
            {
                switch (gameEvent)
                {
                    case CompetitionGameEvent competitionEvent:
                        ShowCompetitionGameEvent(competitionEvent, showMode);
                        break;
                    case SweepstakesGameEvent sweepstakesEvent:
                        ShowSweepstakesMilestoneModalAsync(sweepstakesEvent, showMode: showMode).Forget();
                        break;
                    case CollectionGameEvent collectionGameEvent:
                        ShowCollectionGameEvent(collectionGameEvent, showMode);
                        break;
                }
            }
        }

        public void GoToNextGameEventLevel()
        {
            _eventDispatcher.TriggerEvent(_eventDispatcher.GetMessage<EventFlowRequestedEvent>());
        }

        /// <summary>
        /// this method returns data for match3 playout related to currently active game events
        /// </summary>
        /// <param name="levelUid"></param>
        public GameEventMatch3LabelCounters GetMatch3DataFor(string levelUid)
        {
            return GetMatch3DataFor_Internal(levelUid);
        }

        private GameEventMatch3LabelCounters GetMatch3DataFor_Internal(string levelUid)
        {
            if (!_levelConfigs.TryGetValue(levelUid, out var levelConfig))
                return null;

            var levelState = _locationManager.GetLevelState(levelUid);
            if (levelState == null) //locked level, still can be played from the side map event
            {
                levelState = new LevelState { Stage = 0, SourceUid = levelUid };
            }

            var builder = GameEventMatch3LabelCounters.Builder.Start(_metaConfig, levelConfig, levelState);
            foreach (var gameEvent in _eventsList)
            {
                if (gameEvent.Status == GameEventStatus.Active)
                {
                    builder.Apply(gameEvent);
                }
            }

            return builder.Build();
        }

        public GameEventBase FindGameEventByUid(string gameEventUid)
        {
            if (!gameEventUid.IsNullOrEmpty() && _eventsDict.TryGetValue(gameEventUid, out var gameEventByUid))
            {
                return gameEventByUid;
            }

            return null;
        }

        public string GetOverrideGameEvent()
        {
            return PlayerPrefs.GetString(GameEventOverrideKey, string.Empty);
        }

        public void SetOverrideGameEvent(string value)
        {
            PlayerPrefs.SetString(GameEventOverrideKey, value);
        }

        public bool IsActiveSideMap(ScreenType screenType)
        {
            if (!ConnectivityStatusManager.ConnectivityReachable || (screenType & ScreenType.Map) == 0)
            {
                return false;
            }

            foreach (var gameEvent in _eventsList)
            {
                if (IsGameEventValid(gameEvent) && gameEvent is SideMapGameEvent { Status: GameEventStatus.Active, IntroductionAlreadyShown: true } sideMapEvent &&
                    sideMapEvent.HomeScreen == screenType)
                {
                    return true;
                }
            }

            return false;
        }

        public GameEventBase GetCurrentSideMapEvent()
        {
            var screenType = _screensManager.GetCurrentScreenType();
            if (_screensManager.IsTransitionInProgress)
            {
                screenType = _screensManager.GetTransitionTargetScreenType();
            }

            if ((screenType & ScreenType.SideMap) > 0)
            {
                return GetHighestPriorityEvent_Internal(ev =>
                    ev.GameplayType is GameEventGameplayType.SideMap or GameEventGameplayType.EndOfContent &&
                    ev.Status is GameEventStatus.Active or GameEventStatus.Ended);
            }

            return null;
        }

        public GameEventBase GetAvailableSideMapEvent()
        {
            var predicates = new List<Predicate<GameEventBase>>()
            {
                ev =>
                    ev.GameplayType is GameEventGameplayType.SideMap or GameEventGameplayType.EndOfContent &&
                    ev.Status is GameEventStatus.Active or GameEventStatus.Ended
            };

            foreach (var predicate in predicates)
            {
                var mapEvent = GetHighestPriorityEvent_Internal(predicate);
                if (mapEvent != null)
                {
                    return mapEvent;
                }
            }

            return null;
        }

        private void OnScreenChangingStartedHandler(ScreenType type, IScreensController controller)
        {
            Process();
        }

        private void OnScreenChanged(ScreenType screenType, IScreensController screensController, IViewPresenter viewPresenter)
        {
            SetPeriodicFetchState();
        }

        private bool ShouldFetchInCurrentScreen()
        {
            return _screensManager != null && (_screensManager.GetCurrentScreenType() & ScreenType.Map) != 0;
        }

        public IGameEventWithResources GetGameEvent(string uid)
        {
            return FindGameEventByUid(uid);
        }

        public IEnumerable<IGameEventWithResources> GetGameEventsWithResource(string resourceId)
        {
            var gameEventsWithResource = new List<IGameEventWithResources>();

            foreach (var gameEvent in _eventsDict.Values)
            {
                if (gameEvent != null && gameEvent.EventResourceId == resourceId)
                {
                    gameEventsWithResource.Add(gameEvent);
                }
            }

            return gameEventsWithResource;
        }

        public List<BackgroundDownloadData> GetBundlesToPredownload()
        {
            List<BackgroundDownloadData> bundles = null;
            foreach (var gameEvent in _eventsList)
            {
                if (gameEvent.HasNoResources() || gameEvent.BundlesLoadLevelUid.IsNullOrEmpty() || _locationManager.GetLevelStage(gameEvent.BundlesLoadLevelUid) <= 0) continue;

                var locationUid = (gameEvent as SideMapGameEvent)?.LocationUid;
                var eventBundles = _gameEventResourceManager.GetBundleNamesForEvent(gameEvent.EventResourceId, locationUid);
                if (eventBundles == null || eventBundles.Count == 0) continue;

                bundles ??= new List<BackgroundDownloadData>();
                foreach (var bundle in eventBundles)
                {
                    if (bundle.IsNullOrEmpty()) continue;
                    bundles.Add(new BackgroundDownloadData { Priority = BackgroundDownloadPriority.Normal, Name = bundle });
                }
            }

            return bundles;
        }

        public static bool IsValidScore(string scoreId, out GameEventBase gameEvent)
        {
            gameEvent = null;
            if (_instance == null)
            {
                BDebug.LogError(LogCat.General, "GameEventManager isn't initialized yet");
                return false;
            }

            return _instance.IsValidScore(scoreId, out gameEvent);
        }

        bool IGameEventManager.IsValidScore(string scoreId, out GameEventBase gameEvent)
        {
            var gameplayType = InventoryItems.GetGameplayTypeByScoreUid(scoreId);
            gameEvent = GetHighestPriorityEvent(ev => ev.GameplayType == gameplayType && ev.Status is GameEventStatus.Active or GameEventStatus.Accomplished);

            if (gameplayType != GameEventGameplayType.None && gameEvent == null)
            {
                BDebug.LogFormat(LogCat.General, "Game event for score {0} is not active", scoreId);
                return false;
            }

            return true;
        }
    }
}